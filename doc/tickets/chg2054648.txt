Short description:
Deploy Workforce Planning Model v1.1.5 to Prod

Description:
    This version includes the following changes:
        * ccp.model_preproc_input and ccp.model_preproc_input_emp download queries changed to 
            relax the DOE and birth_dt condition, apply a valid until date for district VA, and
            increase accuracy of grace_per 
        * modified get-forecast-employee service to (a) fix a memory leak, (b) supply default 
            doe_dt and birth_dt if the ccp.model_preproc_input_emp record is missing, 
            (c) return co_tenure and en_tenure, and (d) fix a socket timeout error ocassionally
            seen when switching to a new dataset
        * reduced memory usage in SAP downloader
        * eliminated image tag registration requirement for job scheduling
        * added a ccp.hybrid_result_export table to hold the results of the Hybrid export and modified
            hybrid process to populate it.
        * modified model controller to populate Cox model Feature Selection run ID's in
            ccp.model_subrun
        * model controller now runs hybrid process automatically
        * fixed a bug that was causing an exception upon closing Teradata connections
        * or-model-postprocessor was changed as follows:
            Hire-group  
                replace 14 employees mid July and early August 2023 ATLANTA - CHARLOTTE SOUTH records with ATLANTA - GREENVILLE SOUT.  
                3 final hire_grp_desc around Atlanta: ATLANTA - G<PERSON>ENVILLE SOUT ,   ATLANTA - SOUTH END ,   ATLANTA - YARD AND NORTH  
                Eliminate more employees (entire history) 
                Tenure never grows since 01-01-2014 (non-CT only, CT normal that attrit before gaining tenure) 1003185 1001826 1001831 roughly 197 most are from beginning of 2014 

            Tenure enhancement tenure.docx 
                Relax on having BIRTH_DT when missing from RRB record that will simply calculating from cum creditable month of service less 0 tenure records 
                Fixed a few employees final tenure should come from forward calculation. (a few employees a few months. (sample emp_nbr 1011951) 
                Requested more employees RRB records. Before we provided emp_nbr list on May 2nd, 2023. However employees were Yard master at that time were not in that list, but 
                    July 2023 emp_nbr 969227 switched from YA to EN. This version of data we have full RRB records  
                Added CO_tenure and EN_tenure 

Justification: 
See Description.

Implementation:
    OR Group:
    =========
        Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

        In the text box, enter the latest tags of the following: 
            or-ccp:v1.1.5
            or-ccp-model-preprocessor:v1.1.3
            or-jobmgr:v1.1.1
            or-ccp-xgboost-model:v1.1.3
            or-ccp-cox-model:v1.1.3
            or-ccp-logreg-model:v1.1.3
        Then click Submit.

    DB2 Group:
    ==========
    Please apply the following DDL to SCPMPROD:

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.22

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    create_hybrid_result_export.sql

    [PATH]
    /ddl/db2/ccp/indexes

    [FILELIST]
    create_hybrid_result_export_idx1.sql

    [PATH]
    /ddl/db2/ccp/alters

    [FILELIST]
    alter_model_subrun_1.sql

    [PATH]
    /ddl/db2/ccp/reorgs

    [FILELIST]
    reorg_model_subrun.sql
    reorg_hybrid_result_export.sql

    [PATH]
    /ddl/db2/ccp/permissions

    [FILELIST]
    grant.sql

Risk impact and analysis:
    Risks to collocated applications:
        This application runs on the OR-dedicated namespace in the Openshift (OCP4) environment.  Server
        names are of the form "ocp4-tskm4-or-*.atldc.nscorp.com".  These servers are dedicated exclusively
        to OR applications, but multiple applications will share the same virtual servers and there is
        no way of knowing specifically which applications are collocated with which others.  The 
        Openshift infrastructure imposes hard limits on CPU, memory, and disc usage that prevents 
        collocated applications from interfering with one another as might happen with a traditional, 
        non-containerized deployment model.
        
    Risks to downstream applications:
        There are no applications downstream of WPM.

    Operational impact:
        The application will be down for approximately 15 minutes for the upgrade.  If the change 
        were implemented and then seen to cause problems, then predictive and simulation model runs 
        would not work until the backout procedures were completed.  Backout will take approximately 
        15 minutes.

Backout:
    OR Group:
    =========
        Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

        In the text box, enter the latest tags of the following: 
            or-ccp:v1.1.4
            or-ccp-model-preprocessor:v1.1.2
            or-jobmgr:v1.1.0
            or-ccp-xgboost-model:v1.1.2
            or-ccp-cox-model:v1.1.2
            or-ccp-logreg-model:v1.1.2
        Then click Submit.

    DB2 Group:
    ==========
    Please apply the following DDL to SCPMPROD:

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.23

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    drop_hybrid_result_export.sql

    [PATH]
    /ddl/db2/ccp/alters

    [FILELIST]
    alter_model_subrun_1_backout.sql

    [PATH]
    /ddl/db2/ccp/reorgs

    [FILELIST]
    reorg_model_subrun.sql

Test plan:
    After deployment, we will kick off the predictive models and simulation model and verify that that 
    run to completion.
