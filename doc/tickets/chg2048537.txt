CHG2048537

Description: Deploy CrewMAX UI v1.0.8.2 to Prod

Justification:
    This version includes the following bug fixes:

    1. Extraboard editor blank screen issue

    Clicking off Extra board during middle of creation causes blank screen
    Clicking on another left tab folder causes blank screen
    In the middle of extraboard addition, if user leaves the edit screen without saving the changes, screen goes white 

    2. Extraboard editor save warning issue

    Clicking on another extraboard without saving does not warn user to save or discard the changes.
    Clicking outside extraboard without saving does not warn user to save or discard the changes.  .  

    3. Scenario Report: Export and Export Details Buttons Inconsistent Location for the simulation reports.

Justification: 
    See Description.

Risk impact and analysis:
    This is a low-risk change.

Implementation:
    Log in to the OKD Prod Jenkins page and deploy or-ccp-ui:v1.0.8.2 to Prod

Backout:
    Log in to the OKD Prod Jenkins page and deploy or-ccp-ui:v1.0.7.7 to Prod

Test plan:
    The reports affected by the 3 bugs fixed in this version will be evaluated after the application is deployed
    to verify that the issues are corrected.
