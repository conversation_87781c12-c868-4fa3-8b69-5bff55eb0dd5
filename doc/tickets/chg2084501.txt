Short description:
Deploy latest Workforce Planning Model application to Prod.

Description:
This change moves the Separation Model tables from the ccp schema to the ccp_sep schema, and fixes a transaction
sizing bug occurring during the monthly SAP download.

Justification:
See Description.

Implementation:
Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

In the text box, enter the latest tags of the following applications: 
    or-ccp-sep
    or-ccp-model-preprocessor
    or-ccp-logreg-model
    or-ccp-xgboost-model
    or-ccp-cox-model

Then click Submit.

Risk impact and analysis:
    Risks to collocated applications:
        This application runs on the OR-dedicated namespace in the Openshift (OCP4) environment.  Server
        names are of the form "ocp4-tskm4-or-*.atldc.nscorp.com".  These servers are dedicated exclusively
        to OR applications, but multiple applications will share the same virtual servers and there is
        no way of knowing specifically which applications are collocated with which others.  The 
        Openshift infrastructure imposes hard limits on CPU, memory, and disc usage that prevents 
        collocated applications from interfering with one another as might happen with a traditional, 
        non-containerized deployment model.
        
    Risks to downstream applications:
        There are no applications downstream of WPM.

    Operational impact:
        The application will be down for approximately 15 minutes for the upgrade.  If the change 
        were implemented and then seen to cause problems, then the application would be down until
        the backout procedures were completed.  Backout will take approximately 15 minutes.


Backout:

    Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

    In the text box, enter the following: 

    or-ccp-sep:v1.1.10.4
    or-ccp-model-preprocessor:v1.1.10.1
    or-ccp-logreg-model:v1.1.10.3
    or-ccp-xgboost-model:v1.1.10.2
    or-ccp-cox-model:v1.1.10.3

    Then click Submit.
    
Test plan:
    After deployment, we will run the emp_* population job and one of the separation models to make
    sure the deployment was successful.
