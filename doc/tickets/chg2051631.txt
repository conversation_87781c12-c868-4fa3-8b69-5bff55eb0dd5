Execution time:
    begin: 2023-09-15 @ 09:00
    end:   2023-09-15 @ 17:00

Short description: Promote Workforce Planning Model v1.1.1 to Production

Description: 
    This deployment will apply the latest Workforce Planning code to Production.
    It is a preliminary deployment so that certain functionality can be validated
    prior to the full production rollout later this month.

Justification: see Description

Implementation plan: 
    OR Group:
    ==========
    Deploy the following applications to Prod: 
        or-ccp
        or-ccp-ui-rest
        or-ccp-ui
        or-ccp-model-preprocessor
        or-ccp-cox-model
        or-ccp-xgboost-model
        or-ccp-logreg-model
        or-ccp-jobmgr

    DB2 Group:
    ===========
    Please charge to project "2210776 - Workforce Planning Model".  

    Apply the following changes to SCPMPROD:

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.18

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    drop_emp_action.sql
	drop_emp_credit_ser.sql
	drop_emp_curr_job.sql
	drop_emp_job_hist.sql
	drop_emp_payroll.sql
	drop_emp_rrb.sql
	drop_emp_status.sql
	create_actual_turnover.sql
	create_emp_action.sql
	create_emp_credit_ser.sql
	create_emp_curr_job.sql
	create_emp_dataset.sql
	create_emp_file.sql
	create_emp_job_hist.sql
	create_emp_payroll.sql
	create_emp_rrb.sql
	create_emp_status.sql
	create_hybrid_result.sql
	create_model_config.sql
	create_model_cox_fs_run.sql
	create_model_cox_fs_var.sql
	create_model_input.sql
	create_model_input_curr_emp.sql
	create_model_input_dataset.sql
	create_model_preproc_input.sql
	create_model_preproc_input_emp.sql
	create_model_result.sql
	create_model_run.sql
	create_model_subrun.sql
	create_target_headcount_by_hire_group.sql
	create_model_input_file.sql	
    create_board_hire_group_name_mapping.sql

    [PATH]
    /ddl/db2/ccp/indexes

    [FILELIST]
    create_actual_turnover_idx1.sql
	create_emp_action_idx1.sql
	create_emp_credit_ser_idx1.sql
	create_emp_curr_job_idx1.sql
	create_emp_file_idx1.sql
	create_emp_job_hist_idx1.sql
	create_emp_payroll_idx1.sql
	create_emp_rrb_idx1.sql
	create_emp_status_idx1.sql
	create_hybrid_result_idx1.sql
	create_model_cox_fs_run_idx1.sql
	create_model_input_curr_emp_idx1.sql
	create_model_input_dataset_idx1.sql
	create_model_input_idx1.sql
	create_model_preproc_input_idx1.sql
	create_model_preproc_input_emp_idx1.sql
	create_model_result_idx1.sql
	create_model_run_idx1.sql
	create_model_subrun_idx1.sql
	create_target_headcount_by_hire_group_idx1.sql
	create_model_input_file_idx1.sql
    create_board_hire_group_name_mapping_idx1.sql

    [PATH]
    /ddl/db2/ccp/permissions

    [FILELIST]
    grant.sql

Backout plan:
    OR Group:
    ===========
    Deploy the previous tags of the following:
        or-ccp
        or-ccp-ui-rest
        or-ccp-ui
        or-ccp-jobmgr

    DB2 Group:
    ===========

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.18

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    drop_actual_turnover.sql
	drop_emp_action.sql
	drop_emp_credit_ser.sql
	drop_emp_curr_job.sql
	drop_emp_dataset.sql
	drop_emp_file.sql
	drop_emp_job_hist.sql
	drop_emp_payroll.sql
	drop_emp_rrb.sql
	drop_emp_status.sql
	drop_hybrid_result.sql
	drop_model_config.sql
	drop_model_cox_fs_run.sql
	drop_model_cox_fs_var.sql
	drop_model_input.sql
	drop_model_input_curr_emp.sql
	drop_model_input_dataset.sql
	drop_model_preproc_input.sql
	drop_model_preproc_input_emp.sql
	drop_model_result.sql
	drop_model_run.sql
	drop_model_subrun.sql
	drop_target_headcount_by_hire_group.sql
	drop_model_input_file.sql	
    drop_board_hire_group_name_mapping.sql

Risk impact and analysis: This is a low-risk deployment because the application is not in
    use by users yet.

Test plan:
   After deployment, we will manually kick off TD downloads, SAP download, model preprocessing, 
   and model runs.
