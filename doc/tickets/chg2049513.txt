Short description:
Deploy CrewMAX v1.0.8 to Production

Description:
Crew Profiles - If a new crew profile is added, but not saved, and then removed, then saved, a System Error CCP-P010-500 appears.

Hub Editor - If a new hub is added, but not saved, and then removed, then saved, the profile is not removed and is actually saved to the database.

Pool Setup -
If a new pool is added and not saved, clicking Add new pool brings up the Add Pool popup without prompting the user to save the one they have just created; if second pool is created this way, the first will be overwritten. User should be prompted to save BEFORE the Add Pool popup can be brought up again, if there are any unsaved changes.
The Add button becomes active after entering a Turn ID, but without requiring an Initial Home_Away Terminal selection.  If no selection is made, the value defaults to 0 when creating a new turn.  However, if an existing turn is edited and the Select Option selection is chosen, the user is able to save this selection and it actually appears in the table as Select Option.  Attempting to save that change results in a System Error CCP-P006-500. 

Justification:
See Description.

Implementation:

    Deploy these applications to OKD Prod:
        or-ccp:v1.0.8
        or-ccp-ui-rest:v1.0.8
        or-ccp-ui:v1.0.8

Risk and impact analysis:
This change consists only of bug fixes and poses minor risk.

Backout:
    Deploy these applications to OKD Prod:
        or-ccp:v1.0.7
        or-ccp-ui-rest:v1.0.7
        or-ccp-ui:v1.0.7

Test plan:
The application will automatically run a self-test every half hour to check the backend services.  The UI
tests will be done manually by the OR group after deployment.
