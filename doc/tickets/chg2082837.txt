Short description:
Deploy latest Workforce Planning Model application to Prod.

Description:
This change will deploy the latest Workforce Planning Model code to Prod.  This change includes the new Lookahead Model as well as the Separation Model UI.  It also includes various bug fixes.

Justification:
See Description.

Implementation:
OR Group:
=========
    Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

    In the text box, enter the latest tags of the following applications: 
        or-ccp
        or-ccp-sep
        or-ccp-lam
        or-ccp-cttp
        or-ccp-crewsim
        or-ccp-ui
        or-ccp-ui-rest
        or-ccp-model-preprocessor
        or-ccp-logreg-model
        or-ccp-xgboost-model
        or-ccp-cox-model

    Then click Submit.

    Run the following commands to create the required secrets, replacing $TOKEN with the OCP4 Prod token:

        oc login --token=$TOKEN --server=https://api.ocp4.nscorp.com:6443
        oc create secret generic or-ccp-sep-secret --from-env-file=or-ccp-sep-prod-secret.properties
        oc create secret generic or-ccp-crewsim-secret --from-env-file=or-ccp-crewsim-prod-secret.properties
        oc create secret generic or-ccp-cttp-secret --from-env-file=or-ccp-cttp-prod-secret.properties
        oc create secret generic or-ccp-lam-secret --from-env-file=or-ccp-lam-prod-secret.properties
        oc edit secret/or-ccp-secret
        (delete the ENC_PASSPHRASE and SAP_FTP_PASSWORD properties and save changes)
        oc create secret generic or-ccp-cttp-secret-prvkey --from-file=ccp_enc_private_key.pgp
        oc create secret generic or-ccp-sep-secret-prvkey --from-file=ccp_enc_private_key.pgp

DB2 Group:
==========

Please apply the following scripts to SCPMPROD:

Script #1 of 2:
===============

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.28

    [PATH]
    /ddl/db2/ccp_lam/tables

    [FILELIST]
    create_ct_trainee.sql
    create_ct_trainee_rec.sql
    create_dataset.sql
    create_default_hiregroup_detail.sql
    create_file.sql
    create_go_team.sql
    create_hiregroup_detail.sql
    create_hiregroup_map.sql
    create_run.sql
    create_run_summary_report.sql
    create_te_headcount.sql
    create_temp_transfer.sql

    [PATH]
    /ddl/db2/ccp_lam/indexes

    [FILELIST]
    create_ct_trainee_idx1.sql
    create_ct_trainee_rec_idx1.sql
    create_default_hiregroup_detail_idx1.sql
    create_file_idx1.sql
    create_file_idx2.sql
    create_go_team_idx1.sql
    create_hiregroup_detail_idx1.sql
    create_hiregroup_map_idx1.sql
    create_run_idx1.sql
    create_run_summary_report_idx1.sql
    create_te_headcount_idx1.sql
    create_temp_transfer_idx1.sql

    [PATH]
    /ddl/db2/ccp_lam/views

    [FILELIST]
    create_dataset_detail_view.sql

    [PATH]
    /ddl/db2/ccp_lam/permissions

    [FILELIST]
    grant.sql

Script #2 of 2:
===============

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.29

    [PATH]
    /ddl/db2/ccp_lam/views

    [FILELIST]
    create_dataset_detail_view.sql

    [PATH]
    /ddl/db2/ccp_lam/permissions

    [FILELIST]
    grant.sql

Risk impact and analysis:
    Risks to collocated applications:
        This application runs on the OR-dedicated namespace in the Openshift (OCP4) environment.  Server
        names are of the form "ocp4-tskm4-or-*.atldc.nscorp.com".  These servers are dedicated exclusively
        to OR applications, but multiple applications will share the same virtual servers and there is
        no way of knowing specifically which applications are collocated with which others.  The 
        Openshift infrastructure imposes hard limits on CPU, memory, and disc usage that prevents 
        collocated applications from interfering with one another as might happen with a traditional, 
        non-containerized deployment model.
        
    Risks to downstream applications:
        There are no applications downstream of WPM.

    Operational impact:
        The application will be down for approximately 15 minutes for the upgrade.  If the change 
        were implemented and then seen to cause problems, then the application would be down until
        the backout procedures were completed.  Backout will take approximately 15 minutes.


Backout:

    OR Group:
    =========
        Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build
    
        In the text box, enter the following: 

        or-ccp:v1.1.8.1
        or-ccp-ui:v1.1.8.2
        or-ccp-ui-rest:v1.1.8.1
        or-ccp-logreg-model:v1.1.7.1
        or-ccp-xgboost-model:v1.1.7.1
        or-ccp-cox-model:v1.1.7.2
        or-ccp-model-preprocessor:v1.1.7.1
    
        Then click Submit.
    
        Run the following commands, replacing $TOKEN with the OCP4 Prod token:

            oc login --token=$TOKEN --server=https://api.ocp4.nscorp.com:6443
            oc edit secret/or-ccp-secret
            (restore the ENC_PASSPHRASE and SAP_FTP_PASSWORD properties and save changes)

    DB2 Group:
    ==========
    
    Please apply the following script to SCPMPROD:
    
    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.29

    [PATH]
    /ddl/db2/ccp_lam/tables

    [FILELIST]
    drop_ct_trainee.sql
    drop_ct_trainee_rec.sql
    drop_dataset.sql
    drop_default_hiregroup_detail.sql
    drop_file.sql
    drop_go_team.sql
    drop_hiregroup_detail.sql
    drop_hiregroup_map.sql
    drop_run.sql
    drop_run_summary_report.sql
    drop_te_headcount.sql
    drop_temp_transfer.sql

    [PATH]
    /ddl/db2/ccp_lam/views

    [FILELIST]
    drop_dataset_detail_view.sql

Test plan:
    After deployment, we will log in to the application and verify that the Lookahead Model 
    and Separation Model UI modules are working.  We will also regression test the other modules.
