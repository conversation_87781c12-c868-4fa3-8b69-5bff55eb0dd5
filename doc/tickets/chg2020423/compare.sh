rm -rf /tmp/diffs
mkdir -p /tmp/diffs/old /tmp/diffs/new
comp() {
	OLD=/tmp/diffs/old/$1.txt
	NEW=/tmp/diffs/new/$1.txt
	db2 describe table ccp.$1 |sort >$OLD
	db2 describe table dgccp.$1 |sort >$NEW
}
db2 connect to SCPMTEST user pach8
comp batch_run
comp board_stats
comp board_summary
comp board_train_starts
comp carded_day
comp carded_pool
comp control_param
comp crew_profile
comp crewpro_group
comp crewpro_trn_strt
comp exb_pool_set_up
comp exb_turn
comp extraboard
comp id_pool
comp job
comp monitor_log
comp opd_scenario
comp opd_train
comp plan
comp pool_rotation_history
comp pool_set_up
comp pool
comp scenario_cfg
comp scenario
comp scen_hub
comp scen_line_seg
comp search_pool
comp self_sustaining
comp simulation_train_output
comp study
comp study_trn_type
comp tie_up_exception
comp train_hub
comp train
comp trn_delay_stats
comp trn_stats_by_pool
comp tue_subrule
comp turn
comp turn_utilization
comp user
comp work_rest_prof_rotn
comp work_rest_prof
comp work_rest_prof_tgrp
