Please apply the following scripts to SCPMQA:

[OUTDIR]
SCPMQA

[URL]
https://github.com

[PROJECT]
Norfolk-Southern/or-ccp

gittag=v0.26

[PATH]
/ddl/db2/ccp/tables

[FILELIST]
create_actual_turnover.sql
create_model_subrun.sql
drop_board_hire_group_name_mapping.sql
create_model_result.sql
create_hybrid_result.sql

[PATH]
/ddl/db2/ccp/indexes

[FILELIST]
create_actual_turnover_idx1.sql
create_model_subrun_idx1.sql
create_model_result_idx1.sql
create_hybrid_result_idx1.sql

[PATH]
/ddl/db2/ccp/alters

[FILELIST]
alter_model_input_dataset_1.sql

[PATH]
/ddl/db2/ccp/reorgs

[FILELIST]
reorg_model_input_dataset.sql

[PATH]
/ddl/db2/ccp/permissions

[FILELIST]
grant.sql

