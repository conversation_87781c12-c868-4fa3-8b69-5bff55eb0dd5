Please execute the change described in CHG2020423 against the SCPMPROD database.  The implementation and backout sections are reproduced below:

IMPLEMENTATION:

[OUTDIR]
SCPMPROD

[URL]
https://github.com

[PROJECT]
Norfolk-Southern/or-ccp

gittag=v0.09

[PATH]
/ddl/db2/ccp/tables

[FILELIST]
create_batch_run.sql
create_board_stats.sql
create_board_summary.sql
create_board_train_starts.sql
create_carded_day.sql
create_carded_pool.sql
create_control_param.sql
create_crew_profile.sql
create_crewpro_group.sql
create_crewpro_trn_strt.sql
create_exb_pool_set_up.sql
create_exb_turn.sql
create_extraboard.sql
create_id_pool.sql
create_job.sql
create_monitor_log.sql
create_opd_scenario.sql
create_opd_train.sql
create_plan.sql
create_pool.sql
create_pool_rotation_history.sql
create_pool_set_up.sql
create_scen_hub.sql
create_scen_line_seg.sql
create_scenario.sql
create_scenario_cfg.sql
create_search_pool.sql
create_self_sustaining.sql
create_simulation_train_output.sql
create_study.sql
create_study_trn_type.sql
create_tie_up_exception.sql
create_train.sql
create_train_hub.sql
create_trn_delay_stats.sql
create_trn_stats_by_pool.sql
create_tue_subrule.sql
create_turn.sql
create_turn_utilization.sql
create_user.sql
create_work_rest_prof.sql
create_work_rest_prof_rotn.sql
create_work_rest_prof_tgrp.sql

[PATH]
/ddl/db2/ccp/indexes

[FILELIST]
create_bridx1.sql
create_bridx2.sql
create_bsidx1.sql
create_cadidx1.sql
create_capidx1.sql
create_cpaidx1.sql
create_cpgidx1.sql
create_cpgidx2.sql
create_cpgidx3.sql
create_cpidx1.sql
create_cptsidx1.sql
create_exbidx1.sql
create_exbpsuidx1.sql
create_idpidx1.sql
create_jobidx1.sql
create_jobidx2.sql
create_mlidx1.sql
create_opdtidx1.sql
create_poolidx2.sql
create_prhidx1.sql
create_psuidx1.sql
create_scidx1.sql
create_shubidx1.sql
create_spidx1.sql
create_stidx1.sql
create_stoidx1.sql
create_sttidx1.sql
create_tdsidx1.sql
create_thidx1.sql
create_trnidx1.sql
create_trnidx2.sql
create_tsidx1.sql
create_tsridx1.sql
create_tueidx1.sql
create_turnidx1.sql
create_turnutidx1.sql
create_wrpidx1.sql
create_wrprotnidx1.sql
create_wrptgrpidx1.sql

[PATH]
/ddl/db2/ccp/permissions

[FILELIST]
grant.sql


=================================================

BACKOUT:

[OUTDIR]
SCPMPROD

[URL]
https://github.com

[PROJECT]
Norfolk-Southern/or-ccp

gittag=v0.09

[PATH]
/ddl/db2/ccp/tables

[FILELIST]
drop_batch_run.sql
drop_board_stats.sql
drop_board_summary.sql
drop_board_train_starts.sql
drop_carded_day.sql
drop_carded_pool.sql
drop_control_param.sql
drop_crew_profile.sql
drop_crewpro_group.sql
drop_crewpro_trn_strt.sql
drop_exb_pool_set_up.sql
drop_exb_turn.sql
drop_extraboard.sql
drop_id_pool.sql
drop_job.sql
drop_monitor_log.sql
drop_opd_scenario.sql
drop_opd_train.sql
drop_plan.sql
drop_pool.sql
drop_pool_rotation_history.sql
drop_pool_set_up.sql
drop_scen_hub.sql
drop_scen_line_seg.sql
drop_scenario.sql
drop_scenario_cfg.sql
drop_search_pool.sql
drop_self_sustaining.sql
drop_simulation_train_output.sql
drop_study.sql
drop_study_trn_type.sql
drop_tie_up_exception.sql
drop_train.sql
drop_train_hub.sql
drop_trn_delay_stats.sql
drop_trn_stats_by_pool.sql
drop_tue_subrule.sql
drop_turn.sql
drop_turn_utilization.sql
drop_user.sql
drop_work_rest_prof.sql
drop_work_rest_prof_rotn.sql
drop_work_rest_prof_tgrp.sql

