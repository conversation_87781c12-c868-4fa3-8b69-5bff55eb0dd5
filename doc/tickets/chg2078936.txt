Short description:
Upgrade esb.nscorp.com certificate for all OR OCP applications.

Description:
This change will upgrade the esb.nscorp.com certificate for the following applications in Production:

    or-sfpm1-5-api
    or-iteams-webservice
    or-yardmax-core
    or-ccp-ui-rest
    or-opd-ws-tripplan
    or-iteams-forecast-provider
    or-opd-wpm-ws
    or-opd3-phmsa-api
    or-opd3-plan-merger-ui-rest
    or-opd-ws-npo
    or-opd-yltp-ui-rest

Justification: 
The Production ESB certificate is expiring on 2024-11-23.  Applications not upgraded before expiration
will not be able to authenticate their users.

Implementation:
Use 'oc login' to connect to OCP4 Production.
Run 'oc edit cm/or-esb-nscorp-com-crt-configmap'.  Inside the editor, paste the new certificate, save and exit.

Log in to ArgoCD and trigger a refresh of all the above applications. For any applications that ArgoCD fails to refresh, make a nonfunctional change to the or-ocp-config deployment yaml, push the change to Github, and then refresh in ArgoCD.

Risk impact and analysis:
Operational impact: The application will be down for approximately 30 minutes for the upgrade.  If the change 
were implemented and then seen to cause problems, then user login would not work until the
backout procedures were completed.  Backout will take approximately 30 minutes.

Backout:
Use 'oc login' to connect to OCP4 Production.
Run 'oc edit cm/or-esb-nscorp-com-crt-configmap'.  Inside the editor, paste the old certificate, save and exit.

Log in to ArgoCD and trigger a refresh of all the above applications. For any applications that ArgoCD fails to refresh, make a nonfunctional change to the or-ocp-config deployment yaml, push the change to Github, and then refresh in ArgoCD.

Test plan:
After the change is implemented, we will ask all affected groups to test their applications to 
verify users can log in.
