Short description:
Deploy Workforce Planning Model v1.1.4 to Prod

Description:
This version upgrades the Teradata driver to v20.  Workforce Planning Model depends on Teradata
for a number of queries and all Teradata clients are required to be upgraded to the V20 driver 
no later than 2023-10-21.

Justification: 
TD driver is required to be upgraded by 2023-10-21.

Implementation plan:
Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

In the text box, enter the latest "or-ccp:v1.1.4" tag and click Submit.

Tail the log of deployment "or-ccp" to verify that the application comes up properly.

Risk impact and analysis:
Risks to collocated applications:
    This application runs on the OR-dedicated namespace in the Openshift (OCP4) environment.  Server
    names are of the form "ocp4-tskm4-or-*.atldc.nscorp.com".  These servers are dedicated exclusively
    to OR applications, but multiple applications will share the same virtual servers and there is
    no way of knowing specifically which applications are collocated with which others.  The 
    Openshift infrastructure imposes hard limits on CPU, memory, and disc usage that prevents 
    collocated applications from interfering with one another as might happen with a traditional, 
    non-containerized deployment model.
    
Risks to downstream applications:
    There are no applications downstream of WPM.

Operational impact:
    The application will be down for approximately 15 minutes for the upgrade.  If the change 
    were implemented and then seen to cause problems, then study creation as well as certain
    batch jobs would not be viable until the backout procedures were completed.  Backout will 
    take approximately 15 minutes.

Backout plan:
Navigate to here: https://jenkins-nsjenkins.apps.ocp4np.nscorp.com/job/or/job/prod/job/deploy/build

In the text box, enter "or-ccp:v1.1.3.4" and click Submit.

Tail the log of deployment "or-ccp" to verify that the application comes up properly.

The backout process should take less than 15 minutes once initiated.

Test plan:
After deployment, we will create studies that are dependent on Teradata queries and
verify that the studies were created successfully and have proper data derived from the
Teradata queries.
