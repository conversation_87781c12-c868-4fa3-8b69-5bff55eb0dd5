Short description: Promote CrewMAX v1.0.7 to Production

Description: 
    This version introduces the following changes:

        Hub Editor
            Add/Edit/Remove/List hubs
        Pools/Extraboards tabs
            Prevent User Deleting Hub if there are associated extraboards or pools
        Extraboard Editor
            Add/Edit/Remove/List/Export/ExportDetails
            Turns/SupportedPools/Mark-off/Other
        Pool Setup Editor
            Turns tab (Add/Edit/Remove)
            Display existing hubs in details section as editable dropdown.
            Terminals/Mark-off/PoolSizeRange/Other tabs editing functionality
            Restrict duplicate pools
        Crew Profile Editor
            Making Crew profile UI page editable
            Making Tie-up Exception Rules Noneditable
         Make crew profile UI page editable     
             Remove 'Assignment Profiles' editor under the 'Crew Plans'
             User Session Timeout
             Crew Plans Menu Sort in Alphabetical order
             Tenable scan issues fix
             Expand 'Train Viewer' - Add additional fields passed by OPD in the details section of train viewer (under study)

Justification: see Description

Implementation plan: 
    OR Group:
    ==========
    Deploy the following tags to Prod: or-ccp:v1.0.7.5, or-ccp-ui-rest:v1.0.7.2, or-ccp-ui:v1.0.7.7

    DB2 Group:
    ===========
    Please charge to project "2009304 - Capacity Planning Tool Suite".  

    Apply the following changes to SCPMPROD (note these were previously applied to SCPMQA in REQ2129323):

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.14

    [PATH]
    /ddl/db2/ccp/views

    [FILELIST]
    drop_extraboard_view.sql
    drop_exb_turn_view.sql
    drop_pool_set_up_view.sql
    drop_turn_view.sql
    drop_crew_starts_by_hire_group_view.sql
    drop_target_headcount_by_hire_group_view.sql

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    drop_batch_run.sql
    drop_board_hire_group_mapping.sql
    drop_board_craft_hire_group_mapping.sql
    drop_board_stats.sql
    drop_board_summary.sql
    drop_board_train_starts.sql
    drop_carded_day.sql
    drop_carded_pool.sql
    drop_control_param.sql
    drop_crew_profile.sql
    drop_crewpro_group.sql
    drop_crewpro_trn_strt.sql
    drop_emp_action.sql
    drop_emp_credit_ser.sql
    drop_emp_curr_job.sql
    drop_emp_job_hist.sql
    drop_emp_payroll.sql
    drop_emp_rrb.sql
    drop_emp_status.sql
    drop_exb_pool_set_up.sql
    drop_exb_turn.sql
    drop_extraboard.sql
    drop_hub.sql
    drop_id_pool.sql
    drop_job.sql
    drop_monitor_log.sql
    drop_opd_plan.sql
    drop_opd_scenario.sql
    drop_opd_train.sql
    drop_plan.sql
    drop_pool.sql
    drop_pool_rotation_history.sql
    drop_pool_set_up.sql
    drop_scen_hub.sql
    drop_scen_line_seg.sql
    drop_scen_train.sql
    drop_scenario.sql
    drop_scenario_cfg.sql
    drop_search_pool.sql
    drop_self_sustaining.sql
    drop_simulation_train_output.sql
    drop_study.sql
    drop_study_trn_type.sql
    drop_tie_up_exception.sql
    drop_train.sql
    drop_train_hub.sql
    drop_trn_delay_stats.sql
    drop_trn_stats_by_pool.sql
    drop_tue_subrule.sql
    drop_turn.sql
    drop_turn_utilization.sql
    drop_upside.sql
    drop_upside_train.sql
    drop_user.sql
    drop_work_rest_prof.sql
    drop_work_rest_prof_rotn.sql
    drop_work_rest_prof_tgrp.sql
    create_batch_run.sql
    create_board_hire_group_mapping.sql
    create_board_craft_hire_group_mapping.sql
    create_board_stats.sql
    create_board_summary.sql
    create_board_train_starts.sql
    create_carded_day.sql
    create_carded_pool.sql
    create_control_param.sql
    create_crew_profile.sql
    create_crewpro_group.sql
    create_crewpro_trn_strt.sql
    create_emp_action.sql
    create_emp_credit_ser.sql
    create_emp_curr_job.sql
    create_emp_job_hist.sql
    create_emp_payroll.sql
    create_emp_rrb.sql
    create_emp_status.sql
    create_exb_pool_set_up.sql
    create_exb_turn.sql
    create_extraboard.sql
    create_hub.sql
    create_id_pool.sql
    create_job.sql
    create_monitor_log.sql
    create_opd_plan.sql
    create_opd_scenario.sql
    create_opd_train.sql
    create_plan.sql
    create_pool.sql
    create_pool_rotation_history.sql
    create_pool_set_up.sql
    create_scen_hub.sql
    create_scen_line_seg.sql
    create_scen_train.sql
    create_scenario.sql
    create_scenario_cfg.sql
    create_search_pool.sql
    create_self_sustaining.sql
    create_simulation_train_output.sql
    create_study.sql
    create_study_trn_type.sql
    create_tie_up_exception.sql
    create_train.sql
    create_train_hub.sql
    create_trn_delay_stats.sql
    create_trn_stats_by_pool.sql
    create_tue_subrule.sql
    create_turn.sql
    create_turn_utilization.sql
    create_upside.sql
    create_upside_train.sql
    create_user.sql
    create_work_rest_prof.sql
    create_work_rest_prof_rotn.sql
    create_work_rest_prof_tgrp.sql

    [PATH]
    /ddl/db2/ccp/indexes

    [FILELIST]
    create_thidx1.sql
    create_opdtidx1.sql
    create_bsidx1.sql
    create_mlidx1.sql
    create_wrprotnidx1.sql
    create_cpaidx1.sql
    create_tdsidx1.sql
    create_sttidx1.sql
    create_capidx1.sql
    create_prhidx1.sql
    create_tsidx1.sql
    create_trnidx2.sql
    create_cpgidx1.sql
    create_shubidx1.sql
    create_sctridx1.sql
    create_cadidx1.sql
    create_cptsidx1.sql
    create_wrptgrpidx1.sql
    create_spidx1.sql
    create_idpidx1.sql
    create_jobidx1.sql
    create_opdtidx2.sql
    create_wrpidx1.sql
    create_scidx1.sql
    create_stoidx1.sql
    create_cpidx1.sql
    create_trnidx5.sql
    create_stidx1.sql
    create_trnidx3.sql
    create_cpgidx2.sql
    create_exbpsuidx1.sql
    create_tsridx1.sql
    create_bridx1.sql
    create_jobidx2.sql
    create_poolidx2.sql
    create_cpgidx3.sql
    create_trnidx1.sql
    create_turnutidx1.sql
    create_trnidx4.sql
    create_hubidx1.sql
    create_tueidx1.sql
    create_bridx2.sql
    create_sctridx2.sql
    create_turnidx1.sql
    create_psuidx1.sql

    [PATH]
    /ddl/db2/ccp/reorgs

    [FILELIST]
    reorg_batch_run.sql
    reorg_board_hire_group_mapping.sql
    reorg_board_craft_hire_group_mapping.sql
    reorg_board_stats.sql
    reorg_board_summary.sql
    reorg_board_train_starts.sql
    reorg_carded_day.sql
    reorg_carded_pool.sql
    reorg_control_param.sql
    reorg_crew_profile.sql
    reorg_crewpro_group.sql
    reorg_crewpro_trn_strt.sql
    reorg_emp_action.sql
    reorg_emp_credit_ser.sql
    reorg_emp_curr_job.sql
    reorg_emp_job_hist.sql
    reorg_emp_payroll.sql
    reorg_emp_rrb.sql
    reorg_emp_status.sql
    reorg_exb_pool_set_up.sql
    reorg_exb_turn.sql
    reorg_extraboard.sql
    reorg_hub.sql
    reorg_id_pool.sql
    reorg_job.sql
    reorg_monitor_log.sql
    reorg_opd_plan.sql
    reorg_opd_scenario.sql
    reorg_opd_train.sql
    reorg_plan.sql
    reorg_pool.sql
    reorg_pool_rotation_history.sql
    reorg_pool_set_up.sql
    reorg_scen_hub.sql
    reorg_scen_line_seg.sql
    reorg_scen_train.sql
    reorg_scenario.sql
    reorg_scenario_cfg.sql
    reorg_search_pool.sql
    reorg_self_sustaining.sql
    reorg_simulation_train_output.sql
    reorg_study.sql
    reorg_study_trn_type.sql
    reorg_tie_up_exception.sql
    reorg_train.sql
    reorg_train_hub.sql
    reorg_trn_delay_stats.sql
    reorg_trn_stats_by_pool.sql
    reorg_tue_subrule.sql
    reorg_turn.sql
    reorg_turn_utilization.sql
    reorg_upside.sql
    reorg_upside_train.sql
    reorg_user.sql
    reorg_work_rest_prof.sql
    reorg_work_rest_prof_rotn.sql
    reorg_work_rest_prof_tgrp.sql

    [PATH]
    /ddl/db2/ccp/views

    [FILELIST]
    create_turn_view.sql
    create_exb_turn_view.sql
    create_pool_set_up_view.sql
    create_extraboard_view.sql
    create_crew_starts_by_hire_group_view.sql
    create_target_headcount_by_hire_group_view.sql

    [PATH]
    /ddl/db2/ccp/permissions

    [FILELIST]
    grant.sql

Backout plan:
    OR Group:
    ===========
    Deploy the following tags to Prod: or-ccp:v1.0.6.1, or-ccp-ui-rest:v1.0.6.2, or-ccp-ui:v1.0.6.1

    DB2 Group:
    ===========

    [OUTDIR]
    SCPMPROD

    [URL]
    https://github.com

    [PROJECT]
    Norfolk-Southern/or-ccp

    gittag=v0.14

    [PATH]
    /ddl/db2/ccp/views

    [FILELIST]
    drop_extraboard_view.sql
    drop_exb_turn_view.sql
    drop_pool_set_up_view.sql
    drop_turn_view.sql
    drop_crew_starts_by_hire_group_view.sql
    drop_target_headcount_by_hire_group_view.sql

    [PATH]
    /ddl/db2/ccp/tables

    [FILELIST]
    drop_batch_run.sql
    drop_board_hire_group_mapping.sql
    drop_board_craft_hire_group_mapping.sql
    drop_board_stats.sql
    drop_board_summary.sql
    drop_board_train_starts.sql
    drop_carded_day.sql
    drop_carded_pool.sql
    drop_control_param.sql
    drop_crew_profile.sql
    drop_crewpro_group.sql
    drop_crewpro_trn_strt.sql
    drop_emp_action.sql
    drop_emp_credit_ser.sql
    drop_emp_curr_job.sql
    drop_emp_job_hist.sql
    drop_emp_payroll.sql
    drop_emp_rrb.sql
    drop_emp_status.sql
    drop_exb_pool_set_up.sql
    drop_exb_turn.sql
    drop_extraboard.sql
    drop_hub.sql
    drop_id_pool.sql
    drop_job.sql
    drop_monitor_log.sql
    drop_opd_plan.sql
    drop_opd_scenario.sql
    drop_opd_train.sql
    drop_plan.sql
    drop_pool.sql
    drop_pool_rotation_history.sql
    drop_pool_set_up.sql
    drop_scen_hub.sql
    drop_scen_line_seg.sql
    drop_scen_train.sql
    drop_scenario.sql
    drop_scenario_cfg.sql
    drop_search_pool.sql
    drop_self_sustaining.sql
    drop_simulation_train_output.sql
    drop_study.sql
    drop_study_trn_type.sql
    drop_tie_up_exception.sql
    drop_train.sql
    drop_train_hub.sql
    drop_trn_delay_stats.sql
    drop_trn_stats_by_pool.sql
    drop_tue_subrule.sql
    drop_turn.sql
    drop_turn_utilization.sql
    drop_upside.sql
    drop_upside_train.sql
    drop_user.sql
    drop_work_rest_prof.sql
    drop_work_rest_prof_rotn.sql
    drop_work_rest_prof_tgrp.sql

Test plan:
    The application performs a self-test at half hour intervals.  Also, manual tests will
    be done to the new reports are functional and that plan, study, and and scenario 
    management functions are working as expected.
