
Please apply the following DDL to SCPMQA:

[OUTDIR]
SCPMQA

[URL]
https://github.com

[PROJECT]
Norfolk-Southern/or-ccp

gittag=v0.15

[PATH]
/ddl/db2/ccp/views

[FILELIST]
drop_target_headcount_by_hire_group_view.sql

[PATH]
/ddl/db2/ccp/tables

[FILELIST]
drop_emp_action.sql
drop_emp_credit_ser.sql
drop_emp_curr_job.sql
drop_emp_job_hist.sql
drop_emp_payroll.sql
drop_emp_rrb.sql
drop_emp_status.sql
create_actual_turnover.sql
create_emp_action.sql
create_emp_credit_ser.sql
create_emp_curr_job.sql
create_emp_dataset.sql
create_emp_file.sql
create_emp_job_hist.sql
create_emp_payroll.sql
create_emp_rrb.sql
create_emp_status.sql
create_hybrid_result.sql
create_model_config.sql
create_model_cox_fs_run.sql
create_model_cox_fs_var.sql
create_model_input.sql
create_model_input_curr_emp.sql
create_model_input_dataset.sql
create_model_preproc_input.sql
create_model_preproc_input_emp.sql
create_model_result.sql
create_model_run.sql
create_model_subrun.sql
	

[PATH]
/ddl/db2/ccp/indexes

[FILELIST]
create_actual_turnover_idx1.sql
create_emp_action_idx1.sql
create_emp_credit_ser_idx1.sql
create_emp_curr_job_idx1.sql
create_emp_file_idx1.sql
create_emp_job_hist_idx1.sql
create_emp_payroll_idx1.sql
create_emp_rrb_idx1.sql
create_emp_status_idx1.sql
create_hybrid_result_idx1.sql
create_model_cox_fs_run_idx1.sql
create_model_input_curr_emp_idx1.sql
create_model_input_dataset_idx1.sql
create_model_input_idx1.sql
create_model_preproc_input_idx1.sql
create_model_preproc_input_emp_idx1.sql
create_model_result_idx1.sql
create_model_run_idx1.sql
create_model_subrun_idx1.sql

[PATH]
/ddl/db2/ccp/reorgs

[FILELIST]
reorg_actual_turnover.sql
reorg_emp_action.sql
reorg_emp_credit_ser.sql
reorg_emp_curr_job.sql
reorg_emp_dataset.sql
reorg_emp_file.sql
reorg_emp_job_hist.sql
reorg_emp_payroll.sql
reorg_emp_rrb.sql
reorg_emp_status.sql
reorg_hybrid_result.sql
reorg_model_config.sql
reorg_model_cox_fs_run.sql
reorg_model_cox_fs_var.sql
reorg_model_input.sql
reorg_model_input_curr_emp.sql
reorg_model_input_dataset.sql
reorg_model_preproc_input.sql
reorg_model_preproc_input_emp.sql
reorg_model_result.sql
reorg_model_run.sql
reorg_model_subrun.sql

[PATH]
/ddl/db2/ccp/views

[FILELIST]
create_target_headcount_by_hire_group_view.sql

[PATH]
/ddl/db2/ccp/permissions

[FILELIST]
grant.sql
