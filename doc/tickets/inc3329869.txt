Short description: Pods are not schedulable to the 4n6h2 and cpwml nodes in the or-batch-model=false machineset in the or-qa namespace

Description:

    In the or-qa namespace, there are 4 nodes designated for the or-batch-model=false machineset, as follows:

    ocp4np-xp2bf-or-batch-model-false-qa-4n6h2
    ocp4np-xp2bf-or-batch-model-false-qa-cpwml
    ocp4np-xp2bf-or-batch-model-false-qa-m75fx
    ocp4np-xp2bf-or-batch-model-false-qa-qjs7n

    However, it is impossible to schedule pods on the  4n6h2 and cpwml nodes, which causes us to be unable to deploy new applications to QA if insufficient cores and memory are available on the other 2 nodes.  

    As an experiment I tried creating a pod with a large core and cpu requirement and it never proceeds past the "Pending" state and is listed as Unschedulable.  See https://console-openshift-console.apps.ocp4np.nscorp.com/k8s/ns/or-qa/pods/inc3329869-b6w26.  Had 4n6h2 and cpwml been available, this pod would have been assigned to one of them.
