Short description:
Adjust Workforce Planning Model image pull policy to "IfNotPresent".

Description:

This change switches the image pull policy on all the WPM pods from "Always" to "IfNotPresent".  
This was recommended by the EAS group to improve redundancy facilitate rapid recovery of 
pods after restart.

Justification:
See Description.

Implementation:
OR Group:
=========
Edit each of the following files in the or-ocp-config repository, changing
ImagePullPolicy from "Always" to "IfNotPresent":

./or-ccp-ui-rest/prod/or-ccp-ui-rest.yaml 
./or-ccp-ui-rest/dr/or-ccp-ui-rest.yaml 
./or-ccp-logreg-model/prod/or-ccp-logreg-model.yaml 
./or-ccp-logreg-model/dr/or-ccp-logreg-model.yaml 
./or-ccp/prod/or-ccp.yaml 
./or-ccp/dr/or-ccp.yaml 
./or-jobmgr/prod/or-jobmgr-deployment.yaml 
./or-jobmgr/dr/or-jobmgr-deployment.yaml 
./or-ccp-model-preprocessor/prod/or-ccp-model-preprocessor.yaml 
./or-ccp-model-preprocessor/dr/or-ccp-model-preprocessor.yaml 
./or-ccp-xgboost-model/prod/or-ccp-xgboost-model.yaml 
./or-ccp-xgboost-model/dr/or-ccp-xgboost-model.yaml 
./or-opd-wpm-ws/prod/or-opd-wpm-ws-deploy.yaml 
./or-opd-wpm-ws/dr/or-opd-wpm-ws-deploy.yaml 
./or-ccp-ui/prod/or-ccp-ui-deploy.yaml 
./or-ccp-ui/dr/or-ccp-ui-deploy.yaml 
./or-ccp-cox-model/prod/or-ccp-cox-model.yaml 
./or-ccp-cox-model/dr/or-ccp-cox-model.yaml 

Then push changes to Github and monitor the pods to make sure they come up.

Risk impact and analysis:
    Risks to collocated applications:
        This application runs on the OR-dedicated namespace in the Openshift (OCP4) environment.  Server
        names are of the form "ocp4-tskm4-or-*.atldc.nscorp.com".  These servers are dedicated exclusively
        to OR applications, but multiple applications will share the same virtual servers and there is
        no way of knowing specifically which applications are collocated with which others.  The 
        Openshift infrastructure imposes hard limits on CPU, memory, and disc usage that prevents 
        collocated applications from interfering with one another as might happen with a traditional, 
        non-containerized deployment model.
        
    Risks to downstream applications:
        There are no applications downstream of WPM.

    Operational impact:
        The application will be down for approximately 15 minutes for the upgrade.  If the change 
        were implemented and then seen to cause problems, then study creation, simulation model
        execution, and the predictive models may not work as expected until the backout procedures
        were completed.  Backout will take approximately 15 minutes.

Rollback:
OR Group:
=========
Edit each of the following files in the or-ocp-config repository, changing
ImagePullPolicy from "IfNotPresent" to "Always":

./or-ccp-ui-rest/prod/or-ccp-ui-rest.yaml 
./or-ccp-ui-rest/dr/or-ccp-ui-rest.yaml 
./or-ccp-logreg-model/prod/or-ccp-logreg-model.yaml 
./or-ccp-logreg-model/dr/or-ccp-logreg-model.yaml 
./or-ccp/prod/or-ccp.yaml 
./or-ccp/dr/or-ccp.yaml 
./or-jobmgr/prod/or-jobmgr-deployment.yaml 
./or-jobmgr/dr/or-jobmgr-deployment.yaml 
./or-ccp-model-preprocessor/prod/or-ccp-model-preprocessor.yaml 
./or-ccp-model-preprocessor/dr/or-ccp-model-preprocessor.yaml 
./or-ccp-xgboost-model/prod/or-ccp-xgboost-model.yaml 
./or-ccp-xgboost-model/dr/or-ccp-xgboost-model.yaml 
./or-opd-wpm-ws/prod/or-opd-wpm-ws-deploy.yaml 
./or-opd-wpm-ws/dr/or-opd-wpm-ws-deploy.yaml 
./or-ccp-ui/prod/or-ccp-ui-deploy.yaml 
./or-ccp-ui/dr/or-ccp-ui-deploy.yaml 
./or-ccp-cox-model/prod/or-ccp-cox-model.yaml 
./or-ccp-cox-model/dr/or-ccp-cox-model.yaml 

Then push changes to Github and monitor the pods to make sure they come up.

Test plan:
After deployment, we will create a scenario, run it to completion, and verify 
results.  Also we will briefly kick off each of the Python models and the model preprocessor
to verify that the jobs are able to be scheduled.
