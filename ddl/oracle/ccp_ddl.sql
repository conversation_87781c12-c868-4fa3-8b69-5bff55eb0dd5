spool ccp_ddl.log

set echo on;
set time on;
set timing on;
show user;

create view cpo.nsor_extl_stat_earn_view as 
select
    extl_stat_earn_oid,
    dist_code,
    sub_dist_code,
    bord_id,
    trn_id,
    eff_ts,
    empl_nbr,
    crft_pos_code,
    on_duty_ts,
    off_duty_ts,
    curr_asmt_bord_type,
    curr_asmt_dist_code,
    curr_asmt_sub_dist_code,
    curr_asmt_bord_id,
    curr_asmt_turn_id,
    curr_asmt_crft_pos_code,
    perm_asmt_bord_type,
    perm_asmt_dist_code,
    perm_asmt_sub_dist_code,
    perm_asmt_bord_id,
    perm_asmt_turn_id,
    perm_asmt_crft_pos_code,
    earn_amt,
    load_ts,
    dep_dt,
    arvl_dt,
    asgn_hist_asgn_dist_code,
    asgn_hist_asgn_sub_dist_code,
    asgn_hist_asgn_id,
    ondu_asgn_dist_code,
    ondu_asgn_sub_dist_code,
    ondu_asgn_asgn_id,
    ondu_asgn_crft_pos_code,
    crew_prof_name,
    dep_stn_name,
    arvl_stn_name,
    trn_symb,
    trn_prof_mtch_step_code,
    crew_orgn_stn_code,
    trn_tmzn_code
from cpo.extl_stat_earn;

create view cpo.nsor_ascd_crew_notn_prof_view as
select 
    ascd_crew_notn_prof_oid,
    stn_code,
    trn_symb,
    trn_line_segm_valu,
    crew_pool_prof_name,
    dep_stn_code,
    arvl_stn_code,
    load_ts,
    crew_orgn_stn_code,
    src_sys_last_uptd_date
from cpo.ascd_crew_notn_prof;

create view cpo.nsor_bss_trn_strt_view as 
select
    trn_id,
    load_ts,
    asgn_engr_xb,
    asgn_engr_xb_dist_code,
    asgn_engr_xb_sub_dist_code,
    asgn_fl,
    asgn_cond_xb,
    asgn_cond_xb_dist_code,
    asgn_cond_xb_sub_dist_code,
    trn_type,
    crew_orig_oprn_stn,
    crew_prof_id,
    dest_arvl_oprn_stn,
    dest_arvl_dt,
    finl_tml_dwel_dly_time,
    from_loca_seq_nbr,
    tml_dwel_dly_init,
    load_resn_code,
    orig_dep_dt,
    orig_dep_oprn_stn,
    segm_tnst_time_mins,
    to_loca_seq_nbr,
    trn_crew_dist_segm_valu,
    trn_orig_dt,
    trn_symb_sctn,
    trn_symb,
    crtd_user_id,
    crtd_ts,
    last_updt_user_id,
    last_updt_ts,
    hub_key,
    trn_prof_mtch_step_code,
    trn_base_symb,
    tsr_crew_stn
from cpo.bss_trn_strt;

create view nsop.nsor_job_view as 
    select
    job_id,
    request_id,
    source,
    servant_id,
    request,
    response,
    status,
    request_filename,
    response_filename,
    receipt_ts,
    assigned_ts,
    execution_start_ts,
    execution_stop_ts,
    response_enqueue_ts
from nsop.job;

create view nsop.nsor_job_archive_view as 
    select
    job_id,
    request_id,
    source,
    servant_id,
    request,
    response,
    status,
    request_filename,
    response_filename,
    receipt_ts,
    assigned_ts,
    execution_start_ts,
    execution_stop_ts,
    response_enqueue_ts
from nsop.job_archive;

grant select on cpo.nsor_extl_stat_earn_view to CP_READ;
grant select on cpo.nsor_ascd_crew_notn_prof_view to CP_READ;
grant select on cpo.nsor_bss_trn_strt_view to CP_READ;
grant select on nsop.nsor_job_view to CP_READ;
grant select on nsop.nsor_job_archive_view to CP_READ;

spool off;
