-- grant select, insert, update, delete on table ccp.model_input_file to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.markoff_study to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.markoff_data to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.target_headcount_by_hire_group to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.hire_group_name_mapping to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_file to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_input_dataset to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_dataset to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_preproc_input_emp to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_cox_fs_run to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_cox_fs_var to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_input_curr_emp to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_config to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.hybrid_result to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.hybrid_result_export to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.actual_turnover to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_subrun to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_input to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.cox_model_variables to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_preproc_input to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_result to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.model_run to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_action to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_credit_ser to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_curr_job to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_job_hist to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_payroll to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_rrb to group SCPMAPP, group SCPMFIR;
-- grant select, insert, update, delete on table ccp.emp_status to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.upside to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.upside_train to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.board_stats to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.trn_delay_stats to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.trn_stats_by_pool to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.monitor_log to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.study_trn_type to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.opd_train to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.opd_scenario to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.control_param to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.batch_run to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.exb_pool_set_up to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.crew_profile to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.train_hub to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.crewpro_group to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.train to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.scenario_cfg to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.turn to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.user to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.crewpro_trn_strt to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.self_sustaining to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.board_summary to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.pool to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.tue_subrule to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.exb_turn to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.scenario to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.study to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.carded_day to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.scen_hub to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.search_pool to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.pool_rotation_history to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.turn_utilization to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.simulation_train_output to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.scen_line_seg to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.work_rest_prof_tgrp to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.plan to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.board_train_starts to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.extraboard to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.carded_pool to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.id_pool to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.work_rest_prof_rotn to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.tie_up_exception to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.work_rest_prof to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.job to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.pool_set_up to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.hub to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.scen_train to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.opd_plan to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.board_craft_hire_group_mapping to group SCPMAPP, group SCPMFIR;
grant select, insert, update, delete on table ccp.board_hire_group_mapping to group SCPMAPP, group SCPMFIR;

grant select on table ccp.crew_starts_by_hire_group_view to group SCPMAPP, group SCPMFIR;
grant select on table ccp.pool_set_up_view to group SCPMAPP, group SCPMFIR;
grant select on table ccp.exb_turn_view to group SCPMAPP, group SCPMFIR;
grant select on table ccp.turn_view to group SCPMAPP, group SCPMFIR;
grant select on table ccp.extraboard_view to group SCPMAPP, group SCPMFIR;

-- grant select on table ccp.model_input_file to group SCPMDEV;
grant select on table ccp.target_headcount_by_hire_group to group SCPMDEV;
grant select on table ccp.markoff_study to group SCPMDEV;
grant select on table ccp.markoff_data to group SCPMDEV;
grant select on table ccp.hire_group_name_mapping to group SCPMDEV;
-- grant select on table ccp.emp_file to group SCPMDEV;
-- grant select on table ccp.model_input_dataset to group SCPMDEV;
-- grant select on table ccp.emp_dataset to group SCPMDEV;
-- grant select on table ccp.model_preproc_input_emp to group SCPMDEV;
-- grant select on table ccp.model_cox_fs_run to group SCPMDEV;
-- grant select on table ccp.model_cox_fs_var  to group SCPMDEV;
-- grant select on table ccp.model_input_curr_emp to group SCPMDEV;
-- grant select on table ccp.model_config to group SCPMDEV;
-- grant select on table ccp.hybrid_result to group SCPMDEV;
-- grant select on table ccp.hybrid_result_export to group SCPMDEV;
-- grant select on table ccp.actual_turnover to group SCPMDEV;
-- grant select on table ccp.model_subrun to group SCPMDEV;
-- grant select on table ccp.model_input to group SCPMDEV;
-- grant select on table ccp.cox_model_variables to group SCPMDEV;
-- grant select on table ccp.model_preproc_input to group SCPMDEV;
-- grant select on table ccp.model_result to group SCPMDEV;
-- grant select on table ccp.model_run to group SCPMDEV;
grant select on table ccp.board_stats to group SCPMDEV;
-- grant select on table ccp.emp_action to group SCPMDEV;
-- grant select on table ccp.emp_credit_ser to group SCPMDEV;
-- grant select on table ccp.emp_curr_job to group SCPMDEV;
-- grant select on table ccp.emp_job_hist to group SCPMDEV;
-- grant select on table ccp.emp_payroll to group SCPMDEV;
-- grant select on table ccp.emp_rrb to group SCPMDEV;
-- grant select on table ccp.emp_status to group SCPMDEV;
grant select on table ccp.upside to group SCPMDEV;
grant select on table ccp.upside_train to group SCPMDEV;
grant select on table ccp.trn_delay_stats to group SCPMDEV;
grant select on table ccp.trn_stats_by_pool to group SCPMDEV;
grant select on table ccp.monitor_log to group SCPMDEV;
grant select on table ccp.study_trn_type to group SCPMDEV;
grant select on table ccp.opd_train to group SCPMDEV;
grant select on table ccp.opd_scenario to group SCPMDEV;
grant select on table ccp.control_param to group SCPMDEV;
grant select on table ccp.batch_run to group SCPMDEV;
grant select on table ccp.exb_pool_set_up to group SCPMDEV;
grant select on table ccp.crew_profile to group SCPMDEV;
grant select on table ccp.train_hub to group SCPMDEV;
grant select on table ccp.crewpro_group to group SCPMDEV;
grant select on table ccp.train to group SCPMDEV;
grant select on table ccp.scenario_cfg to group SCPMDEV;
grant select on table ccp.turn to group SCPMDEV;
grant select on table ccp.user to group SCPMDEV;
grant select on table ccp.crewpro_trn_strt to group SCPMDEV;
grant select on table ccp.self_sustaining to group SCPMDEV;
grant select on table ccp.board_summary to group SCPMDEV;
grant select on table ccp.pool to group SCPMDEV;
grant select on table ccp.tue_subrule to group SCPMDEV;
grant select on table ccp.exb_turn to group SCPMDEV;
grant select on table ccp.scenario to group SCPMDEV;
grant select on table ccp.study to group SCPMDEV;
grant select on table ccp.carded_day to group SCPMDEV;
grant select on table ccp.scen_hub to group SCPMDEV;
grant select on table ccp.search_pool to group SCPMDEV;
grant select on table ccp.pool_rotation_history to group SCPMDEV;
grant select on table ccp.turn_utilization to group SCPMDEV;
grant select on table ccp.simulation_train_output to group SCPMDEV;
grant select on table ccp.scen_line_seg to group SCPMDEV;
grant select on table ccp.work_rest_prof_tgrp to group SCPMDEV;
grant select on table ccp.plan to group SCPMDEV;
grant select on table ccp.board_train_starts to group SCPMDEV;
grant select on table ccp.extraboard to group SCPMDEV;
grant select on table ccp.carded_pool to group SCPMDEV;
grant select on table ccp.id_pool to group SCPMDEV;
grant select on table ccp.work_rest_prof_rotn to group SCPMDEV;
grant select on table ccp.tie_up_exception to group SCPMDEV;
grant select on table ccp.work_rest_prof to group SCPMDEV;
grant select on table ccp.job to group SCPMDEV;
grant select on table ccp.pool_set_up to group SCPMDEV;
grant select on table ccp.hub to group SCPMDEV;
grant select on table ccp.scen_train to group SCPMDEV;
grant select on table ccp.opd_plan to group SCPMDEV;
grant select on table ccp.board_craft_hire_group_mapping to group SCPMDEV;
grant select on table ccp.board_hire_group_mapping to group SCPMDEV;

grant select on table ccp.crew_starts_by_hire_group_view to group SCPMDEV;
grant select on table ccp.pool_set_up_view to group SCPMDEV;
grant select on table ccp.exb_turn_view to group SCPMDEV;
grant select on table ccp.turn_view to group SCPMDEV;
grant select on table ccp.extraboard_view to group SCPMDEV;

