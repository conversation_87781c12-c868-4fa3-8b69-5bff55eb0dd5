drop table if exists ccp.crew_profile ;
create table ccp.crew_profile  (
    crew_profile_id  bigint not null generated by default as identity primary key,
    plan_id bigint not null,
		  tieup_exception4 integer , 
		  crew_destin_os varchar(16) , 
		  sub_distr char(2) not null , 
		  pool_home_away integer not null , 
		  craft char(2) not null , 
		  crew_orgn_os varchar(16) not null , 
		  miles integer , 
		  profile_id varchar(16) not null , 
		  rin varchar(16) , 
		  pool_profile_desc varchar(20) , 
		  tieup_exception1 integer , 
		  distr char(2) not null , 
		  tieup_exception2 integer , 
		  pool_name char(2) not null , 
		  tieup_exception3 integer )   in scpm_rpt_usr index in scpm_rpt_idx; 


