drop table if exists ccp.carded_day;
create table ccp.carded_day(
    carded_day_id bigint not null generated by default as identity primary key,
    carded_pool_id bigint not null,
    day_of_cycle integer not null , 
    home_distr char(2) , 
    home_pool_name char(2) , 
    home_sub_distr char(2) , 
    loc_distr char(2) , 
    loc_home_away integer , 
    loc_pool_name char(2) , 
    loc_sub_distr char(2) , 
    start_day integer , 
    turn_name char(4) ) in scpm_rpt_usr index in scpm_rpt_idx;

