import com.nscorp.ccp.launcher.*;
import com.nscorp.ccp.cli.user.*;
import com.nscorp.ccp.common.user.*;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.common.scenario.*;
var ac = JShellRunner.applicationContext
var usermgr = ac.getBean(UserManager.class)
var planmgr = ac.getBean(PlanManager.class)
var planrptmgr = ac.getBean(PlanReportManager.class)
var studymgr = ac.getBean(StudyManager.class)
var studyrptmgr = ac.getBean(StudyReportManager.class)
