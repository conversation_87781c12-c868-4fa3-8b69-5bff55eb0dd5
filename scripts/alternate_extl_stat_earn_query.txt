with t1 as (select                                             
    dep_stn_name as from_os,                                  
    arvl_stn_name as to_os,                                 
    crew_prof_name as crew_profile_id,                                
    trn_id,                                        
    trn_symb,                                      
    crew_orgn_stn_code as crew_orgn_os,                            
    load_ts,                                       
    on_duty_ts,                                    
    count(*) frequency
from cpo.nsor_extl_stat_earn_view                  
where on_duty_ts > sysdate-90                      
    and trn_symb not in ('470', '471', '472', '473', '474', '475', '476', '477',         
    '478', '479', '480', '481', '482', '483', '484', '485', '486', '487', '488',         
    '489', 'X70', 'X71', 'X72', 'X73', 'X74', 'X75', 'X76', 'X77', 'X78', 'X79',         
    'X80', 'X81', 'X82', 'X83', 'X84', 'X85', 'X86', 'X87', 'X88', 'X89') and            
    crew_prof_name is not null and                                                       
    crew_orgn_stn_code is not null
group by 
    dep_stn_name,                                  
    arvl_stn_name,                                 
    crew_prof_name,                                
    trn_id,                                        
    trn_symb,                                      
    crew_orgn_stn_code,                            
    load_ts,                                       
    on_duty_ts
)
select 
    from_os,
    to_os,
    crew_profile_id,
    trn_id,
    trn_symb,
    crew_orgn_os,
    load_ts,
    on_duty_ts,
    null as train_orgn_os,                   
    null as src_sys_last_uptd_date,                        
    null as line_segment       ,
    frequency
from t1 
order by frequency desc
fetch first 1000 rows only
