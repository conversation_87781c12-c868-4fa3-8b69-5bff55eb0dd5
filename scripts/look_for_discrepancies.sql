create view ccp.forecast_emp_discrepancy as
select 
    a.emp_nbr,
    a.input_dataset_id,
    b.staging_dataset_id,
    a.forecast_start_dt,
    a.forecast_mth,
    a.craft as java_craft,
    b.craft as python_craft,
    a.active_count_training as java_active_count_training,
    b.active_count_training as python_active_count_training,
    a.attri_end_dt_new_training as java_attri_end_dt_new_training,
    b.attri_end_dt_new_training as python_attri_end_dt_new_training,
    a.attrit_count as java_attrit_count,
    b.attrit_count as python_attrit_count,
    a.employee_status as java_employee_status,
    b.employee_status as python_employee_status,
    a.end_dt_training as java_end_dt_training,
    b.end_dt_training as python_end_dt_training,
    a.ref_attrit_status_training as java_ref_attrit_status_training,
    b.ref_attrit_status_training as python_ref_attrit_status_training,
    a.last_act_date as java_last_act_date,
    b.last_act_date as python_last_act_date,
    a.last_strict_act_date as java_last_strict_act_date,
    b.last_strict_act_date as python_last_strict_act_date,
    a.transferring as java_transferring,
    b.transferring as python_transferring
from
   ccp.model_input_forecast_emp_view a,
   ccp.forecast_emp_staging b
where
   -- a.input_dataset_id=5 and
   -- b.staging_dataset_id=5 and
   -- a.forecast_mth >= 1 and
   -- a.forecast_mth <= 12 and
   a.forecast_start_dt >= '2021-01-01' and
   a.forecast_start_dt <= '2021-06-01' and
   a.forecast_start_dt = b.forecast_start_dt and
   a.forecast_mth = b.forecast_mth and
   a.emp_nbr=b.emp_nbr and 
   ( a.hire_grp_desc_training <> b.hire_grp_desc_training or
     a.active_count_training <> b.active_count_training or
     a.attri_end_dt_new_training <> b.attri_end_dt_new_training or
     a.attrit_count <> b.attrit_count or
     a.craft <> b.craft or
     a.gender <> b.gender or
     a.employee_status <> b.employee_status or
     a.end_dt_training <> b.end_dt_training or
     a.ref_attrit_status_training <> b.ref_attrit_status_training or
     a.last_act_date <> b.last_act_date or
     a.last_strict_act_date <> b.last_strict_act_date or
     a.transferring <> b.transferring);
