#! /usr/bin/env bash
while [ true ]
do
    date
    sleep 1
done
#if [ $SPRING_PROFILES_ACTIVE = test ] && [ $1 != runsim ] 
#then
#  # Special mode for testing
#  while [ true ]
#  do
#    JAR=or-ccp.jar
#    if [ -e /tmp/or-ccp.jar ]
#    then
#      JAR=/tmp/or-ccp.jar
#    fi
#    java -XX:TieredStopAtLevel=1 -Xverify:none -jar $JAR $*
#  done
#fi
#exec java -XX:TieredStopAtLevel=1 -Xverify:none -jar or-ccp.jar $*
