CREATE VOLATILE TABLE EMP_STATUS_MAP
(<PERSON>O_TYPE  CHAR(1)
,RC CHAR(2)
,IS_ACTIVE SMALLINT
,IS_ATTRIT SMALLINT
,GRACE_PER SMALLINT
,IS_RETIRE SMALLINT
,IS_UNAVAIL SMALLINT
,IS_SUSP_CT SMALLINT
,IS_FURLOUGH SMALLINT
,SEP_TYP CHAR(1))
ON COMMIT PRESERVE ROWS;

insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('A', NULL, 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'RO', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'RA', 0, 0, 5, 1, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', NULL, 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('X', 'DC', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('E', 'HP', 0, 1, 180, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', NULL, 1, 0, NULL, 0, 1, 1, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('D', 'TD', 0, 1, 180, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'SA', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'DS', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Y', 'RD', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'D3', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('V', 'VA', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CB', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'DI', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('S', 'MS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Q', 'IJ', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'D5', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'LS', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Y', 'HS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'D1', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FA', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FO', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'D2', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('P', 'MP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('R', 'IO', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'YM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('J', 'ML', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'LM', 0, 1, 180, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('V', 'DL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'SU', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'P1', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'FE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'MT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'LA', 0, 1, 180, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'DR', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('O', 'MU', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'FF', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'LT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('H', 'RT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'LU', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Y', 'BB', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('D', NULL, 0, 1, 60, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'D4', 0, 1, 5, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('F', 'MN', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'BE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('T', 'SM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'SQ', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'EC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('I', 'CN', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'ER', 0, 0, 5, 1, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'KR', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'FC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FB', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', 'AS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('N', 'JU', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'FE', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Y', 'SR', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'CB', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FD', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'SL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', NULL, 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', 'RO', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'YM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('M', 'ID', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('%', 'DD', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('$', 'OS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'P3', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'DV', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'RO', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'SA', 0, 1, 120, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', 'TD', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', 'HP', 0, 1, 5, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', 'CB', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('X', NULL, 0, 1, 5, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('A', 'RO', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('C', 'YM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('#', NULL, 0, 1, 5, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'OL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('V', 'NP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('M', 'IC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'LE', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('M', 'IU', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'RD', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CH', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('O', 'CL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'CE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'HS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('G', 'MW', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'LT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'MP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'VA', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('G', 'EV', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'KR', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'EC', 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'CH', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'MS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'BD', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'ML', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'DL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'TD', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'CJ', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'DC', 0, 1, 120, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'HP', 0, 1, 120, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'MN', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'P1', 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'RA', 0, 0, 120, 1, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'SQ', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'BB', 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'MU', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'CN', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'MT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'BE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'SM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'FF', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'IJ', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'NP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'FE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'SL', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'ID', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('A', 'YM', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('S', 'SS', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FW', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FT', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('W', 'FP', 0, 0, NULL, 0, 0, 0, 1,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'DE', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'DF', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'PI', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('K', 'QT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'BR', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'DA', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'FI', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'FR', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'MI', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'MO', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'SC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'SI', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'SO', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('L', 'SP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('A', 'AS', 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('@', 'AS', 1, 0, NULL, 0, 0, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('E', 'NM', 0, 1, 65, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('E', 'SH', 0, 1, 65, 0, 0, 0, 0,'I');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('J', 'AC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('P', 'NC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'DW', 0, 1, 120, 0, 0, 0, 0,'V');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('U', 'MW', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('Z', 'ES', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'RT', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'ES', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'CP', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'AC', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'P3', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('B', 'DD', 1, 0, NULL, 0, 1, 0, 0,'N');
insert into EMP_STATUS_MAP(LO_TYPE, RC, IS_ACTIVE, IS_ATTRIT, GRACE_PER, IS_RETIRE, IS_UNAVAIL, IS_SUSP_CT, IS_FURLOUGH,SEP_TYP) values ('V', 'AD', 1, 0, NULL, 0, 1, 0, 0,'N');

with aa as
(
SELECT a.EMP_NBR, a.HGRP6, a.CC, a.AS_OF_DT,
	--a.DOE_DT,
	a.BIRTH_DT,
    a.Gender,
 case when c.CRAFT_SVC_TYPE='TS' or a.CC in ('AC','Y1','S0') then 'CO' when c.CRAFT_SVC_TYPE='ES' then 'EN' when a.CC='CX' then 'CT' else a.CC end as Craft,
 a.weight*sm1.is_active as Active_Count,
 a.weight*sm1.is_unavail as Unavail_Count,
 sm2.is_retire-sm1.is_retire as Retire_Count,
 case when (a.SD=a.DS or a.SD='XX') and sm1.is_retire=0 and sm1.is_attrit=0
 then 1 else 0 end as is_SD_attrit1,
 case when (a.SD_NEXT=a.DS_NEXT or a.SD_NEXT='XX') and sm2.is_retire=0 and sm2.is_attrit=0
 then 1 when a.SD_NEXT is null then null else 0 end as is_SD_attrit2,
 sm1.is_attrit as is_attrit1,
 sm2.is_attrit as is_attrit2,
 sm2.grace_per
 ,a.lo_type,a.rc,a.lo_next,a.rc_next
 ,sm1.is_furlough as is_furlough
 ,sm2.is_furlough as is_furlough_nxt
 ,sm2.sep_typ
FROM
(
SELECT eh.EMP_NBR,eh.DS, hire_grp_desc as HGRP6, eh.CC, eh.AS_OF_DT,
--coalesce (ee.ORGNL_HIRE_DT, e.DOE_DT) as DOE_DT,
ee.BIRTH_DT,
 max(eh.AS_OF_DT) over (partition by eh.EMP_NBR order by eh.AS_OF_DT, eh.TD_LD_TS rows between 1 following and 1 following) - eh.AS_OF_DT as weight,
 eh.LO_TYPE, eh.RC, eh.SD,
 max(eh.LO_TYPE) over (partition by eh.EMP_NBR order by eh.AS_OF_DT, eh.TD_LD_TS rows between 1 following and 1 following) as LO_NEXT,
 max(eh.RC) over (partition by eh.EMP_NBR order by eh.AS_OF_DT, eh.TD_LD_TS rows between 1 following and 1 following) as RC_NEXT,
 max(eh.SD) over (partition by eh.EMP_NBR order by eh.AS_OF_DT, eh.TD_LD_TS rows between 1 following and 1 following) as SD_NEXT,
 max(eh.DS) over (partition by eh.EMP_NBR order by eh.AS_OF_DT, eh.TD_LD_TS rows between 1 following and 1 following) as DS_NEXT, --melody added 03/10/2023

 case when ee.gender in ('M','F') then ee.gender
  when e.EMP_TITLE in ('MR','Male','MALE') then 'M' when e.EMP_TITLE in ('MRS','MISS','MS','Female','FEMALE') then 'F' else 'U' end as Gender
FROM crew_pview.TE_EMPLOYEE_H eh
 left JOIN crew_pview.TE_EMPLOYEE e on (e.EMP_NBR=eh.EMP_NBR)
 left JOIN pv_tepy_01.DM_EMPLOYEE ee on (ee.EMP_NBR=eh.EMP_NBR)
 left join Crew_PVIEW.HIRE_GROUP hr on hr.hire_grp = eh.hire_grp
WHERE eh.EMP_NBR < 999000000
--and extract (year from AS_OF_DT)=2020
--and eh.EMP_NBR=32418-- in (32418,1028825 ,1026974 ,395745, 1021360, 1021537,683753, 49337, 40545,	1026974  )                                               -- in (2920,1205,3935,11915,91596,151006,5524,253,899787 ,554)
and (eh.DS in ('AL','GA','PI','PO','HB','PB','DB','LA','IL')
or (eh.DS='VA' and eh.AS_OF_DT < DATE'2016-02-01')
or (eh.DS='TN' and eh.AS_OF_DT <= DATE'2017-11-01' )) --TN natually gone after 2017-11-01
and (eh.cc not in ('YA','YB') and substr(eh.hire_grp,7,2) not in ('YA','YD','YM')) --just add YA,YD on top of YM.
and substr(eh.hire_grp,1,4) not in ('XXXX','AAAA')
and substr(eh.hire_grp,7,2) not in ('XX','AA')
  --and (eh.lo_type<>'B' or eh.rc<>'DI')
and (
 (eh.lo_type='D' and eh.rc ='TD') or
  (eh.lo_type='C' and (eh.rc is null or eh.rc ='AS'))  or
 (eh.lo_type='X' and eh.rc ='DC')  or
  (eh.lo_type='A' and (eh.rc is null or eh.rc ='AS') ) or
 ( eh.lo_type='B' and (eh.rc is null or eh.rc not in ('RO','SA','TD','DC' ,'HP','DI','RA',
 'D1', 'D3', 'IO', 'SS', 'DS', 'D5', 'LS','FA','FT' ) )) or
 ( eh.lo_type='#' and ( eh.rc <>'HP') ) or
 eh.lo_type not in ('D' ,'C' ,'X' ,'A' ,'B','#' )
 )

) as a
 LEFT JOIN EMP_STATUS_MAP sm1 on (sm1.LO_TYPE=a.LO_TYPE and (sm1.RC=a.RC or (sm1.RC is null and a.RC is null)))
 LEFT JOIN EMP_STATUS_MAP sm2 on (sm2.LO_TYPE=a.LO_NEXT and (sm2.RC=a.RC_NEXT or (sm2.RC is null and a.RC_NEXT is null)))
 LEFT JOIN crew_pview.CRAFT c on c.CC=a.CC
--WHERE --(sm1.is_active>0 or sm1.is_unavail>0 or sm1.is_retire<>sm2.is_retire or sm1.is_attrit<>sm2.is_attrit)
-- and sm1.is_furlough=0
-- and
-- a.BIRTH_DT is not null and a.DOE_DT is not null
) ,
 d as (
SELECT b.EMP_NBR, b.HGRP6, b.Craft, b.AS_OF_DT,-- b.DOE_DT,
b.BIRTH_DT, b.Gender,
 b.Active_Count,
 b.Unavail_Count,
 /*This next bit works because any next row is active or a transition*/
 /*case when b.Retire_Count < 1
  or min(b.AS_OF_DT) over (partition by b.EMP_NBR order by b.AS_OF_DT rows between 1 following and 1 following) - b.AS_OF_DT <= coalesce(b.grace_per,120)
 or ((b.Retire_Count=0 or b.Retire_Count is null) and min(b.AS_OF_DT) over (partition by b.EMP_NBR order by b.AS_OF_DT rows between 1 following and 1 following) - b.AS_OF_DT is null)
   then 0 else 1 end as Retire_Count,
 case when b.Attrit_Count < 1
  or min(b.AS_OF_DT) over (partition by b.EMP_NBR order by b.AS_OF_DT rows between 1 following and 1 following) - b.AS_OF_DT <= coalesce(b.grace_per,120)
  then 0 else 1 end as Attrit_Count*/
  b.Retire_Count as Retire_Count,b.Attrit_Count as Attrit_Count
 ,b.AttritSD_Count --consider add grace period from here. or post processing in program
 ,b.lo_type,b.rc,
  b.is_furlough,b.is_furlough_nxt
  ,b.AS_OF_DT-ROW_NUMBER() OVER (PARTITION BY b.emp_nbr, b.HGRP6, b.Craft, b.Gender ORDER by b.as_of_dt ) as id
 ,b.sep_typ, b.grace_per
 ,b.lo_next,b.rc_next
 --,years_of_service, TE_E_DOE_DT, lst_hire_dt
--,retire_dt, termination_dt
FROM
(
SELECT aa.EMP_NBR, aa.HGRP6, aa.CC, aa.AS_OF_DT, --aa.DOE_DT,
aa.BIRTH_DT, aa.Gender, aa.Craft,
 aa.Active_Count*(1-aa.is_SD_attrit1) as Active_Count,
 aa.Unavail_Count as Unavail_Count,
 aa.Retire_Count,
 greatest(aa.is_attrit2, aa.is_SD_attrit2) - greatest(aa.is_attrit1, aa.is_SD_attrit1) as Attrit_Count,
 aa.is_SD_attrit2 - aa.is_SD_attrit1 as AttritSD_Count,
 aa.grace_per,aa.lo_type,aa.rc ,aa.is_furlough,aa.is_furlough_nxt
 ,aa.sep_typ
 ,aa.lo_next,aa.rc_next
FROM aa
--WHERE(aa.is_SD_attrit1=0 or aa.is_SD_attrit2=0 or
--aa.is_furlough<>aa.is_furlough_nxt) --and aa.is_furlough=0
) as b
),

last_attri as (
	select emp_nbr, --AS_OF_DT + CAST(coalesce(d.grace_per,180) AS INTERVAL DAY) as m_dt
	--case when Attrit_Count > 0 then  AS_OF_DT + CAST(coalesce(d.grace_per,180) AS INTERVAL DAY)
	--else AS_OF_DT end as m_dt
	case when d.grace_per=5 then AS_OF_DT + interval  '5' day
	when d.grace_per=65 then AS_OF_DT + interval  '65' day
	when d.grace_per=120 then AS_OF_DT + interval  '120' day
	when d.grace_per=180 or Attrit_Count > 0 then AS_OF_DT + interval  '180' day
	else AS_OF_DT  end as m_dt
	--max(AS_OF_DT) m_dt
	from d
	WHERE d.AS_OF_DT < :endDate  and
	(d.Attrit_Count > 0 or d.Retire_Count>0
	 --or AttritSD_Count=0
	or d.Active_Count>0)
	qualify max(AS_OF_DT) over (partition by emp_nbr)=AS_OF_DT
	--group by emp_nbr
),
first_act as (
	select emp_nbr,	AS_OF_DT - interval  '180' day  as act_start_dt
	from d
	WHERE d.AS_OF_DT < :endDate  and
	(d.Attrit_Count > 0 or d.Retire_Count>0
	 --or AttritSD_Count=0
	or d.Active_Count>0)
	qualify min(AS_OF_DT) over (partition by emp_nbr)=AS_OF_DT
	--group by emp_nbr
),
newaa as (SELECT d.EMP_NBR, d.HGRP6, d.Craft, d.Gender,
--case when ADD_MONTHS(d.DOE_DT, 12*(extract(year from d.AS_OF_DT)-extract(year from d.DOE_DT))) < d.AS_OF_DT
  --    then extract(year from d.AS_OF_DT)-extract(year from d.DOE_DT)
    --  else extract(year from d.AS_OF_DT)-extract(year from d.DOE_DT)-1 end as Tenure,
--(extract(year from d.AS_OF_DT)-extract(year from d.DOE_DT))*12+extract(month from d.AS_OF_DT)-extract(month from d.DOE_DT) as Tenure_mth,
 case when d.birth_dt is null then 100
  when  ADD_MONTHS(d.BIRTH_DT, 12*(extract(year from d.AS_OF_DT)-extract(year from d.BIRTH_DT))) < d.AS_OF_DT
      then extract(year from d.AS_OF_DT)-extract(year from d.BIRTH_DT)
      else extract(year from d.AS_OF_DT)-extract(year from d.BIRTH_DT)-1 end as Age,
 min(d.AS_OF_DT) as Begins,  max(d.AS_OF_DT) as end_dt,
 extract(month from d.AS_OF_DT) as Mth,
   extract(year from d.AS_OF_DT) as Yr, --Melody added
   --,  d.BIRTH_DT
  -- , d.DOE_DT,
 sum(d.Active_Count) as Active_Count,
 sum(d.Unavail_Count) as Unavail_Count,
 sum(d.Retire_Count) as Retire_Count,
 sum(d.Attrit_Count) as Attrit_Count,
 sum(d.Attrit_Count*d.AttritSD_Count) as AttritSD_Count
  ,sum(d.is_furlough_nxt) as is_furlough_nxt,id, min(grace_per) grace_per
FROM d
WHERE d.AS_OF_DT < :endDate  /*Can't go to present. Must stop earlier to allow attrition grace period.*/
-- and (d.Active_Count > 0 or d.Retire_Count > 0 or d.Attrit_Count > 0)
-- and d.Craft <> 'CT'
GROUP BY d.EMP_NBR, d.HGRP6, d.Craft, d.Gender,  Age, Mth,Yr,d.BIRTH_DT,id --, d.DOE_DT
--,years_of_service, TE_E_DOE_DT, lst_hire_dt,retire_dt, termination_dt
--Having Tenure>-1
),
x as (
select
d.EMP_NBR, newaa.begins,newaa.end_dt,
  (d.AS_OF_DT) as attri_end_dt,
  d.lo_type,d.rc,d.lo_next,d.rc_next,d.sep_typ ,d.grace_per
FROM d inner join newaa on d.emp_nbr=newaa.emp_nbr and
d.as_of_dt>=newaa.Begins and d.as_of_dt<=newaa.end_dt
WHERE d.AS_OF_DT < :endDate
and d.Attrit_Count > 0
--and (d.Attrit_Count > 0 or d.AttritSD_Count>0) --research on 09-06 : do not need, since subquery b already consider greaste(attrit_count, attritsd_count)
qualify max(d.AS_OF_DT) over (partition by d.emp_nbr,newaa.Begins,newaa.end_dt )=d.AS_OF_DT

)
select newaa.EMP_NBR,-- newaa.DS,
 newaa.HGRP6 as hire_grp_desc, newaa.Craft, newaa.Gender, newaa.Age, newaa.Mth,newaa.Yr ,
newaa.Begins,newaa.end_dt,--newaa.BIRTH_DT,
--newaa.DOE_DT ,
 newaa.Active_Count,
newaa.Unavail_Count,newaa.Retire_Count, newaa.Attrit_Count,newaa.AttritSD_Count,
newaa.is_furlough_nxt as furlough_count,
case when newaa.Attrit_Count=1 and x.grace_per is not null then x.grace_per else newaa.grace_per end as grace_per,
--x.attri_end_dt,ehf_new.lo_type,ehf_new.rc,ehf_new.lo_next,ehf_new.rc_next,ehf_new.sep_typ
--case when newaa.Attrit_Count=1 then ehf_new.grace_per else newaa.grace_per end as grace_per,
x.attri_end_dt,x.lo_type,x.rc,x.lo_next,x.rc_next,x.sep_typ
 --,l.m_dt
 from newaa
 left join last_attri l on l.emp_nbr=newaa.emp_nbr
 left join first_act f on f.emp_nbr=newaa.emp_nbr
 left join x
on newaa.emp_nbr=x.emp_nbr --and newaa.ds=x.ds
and newaa.begins=x.begins
and newaa.end_dt=x.end_dt
/*left join aa as ehf
on x.emp_nbr=ehf.emp_nbr and x.attri_end_dt=ehf.as_of_dt

left join (
select ehf.*,x.mth,x.yr
 from aa as ehf inner join x
on x.emp_nbr=ehf.emp_nbr and x.attri_end_dt<=ehf.as_of_dt
and  extract(month from ehf.AS_OF_DT) =x.mth
and  extract(year from ehf.AS_OF_DT) =x.Yr --below qualify still miss age in group by
qualify ( min(ehf.grace_per) over (partition by ehf.emp_nbr,
 ehf.HGRP6, ehf.CC,  extract(month from ehf.AS_OF_DT) ,
   extract(year from ehf.AS_OF_DT))=ehf.grace_per and
   min(ehf.AS_OF_DT) over (partition by ehf.emp_nbr,
 ehf.HGRP6, ehf.CC,  extract(month from ehf.AS_OF_DT) ,
   extract(year from ehf.AS_OF_DT),ehf.grace_per)=ehf.AS_OF_DT )
) as ehf_new on x.emp_nbr=ehf_new.emp_nbr and x.mth=ehf_new.mth and x.yr=ehf_new.yr
*/
where  (newaa.begins <= l.m_dt or l.m_dt is null)
and (newaa.begins >=f.act_start_dt or f.act_start_dt is null)
qualify (max(newaa.Active_Count) over (partition by newaa.emp_nbr)>0  )
order by newaa.EMP_NBR, newaa.Begins,newaa.end_dt;