with t1 as (
    select                                              
        trn_symb,                                       
        trn_type,                                       
        orig_dep_oprn_stn as from_os,                   
        dest_arvl_oprn_stn as to_os,                    
        trn_crew_dist_segm_valu as line_segment,
        count(*) as frequency
    from cpo.bss_trn_strt where trn_type <> 'YDJB' and  
      trn_symb is not null and                          
      trn_type is not null
    group by trn_symb, trn_type, orig_dep_oprn_stn, dest_arvl_oprn_stn, trn_crew_dist_segm_valu
)

select                                              
    trn_symb,                                       
    trn_type,                                       
    from_os,                   
    to_os,                    
    line_segment
from t1 
order by frequency desc
fetch first 1000 rows only
