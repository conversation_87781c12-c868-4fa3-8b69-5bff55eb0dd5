spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
spring.scpm.datasource.jdbcUrl=**************************************************

# ============================================================================================
# WPM-342: switching to SCPMQA for dflc and transit time distribution parameters.
# spring.scpmprod.datasource.jdbcUrl=******************************************************
spring.scpmprod.datasource.jdbcUrl=**************************************************
# ============================================================================================

spring.crewmf.datasource.jdbcUrl=*****************************************
tptUrl=http://or-opd-wpm-ws-svc:8099
jobmgrUrl=http://or-jobmgr:8099

logging.level.org.springframework=WARN
logging.level.root=WARN
logging.level.com.zaxxer.hikari=ERROR
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.org.hibernate=ERROR

spring.scpm.datasource.driver-class-name=com.ibm.db2.jcc.DB2Driver
spring.scpm.datasource.username=syaqt61

# Previous URL before 2025-05-08 upgrade:
# spring.crewproprod.datasource.jdbcUrl=****************************** = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = oracrewracprod.atldc.nscorp.com)(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = CREWPROD_SVC) (FAILOVER_MODE = (TYPE = SELECT) (METHOD = BASIC) (RETRIES = 180) (DELAY = 5))))
spring.crewproprod.datasource.jdbcUrl=******************************=(CONNECT_TIMEOUT=90)(RETRY_COUNT=30)(RETRY_DELAY=3)(TRANSPORT_CONNECT_TIMEOUT=3)(FAILOVER=ON)(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=oracplmprod.atldc.nscorp.com)(PORT=1521)))(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=oracplmadg.atldc.nscorp.com)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME=cplmprod_adg_svc)))

spring.crewproprod.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.crewproprod.datasource.username=ccpora

spring.datawarehouse.datasource.jdbcUrl=************************************************
spring.datawarehouse.datasource.driver-class-name=com.teradata.jdbc.TeraDriver
spring.datawarehouse.datasource.username=ccptd

spring.crewmf.datasource.driver-class-name=com.ibm.db2.jcc.DB2Driver
spring.crewmf.datasource.username=syaqt61

spring.scpmprod.datasource.driver-class-name=com.ibm.db2.jcc.DB2Driver
spring.scpmprod.datasource.username=syaqt61
