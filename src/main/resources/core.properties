
#Train viewer, plan edit feature
isEditEnabled=true

flatTransactions=false
PlanDAOImpl.numSaveLoadThreads=4
server.port = 8099
randomSeeds=8432012522670610539,105746020578780764,9077459243318311715,-8037160793083036582,207015571809310747,6558959944058474701,7952874786185296504,-1015280207642793639,8945920891992578061,-352976401011991655,2024734286407034370,-3386518804883411866,9200883739174917040,-9036335458281922728,-1641012412740907057,1330504495083059852,-881726820629546029,8466038898722046631,-6533076502117846638,4159369716806611739,4257612974978318121,-2037184476383922519,-371639498548921056,4741997711737152550,-6974643327490972980,1311672030013490049,969326581770484981,1793576985649979139,-4134187705664542025,-1426244288366533415,-383817587341081427,8245185705960627407,-3737216090488860085,-651822346025765282,6958130633926111627,7575682985095033576,-851072763741416245,6486684830192988225,-6189051905405306123,-7544021428715808911

skipTestInitialization=false

spring.profiles.active=test

defaultUsers=sys,ada06,pach8,ugd6p,lc9tz

opdTrnTypes=CE,CL,CS,CSAO,CSCD,CSHD,CSID,CSPC,CSPS,CSRL,EMGR,GRAN,HAUL,IE,IM,IMPM,IP,LDGR,LIAD,LICD,LIDD,LIGD,LIHD,LIID,LILD,LIMV,LIPC,LIPD,LIPS,LIVD,LSID,LSLD,MER,ML,NTCD,PE,PUSH,SD,SP,TC,UE,UF,UFE,UFL,UL,UNIT,UP,UPE,UPL,UT,UTIL,WB,WH,WI,WO,WR,WT,YB,YC,YDJB

CrewProJobDAOImpl.jobSql=select v1.job_id, v1.request_id, v1.request_filename, v1.response_filename, v1.request, v1.response, v1.receipt_ts, v1.status, v1.source from nsop.nsor_job_view v1 join (select request_id, max(job_id) as max_job_id from nsop.nsor_job_view where status in ('SUCCESS_SYNCED', 'FAILURE_SYNCED') and source='PST' and receipt_ts >= ? and receipt_ts < ? group by request_id) v2 on v1.job_id = v2.max_job_id order by v1.receipt_ts
CrewProJobDAOImpl.jobArchiveSql=select v1.job_id, v1.request_id, v1.request_filename, v1.response_filename, v1.request, v1.response, v1.receipt_ts, v1.status, v1.source from nsop.nsor_job_archive_view v1 join (select request_id, max(job_id) as max_job_id from nsop.nsor_job_archive_view where status in ('SUCCESS_SYNCED', 'FAILURE_SYNCED') and source='PST' and receipt_ts >= ? and receipt_ts < ? group by request_id) v2 on v1.job_id = v2.max_job_id order by v1.receipt_ts
CrewProJobDAOImpl.jobNoBlobsSql=select v1.job_id, v1.request_id, v1.request_filename, v1.response_filename, v1.receipt_ts, v1.status, v1.source from nsop.nsor_job_view v1 join (select request_id, max(job_id) as max_job_id from nsop.nsor_job_view where status in ('SUCCESS_SYNCED', 'FAILURE_SYNCED') and source='PST' and receipt_ts >= ? and receipt_ts < ? group by request_id) v2 on v1.job_id = v2.max_job_id order by v1.receipt_ts
CrewProJobDAOImpl.jobArchiveNoBlobsSql=select v1.job_id, v1.request_id, v1.request_filename, v1.response_filename, v1.receipt_ts, v1.status, v1.source from nsop.nsor_job_archive_view v1 join (select request_id, max(job_id) as max_job_id from nsop.nsor_job_archive_view where status in ('SUCCESS_SYNCED', 'FAILURE_SYNCED') and source='PST' and receipt_ts >= ? and receipt_ts < ? group by request_id) v2 on v1.job_id = v2.max_job_id order by v1.receipt_ts

LocalYardJobDAOImpl.getLocalTrainsSql=                                                                                                             \n\
SELECT DISTINCT                                                                                                                                    \n\
  p.TRN_ORGN_DT as trn_orgn_dt,                                                                                                                    \n\
  null as trn,                                                                                                                                     \n\
  null as trn_id,                                                                                                                                  \n\
  p.trn as trn_symb,                                                                                                                               \n\
  p.TRN_CREW_DISTR as trn_crew_distr,                                                                                                              \n\
  r.DEPART_OS AS from_os,                                                                                                                          \n\
  r.DEPART_TS as from_ts,                                                                                                                          \n\
  r.ARVL_OS AS to_os,                                                                                                                              \n\
  r.ARVL_TS as to_ts,                                                                                                                              \n\
  hgp.CC as cc,                                                                                                                                    \n\
  PROTECT_XB_DS as protect_xb_ds,                                                                                                                  \n\
  PROTECT_XB_SD as protect_xb_sd,                                                                                                                  \n\
  PROTECT_XB as protect_xb,                                                                                                                        \n\
  r.tt AS trn_type,                                                                                                                                \n\
  p.EMP_NBR as emp_nbr                                                                                                                             \n\
FROM                                                                                                                                               \n\
    CREW_PVIEW.ASGN_POSITIONS_H AS p                                                                                                               \n\
    JOIN CREW_PVIEW.ROAD_ASGN_H AS r ON p.DS = r.ds AND p.SD = r.sd AND p.POOL = r.pool AND p.ASGN_TS = r.asgn_ts AND p.TRAIN_ID = r.train_id      \n\
    JOIN (SELECT DISTINCT tbl.DS, tbl.SD, tbl.POOL , tbl.CC,  PROTECT_XB_DS, PROTECT_XB_SD, PROTECT_XB                                             \n\
        FROM crew_pview.HIRE_GROUP_POOL_H tbl                                                                                                      \n\
    WHERE (SNAPSHOT_MTH BETWEEN :startYYYYMM AND  :endYYYYMM )  ) hgp                                                                              \n\
ON hgp.ds = p.ds AND hgp.sd = p.sd AND hgp.pool = p.pool AND hgp.cc = p.cc                                                                         \n\
WHERE p.EMP_NBR <  900000000                                                                                                                       \n\
AND p.POOL <> '##'                                                                                                                                 \n\
AND p.DS NOT IN   ('cr','ih','np','ns','su','un','va','dm','ec','zz','ct','aw','tn')                                                               \n\
AND p.TRAIN_ID NOT LIKE 'TES%' 	AND r.DEPART_TS IS NOT NULL                                                                                        \n\
AND r.ARVL_TS IS NOT NULL 	AND p.CC IN ('EN', 'CO', 'B1', 'BK')                                                                                   \n\
AND p.TRN_ORGN_DT BETWEEN :startDate AND :endDate                                                                                                  \n\
AND REGULAR_ASGN_OR_XB_IND = 'E'                                                                                                                   \n\
AND ( (r.tt = 10)  OR  ( r.tt BETWEEN 60 AND 79) )                                                                                                 \n\
AND PROTECT_XB_DS IS NOT NULL 	AND PROTECT_XB_SD IS NOT NULL 	AND PROTECT_XB IS NOT NULL

# Note: 'trnCrewDistr' was originally hard-coded as 'YD'.
LocalYardJobDAOImpl.getYardJobsSql=                                                                                       \n\
SELECT DISTINCT                                                                                                           \n\
  p.ASGN_DT AS trn_orgn_dt,                                                                                               \n\
  p.train_id as trn,                                                                                                      \n\
  p.TRAIN_ID as trn_id,                                                                                                   \n\
  p.TRAIN_ID as trn_symb,                                                                                                 \n\
  'YD' AS trn_crew_distr,                                                                                                 \n\
  YARD_PROFILE_ORGN_OS AS from_os,                                                                                        \n\
  p.ASGN_TS AS from_ts,                                                                                                   \n\
  YARD_PROFILE_ORGN_OS AS to_os,                                                                                          \n\
  y.ARVL_TS as to_ts,                                                                                                     \n\
  hh.CC as cc,                                                                                                            \n\
  PROTECT_XB_DS as protect_xb_ds,                                                                                         \n\
  PROTECT_XB_SD as protect_xb_sd,                                                                                         \n\
  PROTECT_XB as protect_xb,                                                                                               \n\
  'YDJB' AS trn_type,                                                                                                     \n\
  p.EMP_NBR as emp_nbr                                                                                                    \n\
FROM                                                                                                                      \n\
CREW_PVIEW.ASGN_POSITIONS_H AS p                                                                                          \n\
JOIN CREW_PVIEW.YARD_ASGN_H AS y                                                                                          \n\
ON p.DS = y.ds AND p.SD = y.sd AND p.POOL = y.pool AND p.ASGN_TS = y.asgn_ts AND p.TRAIN_ID = y.JOB_ASGN                  \n\
JOIN  (  SELECT DISTINCT JOB_ASGN, CC, ASGN , PROTECT_XB_DS, PROTECT_XB_SD, PROTECT_XB                                    \n\
FROM crew_pview.HIRE_GROUP_ASSIGN_CC_H h   	WHERE (SNAPSHOT_MTH BETWEEN :startYYYYMM AND  :endYYYYMM) ) hh                \n\
ON  hh.JOB_ASGN=p.ON_DUTY_JOB_ASGN  AND hh.cc = p.ON_DUTY_CC                                                              \n\
WHERE p.EMP_NBR < 900000000                                                                                               \n\
AND p.POOL <>'##' AND  p.DS NOT IN ('cr','ih','np','ns','su','un','va','dm','ec','zz','ct','aw','tn')                     \n\
AND train_id NOT LIKE'TES%'  	AND y.ARVL_TS IS NOT NULL   	AND p.ASGN_TS IS NOT NULL                                 \n\
AND p.ON_DUTY_CC IN ('EN','CO','B1','BK')	                                                                              \n\
AND p.ASGN_TS BETWEEN :startDate  AND :endDate                                                                            \n\
AND REGULAR_ASGN_OR_XB_IND = 'E' AND p.pool = 'YD'                                                                        \n\
AND EXISTS ( SELECT 1 FROM crew_pview.HIRE_GROUP_POOL_H rd                                                                \n\
WHERE ( rd.SNAPSHOT_MTH BETWEEN :startYYYYMM AND  :endYYYYMM)                                                             \n\
AND rd. PROTECT_XB_DS = hh.PROTECT_XB_DS                                                                                  \n\
AND rd. PROTECT_XB_SD =  hh.PROTECT_XB_SD                                                                                 \n\
AND rd.PROTECT_XB =  hh.PROTECT_XB                                                                                        \n\
AND rd.cc = hh.cc)                                                                                                        \n\
AND PROTECT_XB_DS IS NOT NULL   AND PROTECT_XB_SD IS NOT NULL  AND PROTECT_XB IS NOT NULL

HistoricalTrainDAOImpl.getHistoricalRoadTrainsQuery1Sql=                                                                                                                             \n\
with t1 as(                                                                                                                                                                          \n\
SELECT    a.trn, a.trn_orgn_dt, a.trn_section, a.trn_crew_distr, a.from_loc_seq_nr,                                                                                                  \n\
                    a.from_os, a.sch_from_ts, from_ts, a.to_loc_seq_nr, a.to_os,                                                                                                     \n\
                    a.sch_to_ts, to_ts, s.trn_typ AS trn_type                                                                                                                        \n\
            FROM    NS_PROD_DATA.ACT_TRN_CREW_SEG a, NS_PROD_DATA.sch_trn_symbol s                                                                                                   \n\
            WHERE    a.trn = s.trn                                                                                                                                                   \n\
                AND    a.trn_section = s.trn_section                                                                                                                                 \n\
                AND    a.trn_orgn_dt = s.trn_orgn_dt                                                                                                                                 \n\
                AND    a.trn_orgn_dt BETWEEN ':startDate' AND ':endDate'                                                                                                             \n\
                AND    (SCH_TO_TS IS NOT NULL                                                                                                                                        \n\
                AND    SCH_FROM_TS IS NOT NULL)                                                                                                                                      \n\
                AND    SCH_TO_TS > SCH_FROM_TS                                                                                                                                       \n\
                AND    a.trn < 'A'                                                                                                                                                   \n\
                AND    a.ANNUL_CT=0                                                                                                                                                  \n\
                AND    ( a.TRN_CREW_DISTR, a.TRN, a.TRN_ORGN_DT, a.TRN_SECTION ) NOT IN (                                                                                            \n\
            SELECT    TRN_CREW_DISTR, COMBO_FROM_TRN, COMBO_FROM_TRN_ORGN_DT,                                                                                                        \n\
                    COMBO_FROM_TRN_SECTION                                                                                                                                           \n\
            FROM    NS_PROD_DATA.ACT_TRN_CREW_SEG                                                                                                                                    \n\
            WHERE    (TRN_ORGN_DT BETWEEN ADD_MONTHS(':startDate', -1)                                                                                                               \n\
                AND    ADD_MONTHS(':endDate', 1))                                                                                                                                    \n\
                AND COMBO_FROM_TRN IS NOT NULL)                                                                                                                                      \n\
            GROUP BY    a.TRN, a.TRN_ORGN_DT, a.TRN_SECTION, a.TRN_CREW_DISTR,                                                                                                       \n\
                    a.FROM_LOC_SEQ_NR, a.FROM_OS, a.SCH_FROM_TS, FROM_TS, a.TO_LOC_SEQ_NR,                                                                                           \n\
                    a.TO_OS, a.SCH_TO_TS, TO_TS, s.trn_typ                                                                                                                           \n\
),                                                                                                                                                                                   \n\
t3 as(                                                                                                                                                                               \n\
select a.os, a.loc_seq_nr, b.*                                                                                                                                                       \n\
from NS_PROD_DATA.TRN_LOC_EVENT_H a                                                                                                                                                  \n\
right join(                                                                                                                                                                          \n\
select t1.trn, t1.trn_orgn_dt, t1.trn_section, t1.trn_crew_distr, min(t2.event_ts) as act_from_ts                                                                                    \n\
from NS_PROD_DATA.ACT_TRN_CREW_SEG t1, NS_PROD_DATA.TRN_LOC_EVENT_H t2                                                                                                               \n\
where t1.trn = t2.trn                                                                                                                                                                \n\
and t1.trn_orgn_dt = t2.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t2.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t2.trn_crew_distr                                                                                                                                            \n\
and t2.event_ts is not null                                                                                                                                                          \n\
and t2.trn_loc_event_type = 'DFLC'                                                                                                                                                   \n\
group by t1.trn, t1.trn_orgn_dt, t1.trn_section, t1.trn_crew_distr) b                                                                                                                \n\
on a.trn = b.trn and a.trn_orgn_dt = b.trn_orgn_dt and a.trn_section = b.trn_section and a.trn_crew_distr = b.trn_crew_distr and a.event_ts = b.act_from_ts                          \n\
),                                                                                                                                                                                   \n\
t4 as (                                                                                                                                                                              \n\
select a.os, a.loc_seq_nr, b.*                                                                                                                                                       \n\
from NS_PROD_DATA.TRN_LOC_EVENT_H a                                                                                                                                                  \n\
right join(                                                                                                                                                                          \n\
select t1.trn, t1.trn_orgn_dt, t1.trn_section, t1.trn_crew_distr, max(t2.event_ts) as act_to_ts                                                                                      \n\
from NS_PROD_DATA.ACT_TRN_CREW_SEG t1, NS_PROD_DATA.TRN_LOC_EVENT_H t2                                                                                                               \n\
where t1.trn = t2.trn                                                                                                                                                                \n\
and t1.trn_orgn_dt = t2.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t2.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t2.trn_crew_distr                                                                                                                                            \n\
and t2.event_ts is not null                                                                                                                                                          \n\
and t2.trn_loc_event_type = 'ARIL'                                                                                                                                                   \n\
group by t1.trn, t1.trn_orgn_dt, t1.trn_section, t1.trn_crew_distr) b                                                                                                                \n\
on a.trn = b.trn and a.trn_orgn_dt = b.trn_orgn_dt and a.trn_section = b.trn_section and a.trn_crew_distr = b.trn_crew_distr and a.event_ts = b.act_to_ts                            \n\
),                                                                                                                                                                                   \n\
tt as (                                                                                                                                                                              \n\
select distinct t.trn, t.trn_orgn_dt, t.trn_section, t.trn_crew_distr, t.act_from_loc_seq_nr,                                                                                        \n\
t.act_from_os, t.act_from_ts, t.act_to_loc_seq_nr, t.act_to_os, t.act_to_ts, t.trn_type,                                                                                             \n\
t.crew_profile_now as crew_profile, t.crew_orgn_os_now as crew_orgn_os, profile_count_now from (                                                                                     \n\
select t1.*, case when t3.act_from_ts is null or t4.act_to_ts is null then t1.from_loc_seq_nr else t3.loc_seq_nr end as act_from_loc_seq_nr,                                         \n\
case when t3.act_from_ts is null or t4.act_to_ts is null then t1.from_os else t3.os end as act_from_os,                                                                              \n\
case when t3.act_from_ts is null or t4.act_to_ts is null then t1.sch_from_ts else t3.act_from_ts end as act_from_ts,                                                                 \n\
case when t3.act_from_ts is null or t4.act_to_ts is null then t1.to_loc_seq_nr else t4.loc_seq_nr end as act_to_loc_seq_nr,                                                          \n\
case when t3.act_from_ts is null or t4.act_to_ts is null then t1.to_os else t4.os end as act_to_os,                                                                                  \n\
case when t3.act_from_ts is null or t4.act_to_ts is null then t1.sch_to_ts else t4.act_to_ts end as act_to_ts,                                                                       \n\
t5.road_profile as crew_profile_now, t5.road_profile_orgn_os as crew_orgn_os_now, t7.profile_count_now                                                                               \n\
from t1                                                                                                                                                                              \n\
left join t3                                                                                                                                                                         \n\
on t1.trn = t3.trn                                                                                                                                                                   \n\
and t1.trn_orgn_dt = t3.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t3.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t3.trn_crew_distr                                                                                                                                            \n\
left join t4                                                                                                                                                                         \n\
on t1.trn = t4.trn                                                                                                                                                                   \n\
and t1.trn_orgn_dt = t4.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t4.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t4.trn_crew_distr                                                                                                                                            \n\
left join (                                                                                                                                                                          \n\
select trn, trn_orgn_dt, trn_section, trn_crew_distr, road_profile, road_profile_orgn_os from CREW_PVIEW.ROAD_ASGN_H where trn_orgn_day<32) t5                                       \n\
on t1.trn = t5.trn                                                                                                                                                                   \n\
and t1.trn_orgn_dt = t5.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t5.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t5.trn_crew_distr                                                                                                                                            \n\
left join (                                                                                                                                                                          \n\
select trn, trn_section, trn_orgn_dt, trn_crew_distr, count(trn) as profile_count_now from CREW_PVIEW.ROAD_ASGN_H where trn_orgn_day <32                                             \n\
group by trn, trn_section, trn_orgn_dt, trn_crew_distr) t7                                                                                                                           \n\
on t1.trn = t7.trn                                                                                                                                                                   \n\
and t1.trn_orgn_dt = t7.trn_orgn_dt                                                                                                                                                  \n\
and t1.trn_section = t7.trn_section                                                                                                                                                  \n\
and t1.trn_crew_distr = t7.trn_crew_distr                                                                                                                                            \n\
) t                                                                                                                                                                                  \n\
),                                                                                                                                                                                   \n\
fromLoc as (                                                                                                                                                                         \n\
select tt.trn, tt.trn_orgn_dt, tt.trn_section, tt.trn_crew_distr, tt.act_from_ts, tt.trn_type,                                                                                       \n\
min(tt.act_from_loc_seq_nr) as act_from_loc_seq_nr                                                                                                                                   \n\
from tt                                                                                                                                                                              \n\
group by tt.trn, tt.trn_orgn_dt, tt.trn_section, tt.trn_crew_distr, tt.act_from_ts, tt.trn_type                                                                                      \n\
),                                                                                                                                                                                   \n\
toLoc as (                                                                                                                                                                           \n\
select tt.trn, tt.trn_orgn_dt, tt.trn_section, tt.trn_crew_distr, tt.act_to_ts, tt.trn_type,                                                                                         \n\
min(tt.act_to_loc_seq_nr) as act_to_loc_seq_nr                                                                                                                                       \n\
from tt                                                                                                                                                                              \n\
group by tt.trn, tt.trn_orgn_dt, tt.trn_section, tt.trn_crew_distr, tt.act_to_ts, tt.trn_type                                                                                        \n\
)                                                                                                                                                                                    \n\
select distinct                                                                                                                                                                      \n\
  tb1.trn as trn,                                                                                                                                                                    \n\
  tb1.trn_orgn_dt as trnOrgnDt,                                                                                                                                                      \n\
  tb1.trn_section as trnSection,                                                                                                                                                     \n\
  tb1.trn_crew_distr as trnCrewDistr,                                                                                                                                                \n\
  tb1.act_from_loc_seq_nr as fromLocSeqNr,                                                                                                                                           \n\
  tt.act_from_os as fromOs,                                                                                                                                                          \n\
  tb1.act_from_ts as fromTs,                                                                                                                                                         \n\
  tb1.act_to_loc_seq_nr toLocSeqNr,                                                                                                                                                  \n\
  tb1.act_to_ts toTs,                                                                                                                                                                \n\
  tt.act_to_os toOs,                                                                                                                                                                 \n\
  tb1.trn_type as trnType,                                                                                                                                                           \n\
  tt.profile_count_now profileCountNow,                                                                                                                                              \n\
  case when tt.profile_count_now <> 1 then null else tt.crew_profile end as profileId,                                                                                               \n\
  case when tt.profile_count_now <> 1 then null else tt.crew_orgn_os end as crewOrgnOs                                                                                               \n\
from (                                                                                                                                                                               \n\
select fromLoc.trn, fromLoc.trn_orgn_dt, fromLoc.trn_section, fromLoc.trn_crew_distr,                                                                                                \n\
fromLoc.act_from_loc_seq_nr, fromLoc.act_from_ts, toLoc.act_to_ts, toLoc.act_to_loc_seq_nr,                                                                                          \n\
fromLoc.trn_type                                                                                                                                                                     \n\
from fromLoc                                                                                                                                                                         \n\
left join toLoc                                                                                                                                                                      \n\
on fromLoc.trn = toLoc.trn and fromLoc.trn_orgn_dt = toLoc.trn_orgn_dt and                                                                                                           \n\
fromLoc.trn_section = toLoc.trn_section and fromLoc.trn_crew_distr = toLoc.trn_crew_distr) tb1                                                                                       \n\
left join tt                                                                                                                                                                         \n\
on tb1.trn = tt.trn and tb1.trn_orgn_dt = tt.trn_orgn_dt and tb1.trn_section = tt.trn_section and                                                                                    \n\
tb1.trn_crew_distr = tt.trn_crew_distr and tb1.act_from_ts = tt.act_from_ts and                                                                                                      \n\
tb1.act_from_loc_seq_nr = tt.act_from_loc_seq_nr and tb1.act_to_ts = tt.act_to_ts                                                                                                    \n\
and tb1.act_to_loc_seq_nr = tt.act_to_loc_seq_nr                                                                                                                                     \n\
order by tb1.trn, tb1.trn_orgn_dt, tb1.trn_crew_distr, tb1.act_from_loc_seq_nr

HistoricalTrainDAOImpl.getHistoricalRoadTrainsQuery2Sql=                                      \n\
select                                                                                        \n\
    t1.trn as trn,                                                                            \n\
    t1.trn_orgn_dt as trnOrgnDt,                                                              \n\
    t1.trn_section as trnSection,                                                             \n\
    t1.trn_crew_distr as trnCrewDistr,                                                        \n\
    t1.profile_count_now as profileCountNow,                                                  \n\
    t2.road_profile as profileId,                                                             \n\
    t2.road_profile_orgn_os as crewOrgnOs                                                     \n\
from (                                                                                        \n\
    select                                                                                    \n\
        trn,                                                                                  \n\
        trn_section,                                                                          \n\
        trn_orgn_dt,                                                                          \n\
        trn_crew_distr,                                                                       \n\
        count(trn) as profile_count_now                                                       \n\
    from CREW_PVIEW.ROAD_ASGN_H where trn_orgn_day <32                                        \n\
        and concat(trim(trn), '-', trim(trn_crew_distr), '-', trn_section) in :inClause       \n\
    group by trn, trn_section, trn_orgn_dt, trn_crew_distr) t1                                \n\
    left join                                                                                 \n\
        (select * from CREW_PVIEW.ROAD_ASGN_H where trn_orgn_day <32) t2                      \n\
    on                                                                                        \n\
        t1.trn = t2.trn and                                                                   \n\
        t1.trn_section = t2.trn_section and                                                   \n\
        t1.trn_orgn_dt = t2.trn_orgn_dt and                                                   \n\
        t1.trn_crew_distr = t2.trn_crew_distr

CrewProTrainDownloaderDAOImpl.downloadTrainsSql=                      \n\
    select                                                            \n\
        tsr_crew_stn as tsrCrewStation,                               \n\
        trn_symb as trnSymb,                                          \n\
        trn_orig_dt as trnOrgnDt,                                     \n\
        trn_crew_dist_segm_valu as trnCrewDistr,                      \n\
        orig_dep_oprn_stn as fromOs                                   \n\
    from cpo.nsor_bss_trn_strt_view                                             \n\
    where tsr_crew_stn is not null and                                \n\
          last_updt_ts >= :lastUpdtTs and                             \n\
          load_ts = (select max(load_ts) from cpo.nsor_bss_trn_strt_view)

spring.main.allow-bean-definition-overriding=true

TrainBalance.getTrainBalanceSimulationTotalsByScenarioId=                      \n\
WITH t1                                                                        \n\
     AS (SELECT crew_os,                                                       \n\
                trn_crew_distr,                                                \n\
                trn_type,                                                      \n\
                COUNT(trn) AS outbound                                         \n\
         FROM   simulation_train_output                                        \n\
         WHERE  scenario_id = :scenarioId                                      \n\
                AND crew_os = from_os                                          \n\
         GROUP  BY crew_os,                                                    \n\
                   from_os,                                                    \n\
                   trn_crew_distr,                                             \n\
                   trn_type),                                                  \n\
     t2                                                                        \n\
     AS (SELECT crew_os,                                                       \n\
                trn_crew_distr,                                                \n\
                trn_type,                                                      \n\
                COUNT(trn) AS inbound                                          \n\
         FROM   simulation_train_output                                        \n\
         WHERE  scenario_id = :scenarioId                                      \n\
                AND crew_os = to_os                                            \n\
         GROUP  BY crew_os,                                                    \n\
                   to_os,                                                      \n\
                   trn_crew_distr,                                             \n\
                   trn_type)                                                   \n\
SELECT CASE                                                                    \n\
         WHEN t1.crew_os IS NULL THEN t2.crew_os                               \n\
         ELSE t1.crew_os                                                       \n\
       END AS crew_os,                                                         \n\
       CASE                                                                    \n\
         WHEN t1.trn_crew_distr IS NULL THEN t2.trn_crew_distr                 \n\
         ELSE t1.trn_crew_distr                                                \n\
       END AS line_segment,                                                    \n\
       CASE                                                                    \n\
         WHEN t1.trn_type IS NULL THEN t2.trn_type                             \n\
         ELSE t1.trn_type                                                      \n\
       END AS train_type,                                                      \n\
       CASE                                                                    \n\
         WHEN t2.inbound IS NULL THEN 0                                        \n\
         ELSE t2.inbound                                                       \n\
       END AS inbound,                                                         \n\
       CASE                                                                    \n\
         WHEN t1.outbound IS NULL THEN 0                                       \n\
         ELSE t1.outbound                                                      \n\
       END AS outbound,                                                        \n\
       CASE                                                                    \n\
         WHEN t2.inbound IS NULL THEN t1.outbound                              \n\
         ELSE                                                                  \n\
           CASE                                                                \n\
             WHEN t1.outbound IS NULL THEN -t2.inbound                         \n\
             ELSE t1.outbound - t2.inbound                                     \n\
           END                                                                 \n\
       END AS difference                                                       \n\
FROM   t1                                                                      \n\
       FULL OUTER JOIN t2                                                      \n\
                    ON t1.crew_os = t2.crew_os                                 \n\
                       AND t1.trn_crew_distr = t2.trn_crew_distr               \n\
                       AND t1.trn_type = t2.trn_type 

TrainBalance.getTrainBalanceSimulationAvgsByScenarioId=								\n\
WITH t1                                                                             \n\
     AS (SELECT crew_os,                                                            \n\
                trn_crew_distr,                                                     \n\
                trn_type,                                                           \n\
                COUNT(trn) AS outbound                                              \n\
         FROM   simulation_train_output                                             \n\
         WHERE  scenario_id = :scenarioId                                           \n\
                AND crew_os = from_os                                               \n\
         GROUP  BY crew_os,                                                         \n\
                   trn_crew_distr,                                                  \n\
                   trn_type),                                                       \n\
     t2                                                                             \n\
     AS (SELECT crew_os,                                                            \n\
                trn_crew_distr,                                                     \n\
                trn_type,                                                           \n\
                COUNT(trn) AS inbound                                               \n\
         FROM   simulation_train_output                                             \n\
         WHERE  scenario_id = :scenarioId                                           \n\
                AND crew_os = to_os                                                 \n\
         GROUP  BY crew_os,                                                         \n\
                   trn_crew_distr,                                                  \n\
                   trn_type)                                                        \n\
SELECT CASE                                                                         \n\
         WHEN t1.crew_os IS NULL THEN t2.crew_os                                    \n\
         ELSE t1.crew_os                                                            \n\
       END AS crew_os,                                                              \n\
       CASE                                                                         \n\
         WHEN t1.trn_crew_distr IS NULL THEN t2.trn_crew_distr                      \n\
         ELSE t1.trn_crew_distr                                                     \n\
       END AS line_segment,                                                         \n\
       CASE                                                                         \n\
         WHEN t1.trn_type IS NULL THEN t2.trn_type                                  \n\
         ELSE t1.trn_type                                                           \n\
       END AS train_type,                                                           \n\
       CASE                                                                         \n\
         WHEN t2.inbound IS NULL THEN 0                                             \n\
         ELSE CAST(CAST(t2.inbound AS DECIMAL(10, 2)) / CAST(                       \n\
                               (SELECT CASE                                         \n\
                                                                       WHEN         \n\
                               s.stats_end_date = s.stats_start_date THEN 1.0       \n\
                                                                       ELSE (       \n\
                                       Nvl2(s.stats_end_date,                       \n\
                                       DAYS(s.stats_end_date), 0) -                 \n\
                               Nvl2(s.stats_start_date,                             \n\
                               DAYS(s.stats_start_date),                            \n\
                               0) )                                                 \n\
                                / 7.0                                               \n\
                                                                     END            \n\
                                                              FROM                  \n\
                               scenario_cfg s                                       \n\
                                                              WHERE                 \n\
                               s.scenario_id = :scenarioId)                         \n\
                               AS                                                   \n\
                               DECIMAL(12, 4)) AS DECIMAL(10, 2))                   \n\
       END AS inbound,                                                              \n\
       CASE                                                                         \n\
         WHEN t1.outbound IS NULL THEN 0                                            \n\
         ELSE CAST(CAST(t1.outbound AS DECIMAL(10, 2)) / CAST(                      \n\
                               (SELECT CASE                                         \n\
                                                                        WHEN        \n\
                               s.stats_end_date = s.stats_start_date THEN 1.0       \n\
                                                                        ELSE (      \n\
                                       Nvl2(s.stats_end_date,                       \n\
                                       DAYS(s.stats_end_date), 0                    \n\
                                       ) -                                          \n\
                                              Nvl2(s.stats_start_date,              \n\
                                              DAYS(s.stats_start_date),             \n\
                                              0) )                                  \n\
                                                                             /      \n\
                                       7.0                                          \n\
                                                                      END           \n\
                                                               FROM                 \n\
                               scenario_cfg s                                       \n\
                                                               WHERE                \n\
                               s.scenario_id = :scenarioId)                         \n\
                               AS DECIMAL(12, 4)) AS DECIMAL(10, 2))                \n\
       END AS outbound,                                                             \n\
       CASE                                                                         \n\
         WHEN t2.inbound IS NULL THEN CAST(                                         \n\
                                        CAST(t1.outbound AS DECIMAL(10, 2)) /       \n\
                                        CAST((                                      \n\
                                        SELECT                                      \n\
         CASE                                                                       \n\
                   WHEN                                                             \n\
                                        s.stats_end_date = s.stats_start_date       \n\
         THEN                                                                       \n\
                   1.0                                                              \n\
                                                         ELSE                       \n\
         ( Nvl2(s.stats_end_date,                                                   \n\
           DAYS(s.stats_end_date),                                                  \n\
           0) -                                                                     \n\
         Nvl2(s.stats_start_date, DAYS(s.stats_start_date),                         \n\
         0) )                                                                       \n\
                               / 7.0                                                \n\
                        END                                                         \n\
                 FROM   scenario_cfg s                                              \n\
                 WHERE  s.scenario_id = :scenarioId) AS DECIMAL(12, 4)) AS          \n\
         DECIMAL(10, 2))                                                            \n\
         ELSE                                                                       \n\
           CASE                                                                     \n\
             WHEN t1.outbound IS NULL THEN CAST(                                    \n\
                                             CAST(-t2.inbound AS DECIMAL(10, 2))    \n\
                                           / CAST                                   \n\
                                             ((                                     \n\
                                             SELECT                                 \n\
             CASE                                                                   \n\
                      WHEN                                                          \n\
                      s.stats_end_date = s.stats_start_date THEN                    \n\
                                      1.0                                           \n\
                                      ELSE                                          \n\
             ( Nvl2(s.stats_end_date,                                               \n\
             DAYS(s.stats_end_date),                                                \n\
             0) -                                                                   \n\
           Nvl2(s.stats_start_date,                                                 \n\
           DAYS(s.stats_start_date),                                                \n\
           0) )                                                                     \n\
           / 7.0                                                                    \n\
           END                                                                      \n\
           FROM   scenario_cfg s                                                    \n\
           WHERE  s.scenario_id = :scenarioId) AS DECIMAL(12, 4)) AS                \n\
           DECIMAL(10, 2))                                                          \n\
             ELSE CAST(CAST(t1.outbound - t2.inbound AS DECIMAL(10, 2)) /           \n\
                                   CAST((SELECT CASE                                \n\
                                      WHEN s.stats_end_date = s.stats_start_date    \n\
                                                THEN                                \n\
                                      1.0                                           \n\
                                      ELSE ( Nvl2(s.stats_end_date,                 \n\
                                             DAYS(s.stats_end_date),                \n\
                                             0) -                                   \n\
                                                    Nvl2(s.stats_start_date,        \n\
                                                    DAYS(s.stats_start_date),       \n\
                                                    0) )                            \n\
                                           / 7.0                                    \n\
                                    END                                             \n\
                                        FROM                                        \n\
                                        scenario_cfg s                              \n\
                                        WHERE                                       \n\
                                        s.scenario_id = :scenarioId) AS DECIMAL(    \n\
                                        12, 4))                                     \n\
                       AS                                                           \n\
                       DECIMAL                                                      \n\
                              (10, 2))                                              \n\
           END                                                                      \n\
       END AS difference                                                            \n\
FROM   t1                                                                           \n\
       FULL OUTER JOIN t2                                                           \n\
                    ON t1.crew_os = t2.crew_os                                      \n\
                       AND t1.trn_crew_distr = t2.trn_crew_distr                    \n\
                       AND t1.trn_type = t2.trn_type                                

TrainBalance.getTrainBalanceInputTrnTotalsByStudyId=                                  \n\
WITH t1                                                                               \n\
     AS (SELECT crew_orgn_os,                                                         \n\
                trn_crew_distr,                                                       \n\
                trn_type,                                                             \n\
                COUNT(trn) AS outbound                                                \n\
         FROM   train                                                                 \n\
         WHERE  study_id = :studyId                                                   \n\
                AND (ASSIGN_FLAG IS NULL OR ASSIGN_FLAG = 0)                          \n\
                AND crew_orgn_os = from_os                                            \n\
         GROUP  BY crew_orgn_os,                                                      \n\
                   trn_crew_distr,                                                    \n\
                   trn_type),                                                         \n\
     t2                                                                               \n\
     AS (SELECT crew_orgn_os,                                                         \n\
                trn_crew_distr,                                                       \n\
                trn_type,                                                             \n\
                COUNT(trn) AS inbound                                                 \n\
         FROM   train                                                                 \n\
         WHERE  study_id = :studyId                                                   \n\
                AND (ASSIGN_FLAG IS NULL OR ASSIGN_FLAG = 0)                          \n\
                AND crew_orgn_os = to_os                                              \n\
         GROUP  BY crew_orgn_os,                                                      \n\
                   to_os,                                                             \n\
                   trn_crew_distr,                                                    \n\
                   trn_type)                                                          \n\
SELECT CASE                                                                           \n\
         WHEN t1.crew_orgn_os IS NULL THEN t2.crew_orgn_os                            \n\
         ELSE t1.crew_orgn_os                                                         \n\
       END AS crew_os,                                                                \n\
       CASE                                                                           \n\
         WHEN t1.trn_crew_distr IS NULL THEN t2.trn_crew_distr                        \n\
         ELSE t1.trn_crew_distr                                                       \n\
       END AS line_segment,                                                           \n\
       CASE                                                                           \n\
         WHEN t1.trn_type IS NULL THEN t2.trn_type                                    \n\
         ELSE t1.trn_type                                                             \n\
       END AS train_type,                                                             \n\
       CASE                                                                           \n\
         WHEN t2.inbound IS NULL THEN 0                                               \n\
         ELSE t2.inbound                                                              \n\
       END AS inbound,                                                                \n\
       CASE                                                                           \n\
         WHEN t1.outbound IS NULL THEN 0                                              \n\
         ELSE t1.outbound                                                             \n\
       END AS outbound,                                                               \n\
       CASE                                                                           \n\
         WHEN t2.inbound IS NULL THEN t1.outbound                                     \n\
         ELSE                                                                         \n\
           CASE                                                                       \n\
             WHEN t1.outbound IS NULL THEN -t2.inbound                                \n\
             ELSE t1.outbound - t2.inbound                                            \n\
           END                                                                        \n\
       END AS difference                                                              \n\
FROM   t1                                                                             \n\
       FULL OUTER JOIN t2                                                             \n\
                    ON t1.crew_orgn_os = t2.crew_orgn_os                              \n\
                       AND t1.trn_crew_distr = t2.trn_crew_distr                      \n\
                       AND t1.trn_type = t2.trn_type

TrainBalance.getTrainBalanceInputTrnAvgsByStudyId=                                    \n\
WITH t1                                                                               \n\
     AS (SELECT crew_orgn_os,                                                         \n\
                trn_crew_distr,                                                       \n\
                trn_type,                                                             \n\
                COUNT(trn) AS outbound                                                \n\
         FROM   train                                                                 \n\
         WHERE  study_id = :studyId                                                   \n\
                AND (ASSIGN_FLAG IS NULL OR ASSIGN_FLAG = 0)                          \n\
                AND crew_orgn_os = from_os                                            \n\
         GROUP  BY crew_orgn_os,                                                      \n\
                   from_os,                                                           \n\
                   trn_crew_distr,                                                    \n\
                   trn_type),                                                         \n\
     t2                                                                               \n\
     AS (SELECT crew_orgn_os,                                                         \n\
                trn_crew_distr,                                                       \n\
                trn_type,                                                             \n\
                COUNT(trn) AS inbound                                                 \n\
         FROM   train                                                                 \n\
         WHERE  study_id = :studyId                                                   \n\
                AND (ASSIGN_FLAG IS NULL OR ASSIGN_FLAG = 0)                          \n\
                AND crew_orgn_os = to_os                                              \n\
         GROUP  BY crew_orgn_os,                                                      \n\
                   trn_crew_distr,                                                    \n\
                   trn_type)                                                          \n\
SELECT CASE                                                                           \n\
         WHEN t1.crew_orgn_os IS NULL THEN t2.crew_orgn_os                            \n\
         ELSE t1.crew_orgn_os                                                         \n\
       END AS crew_os,                                                                \n\
       CASE                                                                           \n\
         WHEN t1.trn_crew_distr IS NULL THEN t2.trn_crew_distr                        \n\
         ELSE t1.trn_crew_distr                                                       \n\
       END AS line_segment,                                                           \n\
       CASE                                                                           \n\
         WHEN t1.trn_type IS NULL THEN t2.trn_type                                    \n\
         ELSE t1.trn_type                                                             \n\
       END AS train_type,                                                             \n\
       CASE                                                                           \n\
         WHEN t2.inbound IS NULL THEN 0                                               \n\
         ELSE CAST(CAST(t2.inbound AS DECIMAL(10, 2)) / CAST((SELECT CASE             \n\
                                                                       WHEN           \n\
                               s.end_date = s.start_date THEN 1.0                     \n\
                                                                       ELSE (         \n\
                   Nvl2(s.end_date, DAYS(s.end_date), 0) -                            \n\
                          Nvl2(s.start_date, DAYS(s.start_date), 0) ) / 7.0           \n\
                    END                                                               \n\
                   FROM   study s                                                     \n\
                   WHERE  s.study_id = :studyId) AS DECIMAL(12, 4)) AS DECIMAL(       \n\
                   10, 2))                                                            \n\
       END AS inbound,                                                                \n\
       CASE                                                                           \n\
         WHEN t1.outbound IS NULL THEN 0                                              \n\
         ELSE CAST(CAST(t1.outbound AS DECIMAL(10, 2)) / CAST((SELECT CASE            \n\
                                                                        WHEN          \n\
                               s.end_date = s.start_date THEN 1.0                     \n\
                                                                        ELSE (        \n\
                   Nvl2(s.end_date, DAYS(s.end_date), 0) -                            \n\
                          Nvl2(s.start_date, DAYS(s.start_date), 0) ) / 7.0           \n\
                    END                                                               \n\
                   FROM   study s                                                     \n\
                   WHERE  s.study_id = :studyId) AS DECIMAL(12, 4)) AS DECIMAL(       \n\
                   10, 2))                                                            \n\
       END AS outbound,                                                               \n\
       CASE                                                                           \n\
         WHEN t2.inbound IS NULL THEN CAST(CAST(t1.outbound AS DECIMAL(10, 2)) /      \n\
                                           CAST(                                      \n\
                                           (SELECT CASE                               \n\
                                                     WHEN                             \n\
       s.end_date = s.start_date THEN 1.0                                             \n\
                        ELSE (                                                        \n\
       Nvl2(s.end_date, DAYS(s.end_date), 0) -                                        \n\
       Nvl2(s.start_date, DAYS(s.start_date), 0) ) / 7.0                              \n\
       END                                                                            \n\
       FROM   study s                                                                 \n\
       WHERE  s.study_id = :studyId) AS DECIMAL(12, 4)) AS DECIMAL(10, 2))            \n\
         ELSE                                                                         \n\
           CASE                                                                       \n\
             WHEN t1.outbound IS NULL THEN CAST(CAST(-t2.inbound AS                   \n\
                                                     DECIMAL(10, 2)) /                \n\
                                                CAST(                                 \n\
                                                (SELECT CASE                          \n\
                                                          WHEN                        \n\
           s.end_date = s.start_date THEN 1.0                                         \n\
                           ELSE (                                                     \n\
           Nvl2(s.end_date, DAYS(s.end_date), 0) -                                    \n\
           Nvl2(s.start_date, DAYS(s.start_date), 0) ) / 7.0                          \n\
           END                                                                        \n\
           FROM   study s                                                             \n\
           WHERE  s.study_id = :studyId) AS DECIMAL(12, 4)) AS DECIMAL(10, 2))        \n\
             ELSE CAST(CAST(( t1.outbound - t2.inbound ) AS DECIMAL(10, 2)) /         \n\
                                   CAST((SELECT CASE                                  \n\
                                      WHEN s.end_date = s.start_date THEN 1.0         \n\
                                      ELSE (                                          \n\
Nvl2(s.end_date, DAYS(s.end_date), 0) -                                               \n\
     Nvl2(s.start_date, DAYS(s.start_date), 0) ) /                                    \n\
        7.0                                                                           \n\
 END                                                                                  \n\
     FROM                                                                             \n\
     study s                                                                          \n\
     WHERE                                                                            \n\
     s.study_id = :studyId) AS DECIMAL(12, 4)) AS                                     \n\
DECIMAL(10, 2))                                                                       \n\
END                                                                                   \n\
END AS difference                                                                     \n\
FROM   t1                                                                             \n\
       FULL OUTER JOIN t2                                                             \n\
                    ON t1.crew_orgn_os = t2.crew_orgn_os                              \n\
                       AND t1.trn_crew_distr = t2.trn_crew_distr                      \n\
                       AND t1.trn_type = t2.trn_type 


TrainDelay.getTrainDelayDistrictDurationByScenarioIdTrainType=                      \n\
SELECT distr,                                                   \n\
       trn_crew_distr                         AS line_segment,  \n\
       CAST(delay_duration AS DECIMAL(11, 2)) AS delay_duration, \n\
       CAST(DELAY_PERCENTAGE AS DECIMAL(11, 2)) AS delay_percentage \n\
FROM trn_delay_stats                                            \n\
WHERE scenario_id = :scenarioId                                 \n\
  AND ((:trainType IS NULL AND TRN_TYPE IS NULL)                \n\
    OR (:trainType IS NOT NULL AND TRN_TYPE = :trainType)       \n\
    )                                                           \n\
  AND trn_crew_distr IS NULL                                    \n\
  AND distr IS NOT NULL                                         \n\
ORDER BY distr

TrainDelay.getTrainDelayLineSegDurationByScenarioIdTrainType=   \n\
SELECT distr,                                                   \n\
       trn_crew_distr                         AS line_segment,  \n\
       CAST(delay_duration AS DECIMAL(11, 2)) AS delay_duration, \n\
       CAST(DELAY_PERCENTAGE AS DECIMAL(11, 2)) AS delay_percentage \n\
FROM trn_delay_stats                                            \n\
WHERE scenario_id = :scenarioId                                 \n\
  AND ((:trainType IS NULL AND TRN_TYPE IS NULL)                \n\
    OR (:trainType IS NOT NULL AND TRN_TYPE = :trainType)       \n\
    )                                                           \n\
  AND trn_crew_distr IS NOT NULL                                \n\
  AND distr IS NULL                                             \n\
ORDER BY trn_crew_distr

TrainDelay.getTrainDelayDistrictPercentageByScenarioIdTrainType=                       \n\
SELECT distr,                                                   \n\
       trn_crew_distr                         AS line_segment,  \n\
       CAST(delay_duration AS DECIMAL(11, 2)) AS delay_duration, \n\
       CAST(DELAY_PERCENTAGE AS DECIMAL(11, 2)) AS delay_percentage \n\
FROM trn_delay_stats                                                \n\
WHERE scenario_id = :scenarioId                                     \n\
  AND ((:trainType IS NULL AND TRN_TYPE IS NULL)                    \n\
    OR (:trainType IS NOT NULL AND TRN_TYPE = :trainType)           \n\
    )                                                               \n\
  AND trn_crew_distr IS NULL                                        \n\
  AND distr IS NOT NULL                                             \n\
ORDER BY distr

TrainDelay.getTrainDelayLineSegPercentageByScenarioIdTrainType=     \n\
SELECT distr,                                                   \n\
       trn_crew_distr                         AS line_segment,  \n\
       CAST(delay_duration AS DECIMAL(11, 2)) AS delay_duration, \n\
       CAST(DELAY_PERCENTAGE AS DECIMAL(11, 2)) AS delay_percentage \n\
FROM trn_delay_stats                                                \n\
WHERE scenario_id = :scenarioId                                     \n\
  AND ((:trainType IS NULL AND TRN_TYPE IS NULL)                    \n\
    OR (:trainType IS NOT NULL AND TRN_TYPE = :trainType)           \n\
    )                                                               \n\
  AND trn_crew_distr IS NOT NULL                                    \n\
  AND distr IS NULL                                                 \n\
ORDER BY trn_crew_distr


TrainStatisticsSummaryByPool.findByTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft=       \n\
SELECT craft,                                                                     \n\
       distr,                                                                     \n\
       sub_distr,                                                                 \n\
       pool_name,                                                                 \n\
       CAST(scheduled_trn AS DECIMAL(11, 2))           AS scheduled_trn,          \n\
       CAST(completed_trn AS DECIMAL(11, 2))           AS completed_trn,          \n\
       CAST(canceled_trn AS DECIMAL(11, 2))            AS canceled_trn,           \n\
       CAST(delayed_trn AS DECIMAL(11, 2))             AS delayed_trn,            \n\
       CAST(delay_per_trn AS DECIMAL(11, 2))           AS delay_per_trn,          \n\
       CAST(recrew_trn AS DECIMAL(11, 2))              AS recrew_trn,             \n\
       CAST(waiting_for_recrew_trn AS DECIMAL(11, 2))  AS waiting_for_recrew_trn, \n\
       CAST(recrew_duration_per_trn AS DECIMAL(11, 2)) AS                         \n\
                                                          recrew_duration_per_trn \n\
FROM trn_stats_by_pool                                                            \n\
WHERE scenario_id = :scenarioId                                                   \n\
  AND ((:craft IS NULL                                                            \n\
    AND craft IS NOT NULL)                                                        \n\
    OR craft = :craft)                                                            \n\
  AND ((:summaryLevel = 'SYST'                                                    \n\
    AND (distr IS NULL                                                            \n\
        AND sub_distr IS NULL                                                     \n\
        AND pool_name IS NULL))                                                   \n\
    OR (:summaryLevel = 'DIST'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NULL                                                 \n\
            AND pool_name IS NULL))                                               \n\
    OR (:summaryLevel = 'SUBD'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NOT NULL                                             \n\
            AND pool_name IS NULL))                                               \n\
    OR (:summaryLevel = 'POOL'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NOT NULL                                             \n\
            AND pool_name IS NOT NULL)))                                          \n\
ORDER BY distr,                                                                   \n\
         sub_distr,                                                               \n\
         pool_name,                                                               \n\
         craft

TrainStatisticsSummaryByPool.findByTSSBPAvgByScenarioIdAndSummaryLevelAndCraft=     \n\
 SELECT craft,                                                                     \n\
       distr,                                                                     \n\
       sub_distr,                                                                 \n\
       pool_name,                                                                 \n\
       CAST(scheduled_trn_per_week AS DECIMAL(11, 2))           AS scheduled_trn, \n\
       CAST(completed_trn_per_week AS DECIMAL(11, 2))           AS completed_trn, \n\
       CAST(canceled_trn_per_week AS DECIMAL(11, 2))            AS canceled_trn,  \n\
       CAST(delayed_trn_per_week AS DECIMAL(11, 2))             AS delayed_trn,   \n\
       CAST(delay_per_trn_per_week AS DECIMAL(11, 2))           AS delay_per_trn, \n\
       CAST(recrew_trn_per_week AS DECIMAL(11, 2))              AS recrew_trn,    \n\
       CAST(waiting_for_recrew_trn_per_week AS DECIMAL(11, 2))  AS                \n\
                                                       waiting_for_recrew_trn,    \n\
       CAST(recrew_duration_per_trn_per_week AS DECIMAL(11, 2)) AS                \n\
                                                          recrew_duration_per_trn \n\
FROM trn_stats_by_pool                                                            \n\
WHERE scenario_id = :scenarioId                                                   \n\
  AND ((:craft IS NULL                                                            \n\
    AND craft IS NOT NULL)                                                        \n\
    OR craft = :craft)                                                            \n\
  AND ((:summaryLevel = 'SYST'                                                    \n\
    AND (distr IS NULL                                                            \n\
        AND sub_distr IS NULL                                                     \n\
        AND pool_name IS NULL))                                                   \n\
    OR (:summaryLevel = 'DIST'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NULL                                                 \n\
            AND pool_name IS NULL))                                               \n\
    OR (:summaryLevel = 'SUBD'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NOT NULL                                             \n\
            AND pool_name IS NULL))                                               \n\
    OR (:summaryLevel = 'POOL'                                                    \n\
        AND (distr IS NOT NULL                                                    \n\
            AND sub_distr IS NOT NULL                                             \n\
            AND pool_name IS NOT NULL)))                                          \n\
ORDER BY distr,                                                                   \n\
         sub_distr,                                                               \n\
         pool_name,                                                               \n\
         craft

BoardSummaryReportSummary.findBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr=\n\
select distr,                                                     \n\
       sub_distr,                                                 \n\
       board_name,                                                \n\
       board_type,                                                \n\
       craft,                                                     \n\
       current_board_size,                                        \n\
       new_board_size,                                            \n\
       delta_board_size,                                          \n\
       nbr_of_starts,                                             \n\
       CAST(STARTS_PER_TURN AS DECIMAL(11, 2)) AS STARTS_PER_TURN,\n\
       break_even_point                                           \n\
from BOARD_STATS                                                  \n\
where scenario_id = :scenarioId                                   \n\
  and (:distr IS NULL                                             \n\
    OR distr = :distr)                                            \n\
  and (:subDistr IS NULL                                          \n\
    OR sub_distr = :subDistr)                                     \n\
  AND (:boardType IS NULL                                         \n\
    OR board_type = :boardType)                                   \n\
ORDER BY distr, sub_distr, board_name, board_type, craft

BoardSummaryReportSummary.findByBoardSummaryAvgByScenarioIdBoardTypeDistrSubDistr=   \n\
select distr,                                                               \n\
       sub_distr,                                                           \n\
       board_name,                                                          \n\
       board_type,                                                          \n\
       craft,                                                               \n\
       current_board_size,                                                  \n\
       new_board_size,                                                      \n\
       delta_board_size,                                                    \n\
       CAST(STARTS_PER_WEEK AS DECIMAL(11, 2)) AS NBR_OF_STARTS,            \n\
       CAST(STARTS_PER_TURN_PER_WEEK AS DECIMAL(11, 2)) AS STARTS_PER_TURN, \n\
       break_even_point                                                     \n\
from BOARD_STATS                                                            \n\
where scenario_id = :scenarioId                                             \n\
  and (:distr IS NULL                                                       \n\
    OR distr = :distr)                                                      \n\
  and (:subDistr IS NULL                                                    \n\
    OR sub_distr = :subDistr)                                               \n\
  AND (:boardType IS NULL                                                   \n\
    OR board_type =:boardType)                                              \n\
ORDER BY distr, sub_distr, board_name, board_type, craft

BoardSummaryComp.findTotalsByBasePlanIdBaseScenarioIdTestPlanIdTestScenarioId=    \n\
select CASE                                                                       \n\
           WHEN b.distr IS NULL THEN t.distr                                      \n\
           ELSE b.distr                                                           \n\
           END                                   AS distr,                        \n\
       CASE                                                                       \n\
           WHEN b.sub_distr IS NULL THEN t.sub_distr                              \n\
           ELSE b.sub_distr                                                       \n\
           END                                   AS sub_distr,                    \n\
       CASE                                                                       \n\
           WHEN b.board_name IS NULL THEN t.board_name                            \n\
           ELSE b.board_name                                                      \n\
           END                                   AS board_name,                   \n\
       CASE                                                                       \n\
           WHEN b.craft IS NULL THEN t.craft                                      \n\
           ELSE b.craft                                                           \n\
           END                                   AS craft,                        \n\
       CASE                                                                       \n\
           WHEN b.board_type IS NULL THEN t.board_type                            \n\
           ELSE b.board_type                                                      \n\
           END                                   AS board_type,                   \n\
       CASE                                                                       \n\
           WHEN b.current_board_size IS NULL THEN 0                               \n\
           ELSE b.current_board_size                                              \n\
           END                                   AS base_current_board_size,      \n\
       CASE                                                                       \n\
           WHEN t.current_board_size IS NULL THEN 0                               \n\
           ELSE t.current_board_size                                              \n\
           END                                   AS test_current_board_size,      \n\
       CASE                                                                       \n\
           WHEN b.current_board_size IS NULL                                      \n\
               OR t.current_board_size IS NULL THEN                               \n\
               CASE                                                               \n\
                   WHEN b.current_board_size IS NULL THEN                         \n\
                       t.current_board_size                                       \n\
                   ELSE -b.current_board_size                                     \n\
                   END                                                            \n\
           ELSE t.current_board_size - b.current_board_size                       \n\
           END                                   AS current_board_size_diff,      \n\
       CASE                                                                       \n\
           WHEN b.new_board_size IS NULL THEN 0                                   \n\
           ELSE b.new_board_size                                                  \n\
           END                                   AS base_new_board_size,          \n\
       CASE                                                                       \n\
           WHEN t.new_board_size IS NULL THEN 0                                   \n\
           ELSE t.new_board_size                                                  \n\
           END                                   AS test_new_board_size,          \n\
       CASE                                                                       \n\
           WHEN b.new_board_size IS NULL                                          \n\
               OR t.new_board_size IS NULL THEN                                   \n\
               CASE                                                               \n\
                   WHEN b.new_board_size IS NULL THEN t.new_board_size            \n\
                   ELSE -b.new_board_size                                         \n\
                   END                                                            \n\
           ELSE t.new_board_size - b.new_board_size                               \n\
           END                                   AS new_board_size_diff,          \n\
       b.nbr_of_starts                           as base_nbr_of_starts,           \n\
       t.nbr_of_starts                           as test_nbr_of_starts,           \n\
       CASE                                                                       \n\
           WHEN b.nbr_of_starts IS NULL                                           \n\
               OR t.nbr_of_starts IS NULL THEN                                    \n\
               CASE                                                               \n\
                   WHEN b.nbr_of_starts IS NULL THEN t.nbr_of_starts              \n\
                   ELSE -b.nbr_of_starts                                          \n\
                   END                                                            \n\
           ELSE t.nbr_of_starts - b.nbr_of_starts                                 \n\
           END                                   AS nbr_of_starts_diff,           \n\
       CAST(b.starts_per_turn AS DECIMAL(11, 2)) AS base_starts_per_turn,         \n\
       CAST(t.starts_per_turn AS DECIMAL(11, 2)) AS test_starts_per_turn,         \n\
       b.break_even_point                        AS base_bep,                     \n\
       t.break_even_point                        AS test_bep                      \n\
from BOARD_STATS b                                                                \n\
         full outer join BOARD_STATS t                                            \n\
                         on b.DISTR = t.DISTR                                     \n\
                             and b.SUB_DISTR = t.SUB_DISTR                        \n\
                             and b.BOARD_TYPE = t.BOARD_TYPE                      \n\
                             and b.BOARD_NAME = t.BOARD_NAME                      \n\
                             and b.CRAFT = t.CRAFT                                \n\
where b.SCENARIO_ID = :baseScenarioId                                             \n\
  and t.SCENARIO_ID = :testScenarioId                                             \n\
order by b.DISTR, t.DISTR, b.SUB_DISTR, t.SUB_DISTR, b.BOARD_NAME, t.BOARD_NAME,  \n\
         b.BOARD_TYPE, t.BOARD_TYPE, b.CRAFT, t.CRAFT

BoardSummaryComp.findAvgByBasePlanIdBaseScenarioIdTestPlanIdTestScenarioId=                           \n\
SELECT CASE                                                                                           \n\
           WHEN b.distr IS NULL THEN t.distr                                                          \n\
           ELSE b.distr                                                                               \n\
           END                                            AS distr,                                   \n\
       CASE                                                                                           \n\
           WHEN b.sub_distr IS NULL THEN t.sub_distr                                                  \n\
           ELSE b.sub_distr                                                                           \n\
           END                                            AS sub_distr,                               \n\
       CASE                                                                                           \n\
           WHEN b.board_name IS NULL THEN t.board_name                                                \n\
           ELSE b.board_name                                                                          \n\
           END                                            AS board_name,                              \n\
       CASE                                                                                           \n\
           WHEN b.craft IS NULL THEN t.craft                                                          \n\
           ELSE b.craft                                                                               \n\
           END                                            AS craft,                                   \n\
       CASE                                                                                           \n\
           WHEN b.board_type IS NULL THEN t.board_type                                                \n\
           ELSE b.board_type                                                                          \n\
           END                                            AS board_type,                              \n\
       CASE                                                                                           \n\
           WHEN b.current_board_size IS NULL THEN 0                                                   \n\
           ELSE b.current_board_size                                                                  \n\
           END                                            AS base_current_board_size,                 \n\
       CASE                                                                                           \n\
           WHEN t.current_board_size IS NULL THEN 0                                                   \n\
           ELSE t.current_board_size                                                                  \n\
           END                                            AS test_current_board_size,                 \n\
       CASE                                                                                           \n\
           WHEN b.current_board_size IS NULL                                                          \n\
               OR t.current_board_size IS NULL THEN                                                   \n\
               CASE                                                                                   \n\
                   WHEN b.current_board_size IS NULL THEN                                             \n\
                       t.current_board_size                                                           \n\
                   ELSE -b.current_board_size                                                         \n\
                   END                                                                                \n\
           ELSE t.current_board_size - b.current_board_size                                           \n\
           END                                            AS current_board_size_diff,                 \n\
       CASE                                                                                           \n\
           WHEN b.new_board_size IS NULL THEN 0                                                       \n\
           ELSE b.new_board_size                                                                      \n\
           END                                            AS base_new_board_size,                     \n\
       CASE                                                                                           \n\
           WHEN t.new_board_size IS NULL THEN 0                                                       \n\
           ELSE t.new_board_size                                                                      \n\
           END                                            AS test_new_board_size,                     \n\
       CASE                                                                                           \n\
           WHEN b.new_board_size IS NULL                                                              \n\
               OR t.new_board_size IS NULL THEN                                                       \n\
               CASE                                                                                   \n\
                   WHEN b.new_board_size IS NULL THEN t.new_board_size                                \n\
                   ELSE -b.new_board_size                                                             \n\
                   END                                                                                \n\
           ELSE t.new_board_size - b.new_board_size                                                   \n\
           END                                            AS new_board_size_diff,                     \n\
       CAST(b.starts_per_week AS DECIMAL(11, 2))          as base_nbr_of_starts,                      \n\
       CAST(t.starts_per_week AS DECIMAL(11, 2))          as test_nbr_of_starts,                      \n\
       CASE                                                                                           \n\
           WHEN b.starts_per_week IS NULL                                                             \n\
               OR t.starts_per_week IS NULL THEN                                                      \n\
               CASE                                                                                   \n\
                   WHEN b.starts_per_week IS NULL THEN CAST(t.starts_per_week AS DECIMAL(11, 2))      \n\
                   ELSE CAST(-b.starts_per_week AS DECIMAL(11, 2))                                    \n\
                   END                                                                                \n\
           ELSE CAST(t.starts_per_week AS DECIMAL(11, 2)) - CAST(b.starts_per_week AS DECIMAL(11, 2)) \n\
           END                                            AS nbr_of_starts_diff,                      \n\
       CAST(b.starts_per_turn_per_week AS DECIMAL(11, 2)) AS base_starts_per_turn,                    \n\
       CAST(t.starts_per_turn_per_week AS DECIMAL(11, 2)) AS test_starts_per_turn,                    \n\
       b.break_even_point                                 AS base_bep,                                \n\
       t.break_even_point                                 AS test_bep                                 \n\
from BOARD_STATS b                                                                                    \n\
         full outer join BOARD_STATS t                                                                \n\
                         on b.DISTR = t.DISTR                                                         \n\
                             and b.SUB_DISTR = t.SUB_DISTR                                            \n\
                             and b.BOARD_TYPE = t.BOARD_TYPE                                          \n\
                             and b.BOARD_NAME = t.BOARD_NAME                                          \n\
                             and b.CRAFT = t.CRAFT                                                    \n\
where b.SCENARIO_ID = :baseScenarioId                                                                 \n\
  and t.SCENARIO_ID = :testScenarioId                                                                 \n\
order by b.DISTR, t.DISTR, b.SUB_DISTR, t.SUB_DISTR, b.BOARD_NAME, t.BOARD_NAME, b.BOARD_TYPE,        \n\
         t.BOARD_TYPE, b.CRAFT, t.CRAFT

DeadheadComp.findTotalsByBaseScenarioIdTestScenarioId=                                                      \n\
SELECT CASE                                                                                                 \n\
         WHEN b.distr IS NULL THEN t.distr                                                                  \n\
         ELSE b.distr                                                                                       \n\
       END AS distr,                                                                                        \n\
       CASE                                                                                                 \n\
         WHEN b.sub_distr IS NULL THEN t.sub_distr                                                          \n\
         ELSE b.sub_distr                                                                                   \n\
       END AS sub_distr,                                                                                    \n\
       CASE                                                                                                 \n\
         WHEN b.board_name IS NULL THEN t.board_name                                                        \n\
         ELSE b.board_name                                                                                  \n\
       END AS board_name,                                                                                   \n\
       CASE                                                                                                 \n\
         WHEN b.board_type IS NULL THEN t.board_type                                                        \n\
         ELSE b.board_type                                                                                  \n\
       END AS board_type,                                                                                   \n\
       CASE                                                                                                 \n\
         WHEN b.craft IS NULL THEN t.craft                                                                  \n\
         ELSE b.craft                                                                                       \n\
       END AS craft,                                                                                        \n\
       CASE                                                                                                 \n\
         WHEN b.base_count IS NULL THEN 0                                                                   \n\
         ELSE b.base_count                                                                                  \n\
       END AS base_count,                                                                                   \n\
       CASE                                                                                                 \n\
         WHEN t.test_count IS NULL THEN 0                                                                   \n\
         ELSE t.test_count                                                                                  \n\
       END AS test_count,                                                                                   \n\
       CASE                                                                                                 \n\
         WHEN b.base_count IS NULL                                                                          \n\
               OR t.test_count IS NULL THEN                                                                 \n\
           CASE                                                                                             \n\
             WHEN b.base_count IS NULL THEN t.test_count                                                    \n\
             ELSE -b.base_count                                                                             \n\
           END                                                                                              \n\
         ELSE t.test_count-b.base_count                                                                     \n\
       END AS count_diff                                                                                    \n\
FROM   (SELECT distr,                                                                                       \n\
               sub_distr,                                                                                   \n\
               board_name,                                                                                  \n\
               board_type,                                                                                  \n\
               craft,                                                                                       \n\
               nbr_of_starts AS base_count                                                                  \n\
        FROM   board_train_starts                                                                           \n\
        WHERE  start_type = 'DEADHEAD'                                                                      \n\
               AND scenario_id = :baseScenarioId) b                                                         \n\
       FULL OUTER JOIN (SELECT distr,                                                                       \n\
                               sub_distr,                                                                   \n\
                               board_name,                                                                  \n\
                               board_type,                                                                  \n\
                               craft,                                                                       \n\
                               nbr_of_starts AS test_count                                                  \n\
                        FROM   board_train_starts                                                           \n\
                        WHERE  start_type = 'DEADHEAD'                                                      \n\
                               AND scenario_id = :testScenarioId) t                                         \n\
                    ON b.distr = t.distr                                                                    \n\
                       AND b.sub_distr = t.sub_distr                                                        \n\
                       AND b.board_name = t.board_name                                                      \n\
                       AND b.craft = t.craft

DeadheadComp.findAvgByBaseScenarioIdTestScenarioId=                                                         \n\
SELECT CASE                                                                      \n\
         WHEN b.distr IS NULL THEN t.distr                                       \n\
         ELSE b.distr                                                            \n\
       END AS distr,                                                             \n\
       CASE                                                                      \n\
         WHEN b.sub_distr IS NULL THEN t.sub_distr                               \n\
         ELSE b.sub_distr                                                        \n\
       END AS sub_distr,                                                         \n\
       CASE                                                                      \n\
         WHEN b.board_name IS NULL THEN t.board_name                             \n\
         ELSE b.board_name                                                       \n\
       END AS board_name,                                                        \n\
       CASE                                                                      \n\
         WHEN b.board_type IS NULL THEN t.board_type                             \n\
         ELSE b.board_type                                                       \n\
       END AS board_type,                                                        \n\
       CASE                                                                      \n\
         WHEN b.craft IS NULL THEN t.craft                                       \n\
         ELSE b.craft                                                            \n\
       END AS craft,                                                             \n\
  Nvl2(b.base_count, b.base_count, 0) as base_count,                             \n\
  Nvl2(t.test_count, t.test_count, 0) as test_count,                             \n\
  Nvl2(t.test_count, t.test_count, 0) -                                          \n\
  Nvl2(b.base_count, b.base_count, 0) as count_diff                              \n\
FROM   (SELECT distr,                                                            \n\
               sub_distr,                                                        \n\
               board_name,                                                       \n\
               board_type,                                                       \n\
               craft,                                                            \n\
               nbr_of_starts AS base_count_tmp,                                  \n\
               CASE                                                              \n\
                 WHEN nbr_of_starts IS NULL THEN 0.0                             \n\
                 ELSE CAST(CAST(nbr_of_starts AS DECIMAL(10, 2)) / CAST(         \n\
                                       (SELECT CASE                              \n\
                                               WHEN                              \n\
                                       s.stats_end_date = s.stats_start_date     \n\
                                               THEN 1.0                          \n\
                                                    ELSE                         \n\
                                               (                                 \n\
                                               Nvl2(s.stats_end_date,            \n\
                                               DAYS(s.stats_end_date), 0         \n\
                                               ) -                               \n\
                                                      Nvl2(s.stats_start_date,   \n\
                                                      DAYS(s.stats_start_date),  \n\
                                                      0) )                       \n\
                                               / 7.0                             \n\
                                               END                               \n\
                                                                         FROM    \n\
                                       scenario_cfg s                            \n\
                                                                         WHERE   \n\
                                       s.scenario_id = :baseScenarioId) AS       \n\
                                       DECIMAL(9, 4)) AS                         \n\
                           DECIMAL (                                             \n\
                                  9, 2))                                         \n\
               END           AS base_count                                       \n\
        FROM   board_train_starts                                                \n\
        WHERE  start_type = 'DEADHEAD'                                           \n\
               AND scenario_id = :baseScenarioId) b                              \n\
       FULL OUTER JOIN (SELECT distr,                                            \n\
                               sub_distr,                                        \n\
                               board_name,                                       \n\
                               board_type,                                       \n\
                               craft,                                            \n\
                               CASE                                              \n\
                                 WHEN nbr_of_starts IS NULL THEN 0.0             \n\
                                 ELSE CAST(CAST(nbr_of_starts AS DECIMAL(10, 2)) \n\
                                           / CAST                                \n\
                                           (                                     \n\
                                                       (SELECT                   \n\
                                             CASE                                \n\
                                             WHEN                                \n\
                                                       s.stats_end_date =        \n\
                                                       s.stats_start_date THEN   \n\
                                             1.0                                 \n\
                                             ELSE                                \n\
                                                               (                 \n\
Nvl2(s.stats_end_date,                                                           \n\
DAYS(s.stats_end_date),                                                          \n\
0                                                                                \n\
) -                                                                              \n\
Nvl2(s.stats_start_date,                                                         \n\
DAYS(s.stats_start_date),                                                        \n\
0) )                                                                             \n\
/ 7.0                                                                            \n\
END                                                                              \n\
FROM                                                                             \n\
scenario_cfg s                                                                   \n\
WHERE                                                                            \n\
s.scenario_id = :testScenarioId) AS                                              \n\
DECIMAL(9, 4)) AS                                                                \n\
DECIMAL (                                                                        \n\
9, 2))                                                                           \n\
END AS test_count                                                                \n\
FROM   board_train_starts                                                        \n\
WHERE  start_type = 'DEADHEAD'                                                   \n\
AND scenario_id = :testScenarioId) t                                             \n\
ON b.distr = t.distr                                                             \n\
AND b.sub_distr = t.sub_distr                                                    \n\
AND b.board_name = t.board_name                                                  \n\
AND b.craft = t.craft

TrainDelayComp.findTotalsByBaseScenarioIdTestScenarioId=                                                \n\
SELECT CASE                                                                                             \n\
           WHEN b.distr IS NULL THEN t.distr                                                            \n\
           ELSE b.distr                                                                                 \n\
           END                                     AS distr,                                            \n\
       CASE                                                                                             \n\
           WHEN b.TRN_CREW_DISTR IS NULL THEN t.TRN_CREW_DISTR                                          \n\
           ELSE b.TRN_CREW_DISTR                                                                        \n\
           END                                     AS line_segment,                                     \n\
       CASE                                                                                             \n\
           WHEN b.TOTAL_TRAINS IS NULL THEN 0                                                           \n\
           ELSE b.TOTAL_TRAINS                                                                          \n\
           END                                     AS base_trn_count,                                   \n\
       CASE                                                                                             \n\
           WHEN t.TOTAL_TRAINS IS NULL THEN 0                                                           \n\
           ELSE t.TOTAL_TRAINS                                                                          \n\
           END                                     AS test_trn_count,                                   \n\
       CASE                                                                                             \n\
           WHEN t.TOTAL_TRAINS IS NULL                                                                  \n\
               OR b.TOTAL_TRAINS IS NULL THEN                                                           \n\
               CASE                                                                                     \n\
                   WHEN b.TOTAL_TRAINS IS NULL THEN t.TOTAL_TRAINS                                      \n\
                   ELSE -b.TOTAL_TRAINS                                                                 \n\
                   END                                                                                  \n\
           ELSE t.TOTAL_TRAINS - b.TOTAL_TRAINS                                                         \n\
           END                                     AS trn_count_diff,                                   \n\
       nvl2(b.DELAYED_TRAINS, b.DELAYED_TRAINS, 0) as base_delay_trn_count,                             \n\
       nvl2(t.DELAYED_TRAINS, t.DELAYED_TRAINS, 0) as test_delay_trn_count,                             \n\
       (nvl2(t.DELAYED_TRAINS, t.DELAYED_TRAINS, 0) - nvl2(b.DELAYED_TRAINS, b.DELAYED_TRAINS, 0))      \n\
                                                   AS delay_trn_count_diff,                             \n\
       CASE                                                                                             \n\
           WHEN b.DELAY_PERCENTAGE IS NULL THEN 0                                                       \n\
           ELSE CAST(b.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                              \n\
           END                                     AS base_delay_rate,                                  \n\
       CASE                                                                                             \n\
           WHEN t.DELAY_PERCENTAGE IS NULL THEN 0                                                       \n\
           ELSE CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                              \n\
           END                                     AS test_delay_rate,                                  \n\
       CASE                                                                                             \n\
           WHEN b.DELAY_PERCENTAGE IS NULL                                                              \n\
               OR t.DELAY_PERCENTAGE IS NULL THEN                                                       \n\
               CASE                                                                                     \n\
                   WHEN b.DELAY_PERCENTAGE IS NULL THEN CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2))      \n\
                   ELSE CAST(-b.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                     \n\
                   END                                                                                  \n\
           ELSE CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2)) - CAST(b.DELAY_PERCENTAGE AS DECIMAL(11, 2)) \n\
           END                                     AS delay_rate_diff,                                  \n\
       CASE                                                                                             \n\
           WHEN b.DELAY_DURATION IS NULL THEN 0                                                         \n\
           ELSE CAST(b.DELAY_DURATION AS DECIMAL(11, 2))                                                \n\
           END                                     AS base_delay_duration,                              \n\
       CASE                                                                                             \n\
           WHEN t.DELAY_DURATION IS NULL THEN 0                                                         \n\
           ELSE CAST(t.DELAY_DURATION AS DECIMAL(11, 2))                                                \n\
           END                                     AS test_delay_duration,                              \n\
       CASE                                                                                             \n\
           WHEN b.DELAY_DURATION IS NULL                                                                \n\
               OR t.DELAY_DURATION IS NULL THEN                                                         \n\
               CASE                                                                                     \n\
                   WHEN b.DELAY_DURATION IS NULL THEN CAST                                              \n\
                       (t.DELAY_DURATION                                                                \n\
                       AS                                                                               \n\
                       DECIMAL(11, 2))                                                                  \n\
                   ELSE CAST(-b.DELAY_DURATION AS DECIMAL(11, 2))                                       \n\
                   END                                                                                  \n\
           ELSE CAST(                                                                                   \n\
                   (t.DELAY_DURATION - b.DELAY_DURATION) AS                                             \n\
               DECIMAL(11, 2))                                                                          \n\
           END                                     AS delay_duration_diff                               \n\
FROM trn_delay_stats b                                                                                  \n\
         FULL OUTER JOIN trn_delay_stats t                                                              \n\
                         ON b.distr = t.distr                                                           \n\
                             AND b.trn_crew_distr = t.trn_crew_distr                                    \n\
                             AND b.trn_type IS NULL                                                     \n\
                             AND t.trn_type IS NULL                                                     \n\
WHERE b.SCENARIO_ID = :baseScenarioId                                                                   \n\
  AND t.SCENARIO_ID = :testScenarioId                                                                   \n\
  AND b.distr IS NOT NULL                                                                               \n\
  AND b.TRN_CREW_DISTR IS NOT NULL                                                                      \n\
ORDER BY b.distr, t.distr, line_segment

TrainDelayComp.findAvgByBaseScenarioIdTestScenarioId=                                                                 \n\
SELECT CASE                                                                                                           \n\
           WHEN b.distr IS NULL THEN t.distr                                                                          \n\
           ELSE b.distr                                                                                               \n\
           END                                                                               AS distr,                \n\
       CASE                                                                                                           \n\
           WHEN b.TRN_CREW_DISTR IS NULL THEN t.TRN_CREW_DISTR                                                        \n\
           ELSE b.TRN_CREW_DISTR                                                                                      \n\
           END                                                                               AS line_segment,         \n\
       CASE                                                                                                           \n\
           WHEN b.TOTAL_TRAINS_PER_WEEK IS NULL THEN 0                                                                \n\
           ELSE CAST(b.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2))                                                       \n\
           END                                                                               AS base_trn_count,       \n\
       CASE                                                                                                           \n\
           WHEN t.TOTAL_TRAINS_PER_WEEK IS NULL THEN 0                                                                \n\
           ELSE CAST(t.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2))                                                       \n\
           END                                                                               AS test_trn_count,       \n\
       CASE                                                                                                           \n\
           WHEN t.TOTAL_TRAINS_PER_WEEK IS NULL                                                                       \n\
               OR b.TOTAL_TRAINS_PER_WEEK IS NULL THEN                                                                \n\
               CASE                                                                                                   \n\
                   WHEN b.TOTAL_TRAINS_PER_WEEK IS NULL THEN CAST(b.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2))          \n\
                   ELSE CAST(-b.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2))                                              \n\
                   END                                                                                                \n\
           ELSE CAST(t.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2)) - CAST(b.TOTAL_TRAINS_PER_WEEK AS DECIMAL(11, 2))     \n\
           END                                                                               AS trn_count_diff,       \n\
       nvl2(b.DELAYED_TRAINS_PER_WEEK, CAST(b.DELAYED_TRAINS_PER_WEEK AS DECIMAL(11, 2)), 0) as base_delay_trn_count, \n\
       nvl2(t.DELAYED_TRAINS_PER_WEEK, CAST(t.DELAYED_TRAINS_PER_WEEK AS DECIMAL(11, 2)), 0) as test_delay_trn_count, \n\
       (nvl2(t.DELAYED_TRAINS_PER_WEEK, CAST(t.DELAYED_TRAINS_PER_WEEK AS DECIMAL(11, 2)), 0) -                       \n\
       nvl2(b.DELAYED_TRAINS_PER_WEEK, CAST(b.DELAYED_TRAINS_PER_WEEK AS DECIMAL(11, 2)), 0))                         \n\
                                                                                             AS delay_trn_count_diff, \n\
       CASE                                                                                                           \n\
           WHEN b.DELAY_PERCENTAGE IS NULL THEN 0                                                                     \n\
           ELSE CAST(b.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                                            \n\
           END                                                                               AS base_delay_rate,      \n\
       CASE                                                                                                           \n\
           WHEN t.DELAY_PERCENTAGE IS NULL THEN 0                                                                     \n\
           ELSE CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                                            \n\
           END                                                                               AS test_delay_rate,      \n\
       CASE                                                                                                           \n\
           WHEN b.DELAY_PERCENTAGE IS NULL                                                                            \n\
               OR t.DELAY_PERCENTAGE IS NULL THEN                                                                     \n\
               CASE                                                                                                   \n\
                   WHEN b.DELAY_PERCENTAGE IS NULL THEN CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2))                    \n\
                   ELSE CAST(-b.DELAY_PERCENTAGE AS DECIMAL(11, 2))                                                   \n\
                   END                                                                                                \n\
           ELSE CAST(t.DELAY_PERCENTAGE AS DECIMAL(11, 2)) - CAST(b.DELAY_PERCENTAGE AS DECIMAL(11, 2))               \n\
           END                                                                               AS delay_rate_diff,      \n\
       CASE                                                                                                           \n\
           WHEN b.DELAY_DURATION_PER_WEEK IS NULL THEN 0                                                              \n\
           ELSE CAST(b.DELAY_DURATION_PER_WEEK AS DECIMAL(11, 2))                                                     \n\
           END                                                                               AS base_delay_duration,  \n\
       CASE                                                                                                           \n\
           WHEN t.DELAY_DURATION_PER_WEEK IS NULL THEN 0                                                              \n\
           ELSE CAST(t.DELAY_DURATION_PER_WEEK AS DECIMAL(11, 2))                                                     \n\
           END                                                                               AS test_delay_duration,  \n\
       CASE                                                                                                           \n\
           WHEN b.DELAY_DURATION_PER_WEEK IS NULL                                                                     \n\
               OR t.DELAY_DURATION_PER_WEEK IS NULL THEN                                                              \n\
               CASE                                                                                                   \n\
                   WHEN b.DELAY_DURATION_PER_WEEK IS NULL THEN CAST                                                   \n\
                       (t.DELAY_DURATION_PER_WEEK                                                                     \n\
                       AS                                                                                             \n\
                       DECIMAL(11, 2))                                                                                \n\
                   ELSE CAST(-b.DELAY_DURATION_PER_WEEK AS DECIMAL(11, 2))                                            \n\
                   END                                                                                                \n\
           ELSE CAST(                                                                                                 \n\
                   (t.DELAY_DURATION_PER_WEEK - b.DELAY_DURATION_PER_WEEK) AS                                         \n\
               DECIMAL(11, 2))                                                                                        \n\
           END                                                                               AS delay_duration_diff   \n\
FROM trn_delay_stats b                                                                                                \n\
         FULL OUTER JOIN trn_delay_stats t                                                                            \n\
                         ON b.distr = t.distr                                                                         \n\
                             AND b.trn_crew_distr = t.trn_crew_distr                                                  \n\
                             AND b.trn_type IS NULL                                                                   \n\
                             AND t.trn_type IS NULL                                                                   \n\
WHERE b.SCENARIO_ID = :baseScenarioId                                                                                 \n\
  AND t.SCENARIO_ID = :testScenarioId                                                                                 \n\
  AND b.distr IS NOT NULL                                                                                             \n\
  AND b.TRN_CREW_DISTR IS NOT NULL                                                                                    \n\
ORDER BY b.distr, t.distr, line_segment

TrainStatsComp.findTotalsByBaseScenarioIdTestScenarioId=\n\
SELECT CASE                                                                        \n\
           WHEN SCENARIO_ID = :baseScenarioId                                      \n\
               THEN 'BASE'                                                         \n\
           ELSE 'TEST' END                                      AS scenario_type,  \n\
       distr,                                                                      \n\
       sub_distr,                                                                  \n\
       pool_name,                                                                  \n\
       craft,                                                                      \n\
       CAST(scheduled_trn AS DECIMAL(11, 2))           AS scheduled_trn,           \n\
       CAST(completed_trn AS DECIMAL(11, 2))           AS completed_trn,           \n\
       CAST(canceled_trn AS DECIMAL(11, 2))            AS canceled_trn,            \n\
       CAST(delayed_trn AS DECIMAL(11, 2))             AS delayed_trn,             \n\
       CAST(delay_per_trn AS DECIMAL(11, 2))           AS delay_per_trn,           \n\
       CAST(recrew_trn AS DECIMAL(11, 2))              AS recrew_trn,              \n\
       CAST(recrew_duration_per_trn AS DECIMAL(11, 2)) AS recrew_duration_per_trn, \n\
       CAST(waiting_for_recrew_trn AS DECIMAL(11, 2))  AS waiting_for_recrew_trn   \n\
FROM trn_stats_by_pool                                                             \n\
WHERE (scenario_id = :baseScenarioId OR scenario_id = :testScenarioId)             \n\
  AND craft IS NOT NULL                                                            \n\
  AND distr IS NOT NULL                                                            \n\
  AND sub_distr IS NOT NULL                                                        \n\
  AND pool_name IS NOT NULL                                                        \n\
ORDER BY distr,                                                                    \n\
         sub_distr,                                                                \n\
         pool_name,                                                                \n\
         craft,                                                                    \n\
         scenario_type
TrainStatsComp.findAvgByBaseScenarioIdTestScenarioId=      \n\                                                                                     \n\
SELECT CASE                                                                                 \n\
           WHEN SCENARIO_ID = :baseScenarioId                                               \n\
               THEN 'BASE'                                                                  \n\
           ELSE 'TEST'                                                                      \n\
	   END                                    					AS scenario_type,           \n\
       distr,                                                                               \n\
       sub_distr,                                                                           \n\
       pool_name,                                                                           \n\
       craft,                                                                               \n\
       CAST(scheduled_trn_per_week AS DECIMAL(11, 2))           AS scheduled_trn,           \n\
       CAST(completed_trn_per_week AS DECIMAL(11, 2))           AS completed_trn,           \n\
       CAST(canceled_trn_per_week AS DECIMAL(11, 2))            AS canceled_trn,            \n\
       CAST(delayed_trn_per_week AS DECIMAL(11, 2))             AS delayed_trn,             \n\
       CAST(delay_per_trn_per_week AS DECIMAL(11, 2))           AS delay_per_trn,           \n\
       CAST(recrew_trn_per_week AS DECIMAL(11, 2))              AS recrew_trn,              \n\
       CAST(recrew_duration_per_trn_per_week AS DECIMAL(11, 2)) AS recrew_duration_per_trn, \n\
       CAST(waiting_for_recrew_trn_per_week AS DECIMAL(11, 2))  AS waiting_for_recrew_trn   \n\
FROM trn_stats_by_pool                                                                      \n\
WHERE (scenario_id = :baseScenarioId OR scenario_id = :testScenarioId)                      \n\
  AND craft IS NOT NULL                                                                     \n\
  AND distr IS NOT NULL                                                                     \n\
  AND sub_distr IS NOT NULL                                                                 \n\
  AND pool_name IS NOT NULL                                                                 \n\
ORDER BY distr,                                                                             \n\
         sub_distr,                                                                         \n\
         pool_name,                                                                         \n\
         craft,                                                                             \n\
         scenario_type
TurnUtilizationComp.findTotalsByBaseScenarioIdTestScenarioId=                                                     \n\
SELECT tb.board_type,                                                             \n\
       tb.craft,                                                                  \n\
       tb.distr,                                                                  \n\
       tb.sub_distr,                                                              \n\
       tb.board_name,                                                             \n\
       tb.status,                                                                 \n\
       base_duration,                                                             \n\
       test_duration,                                                             \n\
       CAST((tb.test_duration - tb.base_duration) AS                              \n\
           DECIMAL(10, 2)) AS                                                     \n\
           duration_diff                                                          \n\
FROM (SELECT CASE                                                                 \n\
                 WHEN b.board_type IS NULL THEN t.board_type                      \n\
                 ELSE b.board_type                                                \n\
                 END AS board_type,                                               \n\
             CASE                                                                 \n\
                 WHEN b.craft IS NULL THEN t.craft                                \n\
                 ELSE b.craft                                                     \n\
                 END AS craft,                                                    \n\
             CASE                                                                 \n\
                 WHEN b.distr IS NULL THEN t.distr                                \n\
                 ELSE b.distr                                                     \n\
                 END AS distr,                                                    \n\
             CASE                                                                 \n\
                 WHEN b.sub_distr IS NULL THEN t.sub_distr                        \n\
                 ELSE b.sub_distr                                                 \n\
                 END AS sub_distr,                                                \n\
             CASE                                                                 \n\
                 WHEN b.board_name IS NULL THEN t.board_name                      \n\
                 ELSE b.board_name                                                \n\
                 END AS board_name,                                               \n\
             CASE                                                                 \n\
                 WHEN b.onduty_flag IS NULL THEN t.onduty_flag                    \n\
                 ELSE b.onduty_flag                                               \n\
                 END AS status,                                                   \n\
             CASE                                                                 \n\
                 WHEN b.b_duration IS NULL THEN 0                                 \n\
                 ELSE CAST(b.b_duration AS DECIMAL(10, 2))                        \n\
                 END AS base_duration,                                            \n\
             CASE                                                                 \n\
                 WHEN t.t_duration IS NULL THEN 0                                 \n\
                 ELSE CAST(t.t_duration AS DECIMAL(10, 2))                        \n\
                 END AS test_duration                                             \n\
      FROM (SELECT board_type,                                                    \n\
                   craft,                                                         \n\
                   distr,                                                         \n\
                   sub_distr,                                                     \n\
                   board_name,                                                    \n\
                   onduty_flag,                                                   \n\
                   Sum(duration) AS b_duration                                    \n\
            FROM turn_utilization                                                 \n\
            WHERE scenario_id = :baseScenarioId                                   \n\
            GROUP BY board_type,                                                  \n\
                     craft,                                                       \n\
                     distr,                                                       \n\
                     sub_distr,                                                   \n\
                     board_name,                                                  \n\
                     onduty_flag) b                                               \n\
               FULL OUTER JOIN (SELECT board_type,                                \n\
                                       craft,                                     \n\
                                       distr,                                     \n\
                                       sub_distr,                                 \n\
                                       board_name,                                \n\
                                       onduty_flag,                               \n\
                                       Sum(duration) AS t_duration                \n\
                                FROM turn_utilization                             \n\
                                WHERE scenario_id = :testScenarioId               \n\
                                GROUP BY board_type,                              \n\
                                         craft,                                   \n\
                                         distr,                                   \n\
                                         sub_distr,                               \n\
                                         board_name,                              \n\
                                         onduty_flag) t                           \n\
                               ON b.board_type = t.board_type                     \n\
                                   AND b.craft = t.craft                          \n\
                                   AND b.distr = t.distr                          \n\
                                   AND b.sub_distr = t.sub_distr                  \n\
                                   AND b.board_name = t.board_name                \n\
                                   AND b.onduty_flag = t.onduty_flag) tb          \n\
order by tb.distr, tb.sub_distr, tb.board_name, tb.craft, tb.status

TurnUtilizationComp.findAvgByBaseScenarioIdTestScenarioId=                                                        \n\
SELECT tb.board_type,                                                                                                                                        \n\
       tb.craft,                                                                                                                                             \n\
       tb.distr,                                                                                                                                             \n\
       tb.sub_distr,                                                                                                                                         \n\
       tb.board_name,                                                                                                                                        \n\
       tb.status,                                                                                                                                            \n\
       tb.base_duration,                                                                                                                                     \n\
       tb.test_duration,                                                                                                                                     \n\
       CAST((tb.test_duration - tb.base_duration) AS DECIMAL(10, 2)) AS                                                                                      \n\
           duration_diff                                                                                                                                     \n\
FROM (SELECT CASE                                                                                                                                            \n\
                 WHEN b.board_type IS NULL THEN t.board_type                                                                                                 \n\
                 ELSE b.board_type                                                                                                                           \n\
                 END AS board_type,                                                                                                                          \n\
             CASE                                                                                                                                            \n\
                 WHEN b.craft IS NULL THEN t.craft                                                                                                           \n\
                 ELSE b.craft                                                                                                                                \n\
                 END AS craft,                                                                                                                               \n\
             CASE                                                                                                                                            \n\
                 WHEN b.distr IS NULL THEN t.distr                                                                                                           \n\
                 ELSE b.distr                                                                                                                                \n\
                 END AS distr,                                                                                                                               \n\
             CASE                                                                                                                                            \n\
                 WHEN b.sub_distr IS NULL THEN t.sub_distr                                                                                                   \n\
                 ELSE b.sub_distr                                                                                                                            \n\
                 END AS sub_distr,                                                                                                                           \n\
             CASE                                                                                                                                            \n\
                 WHEN b.board_name IS NULL THEN t.board_name                                                                                                 \n\
                 ELSE b.board_name                                                                                                                           \n\
                 END AS board_name,                                                                                                                          \n\
             CASE                                                                                                                                            \n\
                 WHEN b.onduty_flag IS NULL THEN t.onduty_flag                                                                                               \n\
                 ELSE b.onduty_flag                                                                                                                          \n\
                 END AS status,                                                                                                                              \n\
             CASE                                                                                                                                            \n\
                 WHEN b.b_duration IS NULL THEN 0                                                                                                            \n\
                 ELSE CAST((CAST(b.b_duration AS DECIMAL(12, 4)) / CAST((SELECT (CASE                                                                        \n\
                                                                                           WHEN s.stats_end_date = s.stats_start_date                        \n\
                                                                                               THEN 1.0                                                      \n\
                                                                                           ELSE (Nvl2(s.stats_end_date, DAYS(s.stats_end_date), 0) -         \n\
                                                                                                 Nvl2(s.stats_start_date, DAYS(s.stats_start_date), 0)) /    \n\
                                                                                                7.0 END)                                                     \n\
                                                                               FROM scenario_cfg s                                                           \n\
                                                                               WHERE s.scenario_id = :baseScenarioId) AS                                     \n\
                     DECIMAL(12, 4))) AS DECIMAL(10, 2))                                                                                                     \n\
                 END AS base_duration,                                                                                                                       \n\
             CASE                                                                                                                                            \n\
                 WHEN t.t_duration IS NULL THEN 0                                                                                                            \n\
                 ELSE CAST((CAST(t.t_duration AS DECIMAL(12, 4)) / CAST((SELECT (CASE                                                                        \n\
                                                                                           WHEN s.stats_end_date = s.stats_start_date                        \n\
                                                                                               THEN 1.0                                                      \n\
                                                                                           ELSE (Nvl2(s.stats_end_date, DAYS(s.stats_end_date), 0) -         \n\
                                                                                                 Nvl2(s.stats_start_date, DAYS(s.stats_start_date), 0)) /    \n\
                                                                                                7.0 END)                                                     \n\
                                                                               FROM scenario_cfg s                                                           \n\
                                                                               WHERE s.scenario_id = :testScenarioId) AS                                     \n\
                     DECIMAL(12, 4))) AS DECIMAL(10, 2))                                                                                                     \n\
                 END AS test_duration                                                                                                                        \n\
      FROM (SELECT board_type,                                                                                                                               \n\
                   craft,                                                                                                                                    \n\
                   distr,                                                                                                                                    \n\
                   sub_distr,                                                                                                                                \n\
                   board_name,                                                                                                                               \n\
                   onduty_flag,                                                                                                                              \n\
                   Sum(duration) AS b_duration                                                                                                               \n\
            FROM turn_utilization                                                                                                                            \n\
            WHERE scenario_id = :baseScenarioId                                                                                                              \n\
            GROUP BY board_type,                                                                                                                             \n\
                     craft,                                                                                                                                  \n\
                     distr,                                                                                                                                  \n\
                     sub_distr,                                                                                                                              \n\
                     board_name,                                                                                                                             \n\
                     onduty_flag) b                                                                                                                          \n\
               FULL OUTER JOIN (SELECT board_type,                                                                                                           \n\
                                       craft,                                                                                                                \n\
                                       distr,                                                                                                                \n\
                                       sub_distr,                                                                                                            \n\
                                       board_name,                                                                                                           \n\
                                       onduty_flag,                                                                                                          \n\
                                       Sum(duration) AS t_duration                                                                                           \n\
                                FROM turn_utilization                                                                                                        \n\
                                WHERE scenario_id = :testScenarioId                                                                                          \n\
                                GROUP BY board_type,                                                                                                         \n\
                                         craft,                                                                                                              \n\
                                         distr,                                                                                                               \n\
                                         sub_distr,                                                                                                          \n\
                                         board_name,                                                                                                         \n\
                                         onduty_flag) t                                                                                                      \n\
                               ON b.board_type = t.board_type                                                                                                \n\
                                   AND b.craft = t.craft                                                                                                     \n\
                                   AND b.distr = t.distr                                                                                                     \n\
                                   AND b.sub_distr = t.sub_distr                                                                                             \n\
                                   AND b.board_name = t.board_name                                                                                           \n\
                                   AND b.onduty_flag = t.onduty_flag) tb                                                                                     \n\
order by tb.distr, tb.sub_distr, tb.board_name, tb.craft, tb.status

TieUpExceptionSummary.findByPlanId=select * from tie_up_exception where plan_id=:planId order by distr, sub_distr, craft, description

PoolSummary.findByPlanId=select * from pool where plan_id=:planId order by pool_id

ExtraboardSummary.findByPlanId=select * from exb_pool_set_up where plan_id=:planId order by distr, sub_distr, pool_name

WorkRestProfSummary.findByPlanId=select * from work_rest_prof where plan_id=:planId

WorkRestProfSummary.findByPlanIdAndDistrAndSubDistrAndPoolNameAndPoolType=                                  \n\
SELECT t1.work_rest_prof_id,                                                                                      \n\
       t1.prof_name,                                                                                              \n\
       t1.distr,                                                                                                  \n\
       t1.sub_distr,                                                                                              \n\
       t1.pool_name                                                                                               \n\
FROM   work_rest_prof t1                                                                                          \n\
       LEFT JOIN (SELECT distr,                                                                                   \n\
                         sub_distr,                                                                               \n\
                         pool_name,                                                                               \n\
                         'EB' AS pool_type                                                                        \n\
                  FROM   exb_pool_set_up                                                                          \n\
                  WHERE  plan_id = :planId) t2                                                                    \n\
              ON t1.distr = t2.distr                                                                              \n\
                 AND t1.sub_distr = t2.sub_distr                                                                  \n\
                 AND t1.pool_name = t2.pool_name                                                                  \n\
       LEFT JOIN (SELECT DISTINCT distr,                                                                          \n\
                                  sub_distr,                                                                      \n\
                                  pool_name,                                                                      \n\
                                  'SS' AS pool_type                                                               \n\
                  FROM   pool                                                                                     \n\
                  WHERE  plan_id = :planId) t3                                                                    \n\
              ON t1.distr = t3.distr                                                                              \n\
                 AND t1.sub_distr = t3.sub_distr                                                                  \n\
                 AND t1.pool_name = t3.pool_name                                                                  \n\
WHERE  t1.plan_id = :planId                                                                                       \n\
       AND ( :distr IS NULL                                                                                       \n\
              OR t1.distr = :distr )                                                                              \n\
       AND ( :subDistr IS NULL                                                                                    \n\
              OR t1.sub_distr = :subDistr )                                                                       \n\
       AND ( :poolName IS NULL                                                                                    \n\
              OR t1.pool_name = :poolName )                                                                       \n\
       AND ( :poolType IS NULL                                                                                    \n\
              OR ( :poolType != 'EB'                                                                              \n\
                    OR t2.pool_type = :poolType ) )                                                               \n\
       AND ( :poolType IS NULL                                                                                    \n\
              OR ( :poolType != 'SS'                                                                              \n\
                    OR t3.pool_type = :poolType ) )

BoardSummaryReportSummary.findBoardSummaryExpDetailsTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr=               \n\
select bs.distr,                                                                  \n\
       bs.sub_distr,                                                              \n\
       bs.board_name,                                                             \n\
       bs.board_type,                                                             \n\
       bs.craft,                                                                  \n\
       bs.current_board_size,                                                     \n\
       bs.new_board_size,                                                         \n\
       bs.delta_board_size,                                                       \n\
       CAST(bs.NBR_OF_STARTS AS DECIMAL(11, 2))   AS NBR_OF_STARTS,               \n\
       CAST(bs.STARTS_PER_TURN AS DECIMAL(11, 2)) AS STARTS_PER_TURN,             \n\
       bs.break_even_point,                                                       \n\
       bts.nbr_of_starts                          AS nbr_of_starts_per_start_type,\n\
       bts.START_TYPE                                                             \n\
from BOARD_STATS bs                                                               \n\
         LEFT JOIN board_train_starts bts                                         \n\
                   on bs.scenario_id = bts.scenario_id                            \n\
                       AND bs.distr = bts.distr                                   \n\
                       AND bs.sub_distr = bts.sub_distr                           \n\
                       AND bs.board_name = bts.board_name                         \n\
                       AND bs.craft = bts.craft                                   \n\
                                                                                  \n\
where bs.scenario_id = :scenarioId                                                \n\
  and (:distr IS NULL                                                             \n\
    OR bs.distr = :distr)                                                         \n\
  and (:subDistr IS NULL                                                          \n\
    OR bs.sub_distr = :subDistr)                                                  \n\
  AND (:boardType IS NULL                                                         \n\
    OR bs.board_type = :boardType)                                                \n\
ORDER BY bs.distr, bs.sub_distr, bs.board_name, bs.board_type,                    \n\
         bs.craft, bts.START_TYPE

BoardSummaryReportSummary.findByBoardSummaryExpDetailsAvgByScenarioIdBoardTypeDistrSubDistr=                                           \n\
WITH scenariocfg as (                                                                                                                  \n\
    SELECT s.scenario_id,                                                                                                              \n\
           CAST(                                                                                                                       \n\
                   CASE                                                                                                                \n\
                       WHEN s.stats_end_date = s.stats_start_date THEN 1.0                                                             \n\
                       ELSE (Nvl2(s.stats_end_date, DAYS(s.stats_end_date), 0) -                                                       \n\
                             Nvl2(s.stats_start_date, DAYS(s.stats_start_date), 0)) /                                                  \n\
                            7.0 END AS DECIMAL(12, 4)) as nbr_of_weeks                                                                 \n\
    FROM scenario_cfg s                                                                                                                \n\
    WHERE s.scenario_id = :scenarioId                                                                                                  \n\
)                                                                                                                                      \n\
SELECT bs.distr,                                                                                                                       \n\
       bs.sub_distr,                                                                                                                   \n\
       bs.board_name,                                                                                                                  \n\
       bs.board_type,                                                                                                                  \n\
       bs.craft,                                                                                                                       \n\
       bs.current_board_size,                                                                                                          \n\
       bs.new_board_size,                                                                                                              \n\
       bs.delta_board_size,                                                                                                            \n\
       CAST(bs.STARTS_PER_WEEK AS DECIMAL(11, 2))                                                     AS NBR_OF_STARTS,                \n\
       CAST(bs.STARTS_PER_TURN_PER_WEEK AS DECIMAL(11, 2))                                            AS STARTS_PER_TURN,              \n\
       bs.break_even_point,                                                                                                            \n\
       CAST(CAST(bts.nbr_of_starts AS DECIMAL(10, 2)) /                                                                                \n\
            (select nbr_of_weeks from scenariocfg where SCENARIO_ID = :scenarioId) AS DECIMAL(10, 2)) AS nbr_of_starts_per_start_type, \n\
       bts.START_TYPE                                                                                                                  \n\
from BOARD_STATS bs                                                                                                                    \n\
         LEFT JOIN board_train_starts bts                                                                                              \n\
                   on bs.scenario_id = bts.scenario_id                                                                                 \n\
                       AND bs.distr = bts.distr                                                                                        \n\
                       AND bs.sub_distr = bts.sub_distr                                                                                \n\
                       AND bs.board_name = bts.board_name                                                                              \n\
                       AND bs.craft = bts.craft                                                                                        \n\
where bs.scenario_id = :scenarioId                                                                                                     \n\
  and (:distr IS NULL                                                                                                                  \n\
    OR bs.distr = :distr)                                                                                                              \n\
  and (:subDistr IS NULL                                                                                                               \n\
    OR bs.sub_distr = :subDistr)                                                                                                       \n\
  AND (:boardType IS NULL                                                                                                              \n\
    OR bs.board_type = :boardType)                                                                                                     \n\
ORDER BY bs.distr, bs.sub_distr,bs.board_name, bs.board_type, bs.craft, bts.START_TYPE

ExpPoolSetUpTerminal.findTerminalsByPlanIdAndDistrAndSubDistrAndPoolName=                                         \n\
WITH t1                                                                                                           \n\
     AS (SELECT ps.pool_set_up_id,                                                                                \n\
                ps.distr,                                                                                         \n\
                ps.sub_distr,                                                                                     \n\
                ps.pool_name,                                                                                     \n\
                ps.pool_home_away,                                                                                \n\
                ps.os,                                                                                            \n\
                ps.lead_time,                                                                                     \n\
                ps.regular_rest_time,                                                                             \n\
                ps.undisturbed_rest,                                                                              \n\
                pr.craft,                                                                                         \n\
                CASE                                                                                              \n\
                  WHEN pr.craft = 'CO' THEN pr.tieup_thru                                                         \n\
                  ELSE NULL                                                                                       \n\
                END AS co_tieup_thru,                                                                             \n\
                CASE                                                                                              \n\
                  WHEN pr.craft = 'CO' THEN pr.tieup_short                                                        \n\
                  ELSE NULL                                                                                       \n\
                END AS co_tieup_short,                                                                            \n\
                CASE                                                                                              \n\
                  WHEN pr.craft = 'EN' THEN pr.tieup_thru                                                         \n\
                  ELSE NULL                                                                                       \n\
                END AS en_tieup_thru,                                                                             \n\
                CASE                                                                                              \n\
                  WHEN pr.craft = 'EN' THEN pr.tieup_short                                                        \n\
                  ELSE NULL                                                                                       \n\
                END AS en_tieup_short                                                                             \n\
         FROM   pool_set_up_view ps                                                                                    \n\
                LEFT JOIN pool_rotation_history pr                                                                \n\
                       ON ps.pool_set_up_id = pr.pool_set_up_id                                                   \n\
         WHERE  ps.plan_id = :planId                                                                              \n\
                and  ( :distr IS NULL                                                                             \n\
                        OR ps.distr = :distr )                                                                    \n\
                AND ( :subDistr IS NULL                                                                           \n\
                        OR ps.sub_distr = :subDistr )                                                             \n\
                AND ( :poolName IS NULL                                                                           \n\
                    OR ps.pool_name = :poolName )),                                                               \n\
     t2                                                                                                           \n\
     AS (SELECT *                                                                                                 \n\
         FROM   t1                                                                                                \n\
         WHERE  t1.craft = 'CO'),                                                                                 \n\
     t3                                                                                                           \n\
     AS (SELECT *                                                                                                 \n\
         FROM   t1                                                                                                \n\
         WHERE  t1.craft = 'EN'),                                                                                 \n\
     t4                                                                                                           \n\
     AS (SELECT *                                                                                                 \n\
         FROM   t1                                                                                                \n\
         WHERE  t1.craft IS NULL),                                                                                \n\
     t5                                                                                                           \n\
     AS (SELECT *                                                                                                 \n\
         FROM   t1                                                                                                \n\
         WHERE  t1.craft NOT IN ( 'CO', 'EN' ))                                                                   \n\
SELECT CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_id                                                                        \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_id                                                                    \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_id                                                            \n\
                 ELSE s_id                                                                                        \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS id,                                                                                                 \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_distr                                                                     \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_distr                                                                 \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_distr                                                         \n\
                 ELSE s_distr                                                                                     \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS distr,                                                                                              \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_sub_distr                                                                 \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_sub_distr                                                             \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_sub_distr                                                     \n\
                 ELSE s_sub_distr                                                                                 \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS sub_distr,                                                                                          \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_pool                                                                      \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_pool                                                                  \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_pool                                                          \n\
                 ELSE s_pool                                                                                      \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS pool_name,                                                                                          \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_pool_home                                                                 \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_pool_home                                                             \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_pool_home                                                     \n\
                 ELSE s_pool_home                                                                                 \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS pool_home_away,                                                                                     \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_os                                                                        \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_os                                                                    \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_os                                                            \n\
                 ELSE s_os                                                                                        \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS os,                                                                                                 \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_lead                                                                      \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_lead                                                                  \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_lead                                                          \n\
                 ELSE s_lead                                                                                      \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS lead_time,                                                                                          \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_rest                                                                      \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_rest                                                                  \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_rest                                                          \n\
                 ELSE s_rest                                                                                      \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS rest_hours,                                                                                         \n\
       CASE                                                                                                       \n\
         WHEN co_id IS NOT NULL THEN co_u_rest                                                                    \n\
         ELSE                                                                                                     \n\
           CASE                                                                                                   \n\
             WHEN en_id IS NOT NULL THEN en_u_rest                                                                \n\
             ELSE                                                                                                 \n\
               CASE                                                                                               \n\
                 WHEN null_id IS NOT NULL THEN null_u_rest                                                        \n\
                 ELSE s_u_rest                                                                                    \n\
               END                                                                                                \n\
           END                                                                                                    \n\
       END AS undisturbed_rest,                                                                                   \n\
       co_tieup_thru as tieup_thru_co,                                                                            \n\
        co_tieup_short as tieup_short_co,                                                                         \n\
        en_tieup_thru as tieup_thru_en,                                                                           \n\
        en_tieup_short as tieup_short_en                                                                          \n\
FROM   (SELECT t2.pool_set_up_id    AS co_id,                                                                     \n\
               t2.distr             AS co_distr,                                                                  \n\
               t2.sub_distr         AS co_sub_distr,                                                              \n\
               t2.pool_name         AS co_pool,                                                                   \n\
               t2.pool_home_away    AS co_pool_home,                                                              \n\
               t2.os                AS co_os,                                                                     \n\
               t2.lead_time         AS co_lead,                                                                   \n\
               t2.regular_rest_time AS co_rest,                                                                   \n\
               t2.undisturbed_rest  AS co_u_rest,                                                                 \n\
               t2.co_tieup_thru,                                                                                  \n\
               t2.co_tieup_short,                                                                                 \n\
               t3.pool_set_up_id    AS en_id,                                                                     \n\
               t3.distr             AS en_distr,                                                                  \n\
               t3.sub_distr         AS en_sub_distr,                                                              \n\
               t3.pool_name         AS en_pool,                                                                   \n\
               t3.pool_home_away    AS en_pool_home,                                                              \n\
               t3.os                AS en_os,                                                                     \n\
               t3.lead_time         AS en_lead,                                                                   \n\
               t3.regular_rest_time AS en_rest,                                                                   \n\
               t3.undisturbed_rest  AS en_u_rest,                                                                 \n\
               t3.en_tieup_thru,                                                                                  \n\
               t3.en_tieup_short,                                                                                 \n\
               t4.pool_set_up_id    AS null_id,                                                                   \n\
               t4.distr             AS null_distr,                                                                \n\
               t4.sub_distr         AS null_sub_distr,                                                            \n\
               t4.pool_name         AS null_pool,                                                                 \n\
               t4.pool_home_away    AS null_pool_home,                                                            \n\
               t4.os                AS null_os,                                                                   \n\
               t4.lead_time         AS null_lead,                                                                 \n\
               t4.regular_rest_time AS null_rest,                                                                 \n\
               t4.undisturbed_rest  AS null_u_rest,                                                               \n\
               t5.pool_set_up_id    AS s_id,                                                                      \n\
               t5.distr             AS s_distr,                                                                   \n\
               t5.sub_distr         AS s_sub_distr,                                                               \n\
               t5.pool_name         AS s_pool,                                                                    \n\
               t5.pool_home_away    AS s_pool_home,                                                               \n\
               t5.os                AS s_os,                                                                      \n\
               t5.lead_time         AS s_lead,                                                                    \n\
               t5.regular_rest_time AS s_rest,                                                                    \n\
               t5.undisturbed_rest  AS s_u_rest                                                                   \n\
        FROM   t2                                                                                                 \n\
               FULL OUTER JOIN t3                                                                                 \n\
                            ON t2.pool_set_up_id = t3.pool_set_up_id                                              \n\
               FULL OUTER JOIN t4                                                                                 \n\
                            ON t2.pool_set_up_id = t4.pool_set_up_id                                              \n\
               FULL OUTER JOIN t5                                                                                 \n\
                            ON t2.pool_set_up_id = t5.pool_set_up_id                                              \n\
        ) t                                                                                                       \n\
ORDER BY distr, sub_distr, pool_name, pool_home_away

ExpWorkRestProfTurn.getExpWorkRestProfTurns=                                   \n\
SELECT distr,                                                                                                     \n\
       sub_distr,                                                                                                 \n\
       pool_name,                                                                                                 \n\
       prof_name,                                                                                                 \n\
       turn_id,                                                                                                   \n\
       craft                                                                                                      \n\
FROM   (SELECT w.distr,                                                                                           \n\
               w.sub_distr,                                                                                       \n\
               w.pool_name,                                                                                       \n\
               w.prof_name,                                                                                       \n\
               t.turn_id,                                                                                         \n\
               t.craft,                                                                                           \n\
               p.pool_name        AS regular_pool,                                                                \n\
               exb.exb_board_name AS exb_pool                                                                     \n\
        FROM   work_rest_prof w                                                                                   \n\
                   LEFT JOIN work_rest_prof_tgrp t                                                                \n\
                             ON w.work_rest_prof_id = t.work_rest_prof                                            \n\
                   LEFT JOIN pool_set_up_view p                                                                        \n\
                             ON w.plan_id = p.plan_id                                                             \n\
                                 AND w.distr = p.distr                                                            \n\
                                 AND w.sub_distr = p.sub_distr                                                    \n\
                                 AND w.pool_name = p.pool_name                                                    \n\
                   LEFT JOIN extraboard_view exb                                                                       \n\
                             ON w.plan_id = exb.plan_id                                                           \n\
                                 AND w.distr = exb.distr                                                          \n\
                                 AND w.sub_distr = exb.sub_distr                                                  \n\
                                 AND w.pool_name = exb.exb_board_name                                             \n\
        WHERE  w.plan_id = :planId) tt                                                                            \n\
WHERE  ( :distr IS NULL                                                                                           \n\
    OR distr = :distr )                                                                                           \n\
  AND ( :subDistr IS NULL                                                                                         \n\
    OR sub_distr = :subDistr )                                                                                    \n\
  AND ( :poolName IS NULL                                                                                         \n\
    OR pool_name = :poolName )                                                                                    \n\
  AND ( :poolType IS NULL                                                                                         \n\
    OR ( :poolType != 'SS'                                                                                        \n\
        OR regular_pool IS NOT NULL ) )                                                                           \n\
  AND ( :poolType IS NULL                                                                                         \n\
    OR ( :poolType != 'EB'                                                                                        \n\
        OR exb_pool IS NOT NULL ) )                                                                               \n\
  ORDER BY distr, SUB_DISTR, POOL_NAME, PROF_NAME, TURN_ID

ExpWorkRestProfTurn.getExpWorkRestProfRotations=                                          \n\
SELECT distr,                                                                                                                \n\
       sub_distr,                                                                                                            \n\
       pool_name,                                                                                                            \n\
       prof_name,                                                                                                            \n\
       ord_seq,                                                                                                              \n\
       rotn_id,                                                                                                              \n\
       tt.work_into_rest_day_hrs,                                                                                            \n\
       tt.work_into_rest_day_mkup_hrs,                                                                                       \n\
       tt.futr_mkup_perd_hrs,                                                                                                \n\
       nbr_off_days,                                                                                                         \n\
       nbr_on_days,                                                                                                          \n\
       use_remainder                                                                                                         \n\
FROM (SELECT w.distr,                                                                                                        \n\
             w.sub_distr,                                                                                                    \n\
             w.pool_name,                                                                                                    \n\
             w.prof_name,                                                                                                    \n\
             t.ord_seq,                                                                                                      \n\
             t.rotn_id,                                                                                                      \n\
            CAST(cast(t.work_into_rest_day as DECIMAL(10, 2)) / 60.0 AS DECIMAL(10, 2))      AS work_into_rest_day_hrs,      \n\
            CAST(cast(t.work_into_rest_day_mkup as DECIMAL(10, 2)) / 60.0 AS DECIMAL(10, 2)) AS work_into_rest_day_mkup_hrs, \n\
            CAST(cast(t.futr_mkup_perd as DECIMAL(10, 2)) / 60.0 AS DECIMAL(10, 2))          AS futr_mkup_perd_hrs,          \n\
             t.nbr_off_days,                                                                                                 \n\
             t.nbr_on_days,                                                                                                  \n\
             t.use_remainder,                                                                                                \n\
             p.pool_name                                                          AS regular_pool,                           \n\
             exb.exb_board_name                                                   AS exb_pool                                \n\
      FROM work_rest_prof w                                                                                                  \n\
               LEFT JOIN work_rest_prof_rotn t                                                                               \n\
                         ON w.work_rest_prof_id = t.work_rest_prof                                                           \n\
               LEFT JOIN pool_set_up_view p                                                                                       \n\
                         ON w.plan_id = p.plan_id                                                                            \n\
                             AND w.distr = p.distr                                                                           \n\
                             AND w.sub_distr = p.sub_distr                                                                   \n\
                             AND w.pool_name = p.pool_name                                                                   \n\
               LEFT JOIN extraboard_view exb                                                                                      \n\
                         ON w.plan_id = exb.plan_id                                                                          \n\
                             AND w.distr = exb.distr                                                                         \n\
                             AND w.sub_distr = exb.sub_distr                                                                 \n\
                             AND w.pool_name = exb.exb_board_name                                                            \n\
      WHERE w.plan_id = :planId) tt                                                                                          \n\
WHERE (:distr IS NULL                                                                                                        \n\
    OR distr = :distr)                                                                                                       \n\
  AND (:subDistr IS NULL                                                                                                     \n\
    OR sub_distr = :subDistr)                                                                                                \n\
  AND (:poolName IS NULL                                                                                                     \n\
    OR pool_name = :poolName)                                                                                                \n\
  AND (:poolType IS NULL                                                                                                     \n\
    OR (:poolType != 'SS'                                                                                                    \n\
        OR regular_pool IS NOT NULL))                                                                                        \n\
  AND (:poolType IS NULL                                                                                                     \n\
    OR (:poolType != 'EB'                                                                                                    \n\
        OR exb_pool IS NOT NULL))                                                                                            \n\
ORDER BY DISTR, SUB_DISTR, POOL_NAME, PROF_NAME, ORD_SEQ

TrnTypeDAOImpl.selectTrnTypeSql=      \n\
    select distinct                     \n\
        substr(trn,1,3) as trn_symb,    \n\
        trn_typ as trn_type,            \n\
        orgn_os as from_os,             \n\
        dest_os as to_os                \n\
    from sch_trn_symbol                 \n\
    where                               \n\
        trn is not null and             \n\
        trn_type is not null            \n\
    order by trn_orgn_dt desc

BssTrnStrtDAOImpl.selectBssTrnStrtSql=      \n\
  select                                              \n\
      trn_symb,                                       \n\
      trn_type,                                       \n\
      orig_dep_oprn_stn as from_os,                   \n\
      dest_arvl_oprn_stn as to_os,                    \n\
      trn_crew_dist_segm_valu line_segment            \n\
  from cpo.bss_trn_strt where trn_type <> 'YDJB' and  \n\
    trn_symb is not null and                          \n\
    trn_type is not null

HistDataDAOImpl.selectRecentExtlStatEarnSql=   \n\
        select                                             \n\
            dep_stn_name as from_os,                                  \n\
            arvl_stn_name as to_os,                                 \n\
            crew_prof_name as crew_profile_id,                                \n\
            trn_id,                                        \n\
            trn_symb,                                      \n\
            crew_orgn_stn_code as crew_orgn_os,                            \n\
            load_ts,                                       \n\
            on_duty_ts,                                    \n\
            null as train_orgn_os,                   \n\
            null as src_sys_last_uptd_date,                        \n\
            null as line_segment       \n\
        from cpo.nsor_extl_stat_earn_view                  \n\
        where on_duty_ts > sysdate-90                      \n\
            and trn_symb not in ('470', '471', '472', '473', '474', '475', '476', '477',         \n\
            '478', '479', '480', '481', '482', '483', '484', '485', '486', '487', '488',         \n\
            '489', 'X70', 'X71', 'X72', 'X73', 'X74', 'X75', 'X76', 'X77', 'X78', 'X79',         \n\
            'X80', 'X81', 'X82', 'X83', 'X84', 'X85', 'X86', 'X87', 'X88', 'X89') and            \n\
            crew_prof_name is not null and                                                       \n\
            crew_orgn_stn_code is not null

ApcnDataDAOImpl.selectApcnFullTableSql=    \n\
       select distinct                                 \n\
            dep_stn_code as from_os,           \n\
            arvl_stn_code as to_os,       \n\
            crew_pool_prof_name as crew_profile_id, \n\
            null as trn_id,                                        \n\
            trn_symb,                      \n\
            crew_orgn_stn_code as crew_orgn_os,            \n\
            null as load_ts,               \n\
            null as on_duty_ts,               \n\
            stn_code as train_orgn_os,                      \n\
            src_sys_last_uptd_date,                        \n\
            trn_line_segm_valu as line_segment       \n\
        from cpo.nsor_ascd_crew_notn_prof_view   \n\
        order by src_sys_last_uptd_date desc

maxMemoryBytes=24000000000
maxCpuMillis=5000
ttlMillis=300000

TrainDistsDAOImpl.getAllDepartureDistributionsSql=     \n\
    select                                             \n\
        departure_distribution,                        \n\
        departure_lamda_1,                             \n\
        departure_lower,                               \n\
        departure_mean_1,                              \n\
        departure_medium,                              \n\
        departure_rate,                                \n\
        departure_scale,                               \n\
        departure_shape,                               \n\
        departure_shift_constant,                      \n\
        departure_upper,                               \n\
        departure_variance_1,                          \n\
        dow,                                           \n\
        from_os,                                       \n\
        line_segment,                                  \n\
        process_date,                                  \n\
        total_count,                                   \n\
        trn_symbol,                                    \n\
        update_ts                                      \n\
    from crewpro_transit_time.departure_distribution   \n\
    where process_date = (select max(process_date)     \n\
    from crewpro_transit_time.departure_distribution)  \n\
    order by process_date, trn_symbol, line_segment, dow, from_os

TrainDistsDAOImpl.getAllTransitTimeDistributionsSql=      \n\
    select                                                \n\
        transit_distribution,                             \n\
        transit_lamda_1,                                  \n\
        transit_lower,                                    \n\
        transit_mean_1,                                   \n\
        transit_medium,                                   \n\
        transit_rate,                                     \n\
        transit_scale,                                    \n\
        transit_shape,                                    \n\
        transit_shift_constant,                           \n\
        transit_upper,                                    \n\
        transit_variance_1,                               \n\
        dow,                                              \n\
        from_os,                                          \n\
        to_os,                                            \n\
        line_segment,                                     \n\
        process_date,                                     \n\
        total_count,                                      \n\
        trn_symbol,                                       \n\
        update_ts                                         \n\
    from crewpro_transit_time.transit_time_distribution   \n\
    where process_date = (select max(process_date)        \n\
    from crewpro_transit_time.transit_time_distribution)  \n\
    order by process_date, trn_symbol, line_segment, dow, from_os, to_os

TrainDistsDAOImpl.getAllTtDistByTrainTypeSql=      \n\
    select                                                \n\
        transit_distribution,                             \n\
        transit_lamda_1,                                  \n\
        transit_lower,                                    \n\
        transit_mean_1,                                   \n\
        transit_medium,                                   \n\
        transit_rate,                                     \n\
        transit_scale,                                    \n\
        transit_shape,                                    \n\
        transit_shift_constant,                           \n\
        transit_upper,                                    \n\
        transit_variance_1,                               \n\
        dow,                                              \n\
        from_os,                                          \n\
        to_os,                                            \n\
        line_segment,                                     \n\
        process_date,                                     \n\
        total_count,                                      \n\
        trn_type,                                         \n\
        update_ts                                         \n\
    from crewpro_transit_time.tt_dist_by_trn_type       \n\
    where process_date = (select max(process_date)        \n\
    from crewpro_transit_time.tt_dist_by_trn_type)      \n\
    order by process_date, trn_type, line_segment, dow, from_os, to_os

StudyTree.findAllStudiesTree=\n\
		SELECT DISTINCT st.study_id,                                        \n\
                                st.study_name,                              \n\
                                pl.plan_name,                               \n\
                                st.description    AS study_desc,            \n\
                                st.user_id,                                 \n\
                                st.create_ts,                               \n\
                                st.error_message,                           \n\
                                st.update_ts,                               \n\
                                st.update_user_id,                          \n\
                                st.status         status,                   \n\
                                st.start_date,                              \n\
                                st.end_date,                                \n\
                                st.study_type,                              \n\
                                st.local_yard_job_start_date,               \n\
                                st.local_yard_job_end_date,                 \n\
                                st.algorithm,                               \n\
                                st.opd_scenario,                            \n\
                                sc.scenario_id,                             \n\
                                sc.plan_id,                                 \n\
                                sc.scen_name,                               \n\
                                sc.description    scen_desc,                \n\
                                sc.status         AS scen_status,           \n\
                                sc.user_id        AS scen_user_id,          \n\
                                sc.create_ts      AS scen_create_ts,        \n\
                                sc.error_message  AS scen_error_message,    \n\
                                sc.update_ts      AS scen_update_ts,        \n\
                                sc.update_user_id AS scen_update_user_id,   \n\
                                st.history_start_date,                      \n\
                                st.history_end_date,                        \n\
                                st.dept_dist,                               \n\
                                st.transit_dist,                            \n\
                                st.opd_plan,                                \n\
                                st.opd_trn_types,                           \n\
                                st.historical_trn_types                     \n\
FROM   study st                                                             \n\
                       LEFT OUTER JOIN scenario sc                          \n\
                                    ON st.study_id = sc.study_id            \n\
		                            AND sc.status <> 'DELETED'              \n\
		                            AND sc.scenario_type <> 'CCP_MON'       \n\
                       LEFT OUTER JOIN plan pl                              \n\
                                    ON sc.plan_id = pl.plan_id              \n\
		                            AND pl.status <> 'DELETED'              \n\
		                            AND pl.plan_type <> 'CCP_MON'           \n\
		        WHERE st.status <> 'DELETED'                                \n\
		              AND st.study_type <> 'CCP_MON'                        \n\
                ORDER  BY st.study_id,                                      \n\
                          sc.scenario_id

TrainSummary.findByStudyIdAndTrnCrewDistrAndTrnTypeAndTrnSymbCrewOrgnOsAndAssignFlagAndRowNumBetween=\n\
SELECT                                           \n\
       trn_symb,                                 \n\
       from_os,                                  \n\
       to_os,                                    \n\
       trn_type,                                 \n\
       COUNT(trn)                                \n\
       AS num_trains                             \n\
FROM   train                                     \n\
WHERE  study_id = :studyId                       \n\
       AND ( :trnCrewDistr IS NULL               \n\
              OR trn_crew_distr = :trnCrewDistr )\n\
       AND ( :trnType IS NULL                    \n\
              OR trn_type = :trnType )           \n\
       AND ( :trnSymb IS NULL                    \n\
              OR trn_symb = :trnSymb )           \n\
       AND ( :crewOrgnOs IS NULL                 \n\
              OR crew_orgn_os = :crewOrgnOs )    \n\
       AND ( :assignFlag IS NULL                 \n\
              OR assign_flag = :assignFlag )     \n\
GROUP  BY trn_symb,                              \n\
          trn_type,                              \n\
          trn_crew_distr,                        \n\
          from_os,                               \n\
          to_os                                  \n\
ORDER BY :ORDER_BY_CLAUSE                        \n\
LIMIT  :noOfRows OFFSET :startRowNum             \n\

TrainViewerReport.getTrainsBySummaryInfo=\n\
SELECT                                                \n\
    t.TRAIN_ID,                                       \n\
    t.STUDY_ID,                                       \n\
    t.FROM_TS,                                        \n\
    t.ASSIGN_EN_XB_SUB_DISTR,                         \n\
    t.ASSIGN_CO_XB_DISTR,                             \n\
    t.TO_OS,                                          \n\
    t.CREW_ORGN_OS,                                   \n\
    t.ASSIGN_CO_XB_EXB,                               \n\
    t.FROM_LOC_SEQ_NR,                                \n\
    t.TRN_TYPE,                                       \n\
    t.ASSIGN_FLAG,                                    \n\
    t.FROM_OS,                                        \n\
    t.ASSIGN_EN_XB_DISTR,                             \n\
    t.ASSIGN_CO_XB_SUB_DISTR,                         \n\
    t.TO_TS,                                          \n\
    CAST(t.INIT_TERMINAL_TIME AS                      \n\
    DECIMAL(9, 2)) AS INIT_TERMINAL_TIME,             \n\
    t.TO_LOC_SEQ_NR,                                  \n\
    t.TRN,                                            \n\
    t.TRN_ORGN_DT,                                    \n\
    t.TRN_CREW_DISTR,                                 \n\
    CAST(t.SEG_TRANSIT_TIME AS                        \n\
    DECIMAL(9, 2)) AS SEG_TRANSIT_TIME,               \n\
    t.PROFILE_ID,                                     \n\
    t.ASSIGN_EN_XB_EXB,                               \n\
    t.TRN_SECTION,                                    \n\
    t.RTE,                                            \n\
    t.DEPARTURE_DISTRIBUTION,                         \n\
    t.DEPT_LAMBDA1,                                   \n\
    t.DEPT_SHAPE,                                     \n\
    t.DEPT_LOWER,                                     \n\
    t.DEPT_SHIFT_CONSTANT,                            \n\
    t.DEPT_MEAN1,                                     \n\
    t.DEPT_MEDIUM,                                    \n\
    t.DEPT_RATE,                                      \n\
    t.DEPT_SCALE,                                     \n\
    t.DEPT_VARIANCE1,                                 \n\
    t.DEPT_UPPER,                                     \n\
    t.TRANSIT_TIME_DISTRIBUTION,                      \n\
    t.TRANSIT_SHIFT_CONSTANT,                         \n\
    t.TRANSIT_SHAPE,                                  \n\
    t.TRANSIT_LOWER,                                  \n\
    t.TRANSIT_VARIANCE1,                              \n\
    t.TRANSIT_LAMBDA1,                                \n\
    t.TRANSIT_MEDIUM,                                 \n\
    t.TRANSIT_SCALE,                                  \n\
    t.TRANSIT_UPPER,                                  \n\
    t.TRANSIT_RATE,                                   \n\
    t.TRANSIT_MEAN1,                                  \n\
    t.PROF_MATCH_CODE,                                \n\
    t.EFF_DT,                                         \n\
    t.EXP_DT,                                         \n\
    t.COST_CTR,                                       \n\
    t.COST_CTR_DESC,                                  \n\
    t.HIRE_GRP,                                       \n\
    t.HIRE_GRP_DESC,                                  \n\
    t.EN_CNT,                                         \n\
    t.TR_CNT                                          \n\
FROM   TRAIN t                                        \n\
WHERE  study_id = :studyId                            \n\
        AND trn_symb = :trnSymb                       \n\
        AND from_os = :fromOs                         \n\
        AND to_os = :toOs                             \n\
        AND trn_type = :trnType                       \n\
        AND ( :trnCrewDistr IS NULL                   \n\
               OR trn_crew_distr = :trnCrewDistr )    \n\
        AND ( :crewOrgnOs IS NULL                     \n\
               OR crew_orgn_os = :crewOrgnOs )        \n\
        AND ( :assignFlag IS NULL                     \n\
               OR assign_flag = :assignFlag )         \n\
        ORDER BY train_id

TrainViewerReport.getTrainExportDetails=\n\
SELECT                                                \n\
    t.TRAIN_ID,                                       \n\
    t.STUDY_ID,                                       \n\
    t.FROM_TS,                                        \n\
    t.ASSIGN_EN_XB_SUB_DISTR,                         \n\
    t.ASSIGN_CO_XB_DISTR,                             \n\
    t.TO_OS,                                          \n\
    t.CREW_ORGN_OS,                                   \n\
    t.ASSIGN_CO_XB_EXB,                               \n\
    t.FROM_LOC_SEQ_NR,                                \n\
    t.TRN_TYPE,                                       \n\
    t.ASSIGN_FLAG,                                    \n\
    t.FROM_OS,                                        \n\
    t.ASSIGN_EN_XB_DISTR,                             \n\
    t.ASSIGN_CO_XB_SUB_DISTR,                         \n\
    t.TO_TS,                                          \n\
    CAST(Round((t.INIT_TERMINAL_TIME) / 60.0, 2) AS   \n\
    DECIMAL(9, 2)) AS INIT_TERMINAL_TIME,             \n\
    t.TO_LOC_SEQ_NR,                                  \n\
    t.TRN,                                            \n\
    t.TRN_ORGN_DT,                                    \n\
    t.TRN_CREW_DISTR,                                 \n\
    CAST(Round((t.SEG_TRANSIT_TIME) / 60.0, 2) AS     \n\
    DECIMAL(9, 2)) AS SEG_TRANSIT_TIME,               \n\
    t.PROFILE_ID,                                     \n\
    t.ASSIGN_EN_XB_EXB,                               \n\
    t.TRN_SECTION,                                    \n\
    t.RTE,                                            \n\
    t.DEPARTURE_DISTRIBUTION,                         \n\
    t.DEPT_LAMBDA1,                                   \n\
    t.DEPT_SHAPE,                                     \n\
    t.DEPT_LOWER,                                     \n\
    t.DEPT_SHIFT_CONSTANT,                            \n\
    t.DEPT_MEAN1,                                     \n\
    t.DEPT_MEDIUM,                                    \n\
    t.DEPT_RATE,                                      \n\
    t.DEPT_SCALE,                                     \n\
    t.DEPT_VARIANCE1,                                 \n\
    t.DEPT_UPPER,                                     \n\
    t.TRANSIT_TIME_DISTRIBUTION,                      \n\
    t.TRANSIT_SHIFT_CONSTANT,                         \n\
    t.TRANSIT_SHAPE,                                  \n\
    t.TRANSIT_LOWER,                                  \n\
    t.TRANSIT_VARIANCE1,                              \n\
    t.TRANSIT_LAMBDA1,                                \n\
    t.TRANSIT_MEDIUM,                                 \n\
    t.TRANSIT_SCALE,                                  \n\
    t.TRANSIT_UPPER,                                  \n\
    t.TRANSIT_RATE,                                   \n\
    t.TRANSIT_MEAN1,                                  \n\
    t.PROF_MATCH_CODE,                                \n\
    t.EFF_DT,                                         \n\
    t.EXP_DT,                                         \n\
    t.COST_CTR,                                       \n\
    t.COST_CTR_DESC,                                  \n\
    t.HIRE_GRP,                                       \n\
    t.HIRE_GRP_DESC,                                  \n\
    t.EN_CNT,                                         \n\
    t.TR_CNT                                          \n\
FROM TRAIN t                                          \n\
WHERE study_id = :studyId                             \n\
    AND (:trnCrewDistr IS NULL                        \n\
    OR trn_crew_distr = :trnCrewDistr)                \n\
    AND (:trnType IS NULL                             \n\
    OR trn_type = :trnType)                           \n\
    AND (:trnSymb IS NULL                             \n\
    OR trn_symb = :trnSymb)                           \n\
    AND (:crewOrgnOs IS NULL                          \n\
    OR crew_orgn_os = :crewOrgnOs)                    \n\
    AND (:assignFlag IS NULL                          \n\
    OR assign_flag = :assignFlag)                     \n\
    ORDER BY train_id                                 \n\
    LIMIT :numRows OFFSET :startRow

TrainViewerFilter.findByStudyId=\n\
		SELECT DISTINCT trn_crew_distr,                                         \n\
                        crew_orgn_os,                                           \n\
                        trn_type,                                               \n\
                        trn_symb,                                               \n\
                        assign_flag                                             \n\
        FROM   train                                                            \n\
        WHERE  study_id = :studyId                                              \n\
        ORDER BY trn_crew_distr ,crew_orgn_os ,trn_type, trn_symb, assign_flag

Diagnostic.findByStudyIdAndSegTransitTimeGreaterThan=\n\
		SELECT trn_symb,                                       \n\
               trn_type,                                       \n\
               trn_crew_distr,                                 \n\
               from_os,                                        \n\
               to_os,                                          \n\
               seg_transit_time,                               \n\
               COUNT(trn) AS no_of_trns                        \n\
        FROM   train                                           \n\
        WHERE  study_id = :studyId                             \n\
               AND seg_transit_time > :segTransitTime          \n\
        GROUP  BY trn_symb,                                    \n\
                  trn_type,                                    \n\
                  trn_crew_distr,                              \n\
                  from_os,                                     \n\
                  to_os,                                       \n\
                  seg_transit_time                             \n\
        ORDER BY trn_symb, trn_type, trn_crew_distr

Diagnostic.findByStudyIdAndSegTransitTimeBetween=\n\
		SELECT trn_symb,                                            \n\
				       trn_type,                                    \n\
				       trn_crew_distr,                              \n\
				       from_os,                                     \n\
				       to_os,                                       \n\
				       seg_transit_time,                            \n\
				       COUNT(trn) AS no_of_trns                     \n\
				FROM   train                                        \n\
				WHERE  study_id = :studyId                          \n\
				       AND seg_transit_time <= :segTransitTimeEnd   \n\
				       AND seg_transit_time >= :segTransitTimeStart \n\
				GROUP  BY trn_symb,                                 \n\
				          trn_type,                                 \n\
				          trn_crew_distr,                           \n\
				          from_os,                                  \n\
				          to_os,                                    \n\
				          seg_transit_time                          \n\
				ORDER BY trn_symb, trn_type, trn_crew_distr

Diagnostic.findByStudyIdAndProfileIdIsNull=\n\
		SELECT trn_symb,                             \n\
               trn_type,                             \n\
               trn_crew_distr,                       \n\
               from_os,                              \n\
               to_os,                                \n\
               seg_transit_time,                     \n\
               COUNT(trn) AS no_of_trns              \n\
        FROM   train                                 \n\
        WHERE  study_id = :studyId                   \n\
               AND profile_id IS NULL                \n\
        GROUP  BY trn_symb,                          \n\
                  trn_type,                          \n\
                  trn_crew_distr,                    \n\
                  from_os,                           \n\
                  to_os,                             \n\
                  seg_transit_time                   \n\
        ORDER BY trn_symb, trn_type, trn_crew_distr

LineSegment.findByStudyId=\n\
        SELECT DISTINCT trn_crew_distr  \n\
         FROM   train                   \n\
         WHERE  study_id = :studyId     \n\
            ORDER BY trn_crew_distr

SimulationTrainReport.getSimulationTrainReport=\n\
    select                                                                                     \n\
        a.trn,                                                                                   \n\
        a.trn_crew_distr,                                                                        \n\
        a.trn_orgn_dt,                                                                           \n\
        a.trn_section,                                                                           \n\
        a.rte,                                                                                   \n\
        a.from_loc_seq_nr,                                                                       \n\
        a.from_os,                                                                               \n\
        a.to_os,                                                                                 \n\
        a.from_ts,                                                                               \n\
        a.to_ts,                                                                                 \n\
        a.trn_type,                                                                              \n\
        a.status,                                                                                \n\
        a.crew_os,                                                                               \n\
        a.crew_profile_id,                                                                       \n\
        a.recrew_duration,                                                                       \n\
        a.delay_due_to_crew_duration,                                                            \n\
        a.co_profile_distr,                                                                      \n\
        a.co_profile_sub_distr,                                                                  \n\
        a.co_profile_pool_name,                                                                  \n\
        a.co_profile_pool_home_away,                                                             \n\
        a.co_profile_pool_os,                                                                    \n\
        a.co_on_duty_start_time,                                                                 \n\
        a.co_on_duty_duration,                                                                   \n\
        a.en_profile_distr,                                                                      \n\
        a.en_profile_sub_distr,                                                                  \n\
        a.en_profile_pool_name,                                                                  \n\
        a.en_profile_pool_home_away,                                                             \n\
        a.en_profile_pool_os,                                                                    \n\
        a.en_on_duty_start_time,                                                                 \n\
        a.en_on_duty_duration,                                                                   \n\
        a.arvl_flag,                                                                             \n\
        b.prof_match_code,                                                                       \n\
        b.orig_prof_match_code,                                                                  \n\
        b.orig_profile_id,                                                                       \n\
        b.orig_crew_orgn_os                                                                      \n\
    from  SIMULATION_TRAIN_OUTPUT a                                                              \n\
        left outer join                                                                          \n\
        SCEN_TRAIN b on                                                                          \n\
             a.scenario_id = b.scenario_id and                                                   \n\
             a.trn = b.trn and                                                                   \n\
             a.trn_orgn_dt = b.trn_orgn_dt and                                                   \n\
             a.trn_crew_distr = b.trn_crew_distr and                                             \n\
             a.from_loc_seq_nr = b.from_loc_seq_nr                                               \n\
    where a.scenario_id = :scenarioId                                                            \n\
        and (:distr is NULL OR (a.co_profile_distr= :distr or a.en_profile_distr = :distr))      \n\
        and (:trainType is NULL OR a.trn_type =:trainType)                                       \n\
        and (:lineSeg is NULL OR a.trn_crew_distr = :lineSeg)                                    \n\
        and (:trainId is NULL OR Substr(a.trn, 1, 3) = :trainId)                                 \n\
        and (:fromOs is NULL OR a.from_os = :fromOs)                                             \n\
        and (:delay is NULL                                                                      \n\
        OR (('CR_DELAY' =:delay AND a.delay_due_to_crew_duration > 0)                            \n\
         OR ('NO_DELAY' =:delay AND a.recrew_duration <=0 AND a.delay_due_to_crew_duration <=0)  \n\
         OR ('RC_DELAY' =:delay AND a.recrew_duration > 0)                                       \n\
         ))                                                                                      \n\
    order by a.trn_type, a.trn_crew_distr, a.co_profile_distr, a.en_profile_distr, a.trn

BoardSummaryReportDetail.findBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft=\n\
		SELECT start_type,                                         \n\
               nbr_of_starts                                       \n\
        FROM   board_train_starts                                  \n\
        WHERE  scenario_id = :scenarioId                           \n\
               AND (:boardName IS NULL OR board_name = :boardName) \n\
               AND (:distr IS NULL OR distr = :distr)              \n\
               AND (:subDistr IS NULL OR sub_distr = :subDistr)    \n\
               AND (:craft IS NULL OR craft = :craft)              \n\
        ORDER BY distr, sub_distr, board_name, craft

BoardSummaryReportDetail.findBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft=\n\
		SELECT start_type,                                                                                              \n\
               CAST(CAST(nbr_of_starts AS DECIMAL(10, 2))/ CAST((SELECT                                                 \n\
                (CASE  WHEN   s.stats_end_date = s.stats_start_date THEN 1.0                                            \n\
                            ELSE ( Nvl2(s.stats_end_date, DAYS(s.stats_end_date), 0) -                                  \n\
                                   Nvl2(s.stats_start_date, DAYS(s.stats_start_date), 0) ) / 7.0 END)                   \n\
                                          FROM   scenario_cfg s                                                         \n\
                                          WHERE  s.scenario_id = :scenarioId ) AS DECIMAL(12, 4)) AS                    \n\
                    DECIMAL(10, 2)) AS nbr_of_starts                                                                    \n\
        FROM   board_train_starts                                                                                       \n\
        WHERE  scenario_id = :scenarioId                                                                                \n\
               AND ( :boardName IS NULL                                                                                 \n\
                      OR board_name = :boardName )                                                                      \n\
               AND ( :distr IS NULL                                                                                     \n\
                      OR distr = :distr )                                                                               \n\
               AND ( :subDistr IS NULL                                                                                  \n\
                      OR sub_distr = :subDistr )                                                                        \n\
               AND ( :craft IS NULL                                                                                     \n\
                      OR craft = :craft )                                                                               \n\
        ORDER BY distr, sub_distr, board_name, craft

CardedJobSummary.findByPlanIdAndDistrAndSubDistrAndPoolName=\n\
        SELECT carded_pool_id,                              \n\
               distr,                                       \n\
               sub_distr,                                   \n\
               pool_name,                                   \n\
               craft,                                       \n\
               pool_home_away,                              \n\
               carded_train,                                \n\
               use_schedule_for_train,                      \n\
               cycle_days,                                  \n\
               start_time,                                  \n\
               end_time,                                    \n\
               alt_distr,                                   \n\
               alt_sub_distr,                               \n\
               alt_pool_name,                               \n\
               alt_pool_home_away                           \n\
        FROM   carded_pool                                  \n\
        WHERE  plan_id = :planId                            \n\
               AND ( :distr IS NULL                         \n\
                      OR ( distr = :distr ) )               \n\
               AND ( :subDistr IS NULL                      \n\
                      OR ( sub_distr = :subDistr ) )        \n\
               AND ( :poolName IS NULL                      \n\
                      OR ( pool_name = :poolName ) )        \n\
        ORDER  BY distr,                                    \n\
                  sub_distr,                                \n\
                  pool_name,                                \n\
                  craft,                                    \n\
                  pool_home_away,                           \n\
                  carded_train

TerminalDetail.findByPlanId=\n\
    select distinct crew_orgn_os as os from crew_profile    \n\
    where plan_id=:planId                                   \n\
    union                                                   \n\
    select distinct b.os as os from pool a, pool_set_up b   \n\
    where a.plan_id=:planId and a.pool_id=b.pool_id

CardedJobDetail.findByCardedPoolId=\n\
        SELECT carded_day_id,                  \n\
               day_of_cycle,                   \n\
               home_distr,                     \n\
               home_sub_distr,                 \n\
               home_pool_name,                 \n\
               turn_name,                      \n\
               loc_distr,                      \n\
               loc_pool_name,                  \n\
               loc_home_away                   \n\
        FROM   carded_day                      \n\
        WHERE  carded_pool_id = :cardedPoolId  \n\
        ORDER  BY day_of_cycle

CrewProfileReport.findByCrewProfilesByPlanIdAndDistrAndSubDistrAndPoolNameAndRowNumBetween= \n\
SELECT                                  \n\
    distr,                              \n\
    sub_distr,                          \n\
    pool_name,                          \n\
    crew_orgn_os,                       \n\
    profile_id,                         \n\
    crew_destin_os,                     \n\
    craft,                              \n\
    miles,                              \n\
    tieup_exception1,                   \n\
    tieup_exception2,                   \n\
    tieup_exception3,                   \n\
    tieup_exception4,                   \n\
    crew_profile_id,                    \n\
    plan_id,                            \n\
    pool_profile_desc,                  \n\
    pool_home_away                      \n\
  FROM CREW_PROFILE                     \n\
  WHERE plan_id = :planId               \n\
    AND (:distr IS NULL                 \n\
      OR distr = :distr)                \n\
    AND (:subDistr IS NULL              \n\
      OR sub_distr = :subDistr)         \n\
    AND (:poolName IS NULL              \n\
      OR pool_name = :poolName)         \n\
 ORDER BY :ORDER_BY_CLAUSE              \n\
LIMIT  :noOfRows OFFSET :startRowNum


ExpCardedJob.findByPlanIdAndDistrAndSubDistrAndPoolName=\n\
        SELECT cp.distr,                                                 \n\
               cp.sub_distr,                                             \n\
               cp.pool_name,                                             \n\
               cp.craft,                                                 \n\
               cp.carded_train,                                          \n\
               cp.USE_SCHEDULE_FOR_TRAIN,                                \n\
               cp.CYCLE_DAYS,                                            \n\
               cp.START_TIME,                                            \n\
               cp.END_TIME,                                              \n\
               cp.ALT_DISTR,                                             \n\
               cp.ALT_SUB_DISTR,                                         \n\
               cp.ALT_POOL_NAME,                                         \n\
               cp.ALT_POOL_HOME_AWAY,                                    \n\
               cd.day_of_cycle,                                          \n\
               cd.home_distr,                                            \n\
               cd.home_sub_distr,                                        \n\
               cd.home_pool_name,                                        \n\
               cd.turn_name,                                             \n\
               cd.loc_distr,                                             \n\
               cd.loc_pool_name,                                         \n\
               cd.loc_home_away,                                         \n\
               cp.start_time,                                            \n\
               cp.end_time                                               \n\
        FROM   carded_pool cp                                            \n\
                   LEFT JOIN carded_day cd                               \n\
                             ON cp.carded_pool_id = cd.carded_pool_id    \n\
        WHERE  cp.plan_id = :planId                                      \n\
          AND ( :distr IS NULL                                           \n\
            OR ( cp.distr = :distr ) )                                   \n\
          AND ( :subDistr IS NULL                                        \n\
            OR ( cp.sub_distr = :subDistr ) )                            \n\
          AND ( :poolName IS NULL                                        \n\
            OR ( cp.pool_name = :poolName ) )                            \n\
        ORDER  BY cp.distr,                                              \n\
                  cp.sub_distr,                                          \n\
                  cp.pool_name,                                          \n\
                  cp.craft,                                              \n\
                  cp.cycle_days,                                         \n\
                  cp.carded_train,                                       \n\
                  cd.day_of_cycle

CardedJobFilter.findByPlanId=\n\
        SELECT DISTINCT distr,         \n\
                        sub_distr,     \n\
                        pool_name      \n\
        FROM   carded_pool             \n\
        WHERE  plan_id = :planId       \n\
        ORDER  BY distr,               \n\
                  sub_distr,           \n\
                  pool_name

CrewProfileFilter.findByPlanId=\n\
        SELECT DISTINCT distr,     \n\
                        sub_distr, \n\
                        pool_name  \n\
        FROM   crew_profile        \n\
        WHERE  plan_id = :planId   \n\
        ORDER  BY distr,           \n\
                  sub_distr,       \n\
                  pool_name

ExtraboardFilter.findByPlanId=\n\
        SELECT DISTINCT distr,    \n\
                        sub_distr \n\
        FROM   exb_pool_set_up    \n\
        WHERE  plan_id = :planId  \n\
        ORDER  BY distr,          \n\
                  sub_distr

IdPoolFilter.findByPlanId=\n\
        SELECT DISTINCT distr,    \n\
                        sub_distr \n\
        FROM   id_pool            \n\
        WHERE  plan_id = :planId  \n\
        ORDER  BY distr,          \n\
                  sub_distr

ExpExbTurn.findTurnsByPlanIdAndDistrAndSubDistr=\n\
        SELECT t.distr,                                                            \n\
                       t.sub_distr,                                                \n\
                       es.description,                                             \n\
                       t.pool_name,                                                \n\
                       es.craft,                                                   \n\
                       es.home_os,                                                 \n\
                       es.exb_type,                                                \n\
                       t.turn_id                                                   \n\
                               FROM   exb_turn_view t                                   \n\
                               LEFT JOIN exb_pool_set_up es                        \n\
                               ON t.exb_pool_set_up_id = es.exb_pool_set_up_id     \n\
                               AND t.distr = es.distr                              \n\
                               AND t.sub_distr = es.sub_distr                      \n\
                               AND t.pool_name = es.pool_name                      \n\
                               WHERE  t.plan_id = :planId                          \n\
                               AND (:distr IS NULL OR t.distr = :distr)            \n\
                               AND (:subDistr IS NULL OR t.sub_distr = :subDistr)  \n\
        ORDER BY distr, sub_distr, pool_name, craft, exb_type, turn_id

ExpExbTurn.findSupportedPoolsByPlanIdAndDistrAndSubDistr=\n\
        SELECT e.distr,                                                \n\
               e.sub_distr,                                            \n\
               es.description,                                         \n\
               es.craft,                                               \n\
               es.home_os,                                             \n\
               es.exb_type,                                            \n\
               e.pool_name,                                            \n\
               es.pool_name as exb_name,                               \n\
               e.pool_home_away,                                       \n\
               e.pool_craft,                                           \n\
               ES.MARK_OFF_RATE,                                       \n\
               ES.SU_MARK_OFF_RATE,                                    \n\
               ES.MO_MARK_OFF_RATE,                                    \n\
               ES.TU_MARK_OFF_RATE,                                    \n\
               ES.WE_MARK_OFF_RATE,                                    \n\
               ES.TH_MARK_OFF_RATE,                                    \n\
               ES.FR_MARK_OFF_RATE,                                    \n\
               ES.SA_MARK_OFF_RATE,                                    \n\
               ES.OPTIMIZE_SIZE                                        \n\
        FROM   extraboard_view e                                            \n\
               LEFT JOIN exb_pool_set_up es                            \n\
                      ON e.exb_pool_set_up_id = es.exb_pool_set_up_id  \n\
                         AND e.distr = es.distr                        \n\
                         AND e.sub_distr = es.sub_distr                \n\
                         AND e.exb_board_name = es.pool_name           \n\
        WHERE  e.plan_id = :planId                                     \n\
               AND ( :distr IS NULL                                    \n\
                      OR e.distr = :distr )                            \n\
               AND ( :subDistr IS NULL                                 \n\
                      OR e.sub_distr = :subDistr )                     \n\
        ORDER BY distr, sub_distr, pool_name, craft

ExpPoolSetUpTerminal.findTurnsByPlanIdAndDistrAndSubDistrAndPoolName=\n\
        SELECT distinct p.distr,                           \n\
               p.sub_distr,                                \n\
               p.pool_name,                                \n\
               t.craft,                                    \n\
               t.turn_id                                   \n\
        FROM   turn t                                      \n\
               LEFT JOIN pool_set_up_view p                     \n\
                      ON t.pool_id = p.pool_id             \n\
        WHERE  p.plan_id = :planId                         \n\
               AND ( :distr IS NULL                        \n\
                      OR p.distr = :distr )                \n\
               AND ( :subDistr IS NULL                     \n\
                      OR p.sub_distr = :subDistr )         \n\
               AND ( :poolName IS NULL                     \n\
                      OR p.pool_name = :poolName )         \n\
        ORDER BY p.distr, p.sub_distr, p.pool_name, t.craft

ExpPoolSetUpTerminal.findOthersByPlanIdAndDistrAndSubDistrAndPoolName=\n\
        SELECT distinct p.distr,                   \n\
               p.sub_distr,                        \n\
               p.pool_name,                        \n\
               t.*                                 \n\
        FROM   pool_set_up_view p                       \n\
               LEFT JOIN pool t                    \n\
                      ON p.pool_id = t.pool_id     \n\
        WHERE  p.plan_id = :planId                 \n\
               AND ( :distr IS NULL                \n\
                      OR p.distr = :distr )        \n\
               AND ( :subDistr IS NULL             \n\
                      OR p.sub_distr = :subDistr ) \n\
               AND ( :poolName IS NULL             \n\
                      OR p.pool_name = :poolName ) \n\
        ORDER BY p.distr, p.sub_distr, p.pool_name

PoolSetupFilter.findByPlanId=\n\
        SELECT DISTINCT distr,     \n\
                        sub_distr, \n\
                        pool_name  \n\
        FROM   pool_set_up_view         \n\
        WHERE  plan_id = :planId   \n\
        ORDER  BY distr,           \n\
                  sub_distr,       \n\
                  pool_name

TueFilter.findByPlanId=\n\
        SELECT DISTINCT distr,     \n\
                        sub_distr  \n\
        FROM   tie_up_exception    \n\
        WHERE  plan_id = :planId   \n\
        ORDER  BY distr,           \n\
                  sub_distr

WrpFilter.findByPlanId=\n\
        SELECT DISTINCT distr,      \n\
                        sub_distr,  \n\
                        pool_name   \n\
        FROM   work_rest_prof       \n\
        WHERE  plan_id = :planId    \n\
        ORDER  BY distr,            \n\
                  sub_distr,        \n\
                  pool_name

ScenarioSelector.findByStatus=\n\
		SELECT scenario.scenario_id,                                   \n\
               scenario.scen_name,                                     \n\
               scenario.plan_id,                                       \n\
               PLAN.plan_name,                                         \n\
               study.study_name,                                       \n\
               scenario.description,                                   \n\
               study.start_date AS study_start_date,                   \n\
               study.end_date   AS study_end_date,                     \n\
               scenario.create_ts,                                     \n\
               scenario.user_id                                        \n\
        FROM   scenario scenario                                       \n\
               LEFT OUTER JOIN study study                             \n\
                            ON scenario.study_id = study.study_id      \n\
               LEFT OUTER JOIN PLAN PLAN                               \n\
                            ON scenario.plan_id = PLAN.plan_id         \n\
        WHERE  scenario.status = :status                               \n\
        ORDER BY scenario_id

BoardSummaryFilter.findByScenarioId=\n\
		SELECT DISTINCT distr,           \n\
                        sub_distr        \n\
        FROM   board_summary             \n\
        WHERE  scenario_id = :scenarioId \n\
        ORDER BY distr, sub_distr

DeadheadFilter.findByScenarioId=\n\
		SELECT DISTINCT distr,                 \n\
                        sub_distr,             \n\
                        board_name             \n\
        FROM   board_train_starts              \n\
        WHERE  start_type = 'DEADHEAD'         \n\
               AND scenario_id = :scenarioId   \n\
        ORDER BY distr, sub_distr, board_name

SimulationTrainFilter.findTrnTypesByScenarioId=\n\
		SELECT DISTINCT trn_type               \n\
        FROM   simulation_train_output         \n\
        WHERE  scenario_id = :scenarioId       \n\
        order by trn_type

SimulationTrainFilter.findByScenarioId=\n\
		SELECT DISTINCT trn_type,                                                   \n\
                        trn_crew_distr    AS line_segment,                          \n\
                        from_os,                                                    \n\
                        Substr(trn, 1, 3) AS trn_symb,                              \n\
                        CASE                                                        \n\
                            WHEN en_profile_distr IS NULL THEN co_profile_distr     \n\
                            ELSE en_profile_distr                                   \n\
                            END               AS distr                              \n\
        FROM   simulation_train_output                                              \n\
        WHERE scenario_id = :scenarioId                                             \n\
        ORDER BY distr, trn_symb, TRN_TYPE, line_segment

TurnUtilizationFilter.findByScenarioId=\n\
		SELECT DISTINCT t.distr,                 \n\
        t.sub_distr,                             \n\
        t.board_name,                            \n\
        t.board_type,                            \n\
        t.craft                                  \n\
        FROM   turn_utilization AS t             \n\
         WHERE  t.scenario_id = :scenarioId      \n\
        ORDER BY distr, sub_distr, board_name

ScenarioDetail.findByStatusIn=\n\
		SELECT SC.scenario_id,                             \n\
               SC.scen_name,                               \n\
               S.study_name,                               \n\
               SC.status,                                  \n\
               SC.create_ts,                               \n\
               SC.user_id                                  \n\
        FROM   scenario SC                                 \n\
               LEFT OUTER JOIN study S                     \n\
                            ON SC.study_id = S.study_id    \n\
        WHERE  SC.status IN( :status)                      \n\
        ORDER  BY SC.scen_name

Deadhead.getDeadheadTotalStartsReport=\n\
		select                                                                \n\
        distr,                                                                \n\
        sub_distr,                                                            \n\
        board_name,                                                           \n\
        craft,                                                                \n\
        nbr_of_starts as Count from BOARD_TRAIN_STARTS                        \n\
         where start_type = 'DEADHEAD'                                        \n\
         and scenario_id = :scenarioId                                        \n\
         and ( (:craft is null AND craft in ('CO','EN') )OR craft = :craft)   \n\
         and (:district is null OR distr = :district)                         \n\
         and (:subDistr is null OR sub_distr = :subDistr)                     \n\
         and (:boardName  is null OR board_name = :boardName)                 \n\
         order by distr, sub_distr, board_name, craft

Deadhead.getDeadheadAvgStartsPerWeekReport=\n\
	select                                                                                                                 \n\
        distr,                                                                                                             \n\
        sub_distr,                                                                                                         \n\
        board_name,                                                                                                        \n\
        craft,                                                                                                             \n\
        CAST(CAST(nbr_of_starts as decimal(10,2)) / CAST((SELECT CASE WHEN s.stats_end_date=s.stats_start_date then 1.0    \n\
        else ( nvl2(s.stats_end_date,DAYS(s.stats_end_date),0)-nvl2(s.stats_start_date,DAYS(s.stats_start_date),0)) / 7.0  \n\
        end                                                                                                                \n\
    FROM   scenario_cfg s                                                                                                  \n\
    WHERE  s.scenario_id = :scenarioId ) AS DECIMAL(12, 4)) AS                                                             \n\
    DECIMAL(10, 2)) AS Count                                                                                               \n\
        from BOARD_TRAIN_STARTS                                                                                            \n\
         where start_type = 'DEADHEAD'                                                                                     \n\
         and scenario_id = :scenarioId                                                                                     \n\
         and ( (:craft is null AND craft in ('CO','EN') )OR craft = :craft)                                                \n\
         and (:district is null OR distr = :district)                                                                      \n\
         and (:subDistr is null OR sub_distr = :subDistr)                                                                  \n\
         and (:boardName  is null OR board_name = :boardName)                                                              \n\
         order by distr, sub_distr, board_name, craft

TurnUtilization.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft=\n\
		select                                                                                          \n\
            board_type,                                                                                 \n\
            craft,                                                                                      \n\
            distr,                                                                                      \n\
            sub_distr,                                                                                  \n\
            board_name,                                                                                 \n\
            turn_id,                                                                                    \n\
            status,                                                                                     \n\
            duration,                                                                                   \n\
            onduty_flag                                                                                 \n\
         from TURN_UTILIZATION                                                                          \n\
         where scenario_id = :scenarioId                                                                \n\
         and ((:boardType is null AND board_type in ('Pool', 'Extra')) OR board_type = :boardType)      \n\
         and ((:craft is null AND craft in ('CO','EN')) OR craft = :craft)                              \n\
         and (:district is null OR distr = :district)                                                   \n\
         and (:subDistr is null OR sub_distr = :subDistr)                                               \n\
         and (:boardName is null OR board_name = :boardName)                                            \n\
        order by distr, sub_distr, board_name, board_type, craft

TurnUtilization.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft=\n\
		select                                                                                          \n\
            board_type,                                                                                 \n\
            craft,                                                                                      \n\
            distr,                                                                                      \n\
            sub_distr,                                                                                  \n\
            board_name,                                                                                 \n\
            turn_id,                                                                                    \n\
            status,                                                                                     \n\
         CAST(CAST(DURATION AS DECIMAL(10,2)) / CAST((SELECT CASE                                       \n\
            WHEN s.stats_end_date - s.stats_start_date = 0 then                                         \n\
            1.0                                                                                         \n\
            else ( nvl2(s.stats_end_date,DAYS(s.stats_end_date),0)                                      \n\
                    -nvl2(s.stats_start_date,DAYS(s.stats_start_date),0)) / 7.0 end                     \n\
            FROM scenario_cfg s                                                                         \n\
            WHERE s.scenario_id = :scenarioId) AS DECIMAL(12, 4)) AS                                    \n\
            DECIMAL(10, 2)) AS duration,                                                                \n\
            onduty_flag                                                                                 \n\
         from TURN_UTILIZATION                                                                          \n\
         where scenario_id = :scenarioId                                                                \n\
         and ((:boardType is null AND board_type in ('Pool', 'Extra')) OR board_type = :boardType)      \n\
         and ((:craft is null AND craft in ('CO','EN')) OR craft = :craft)                              \n\
         and (:district is null OR distr = :district)                                                   \n\
         and (:subDistr is null OR sub_distr = :subDistr)                                               \n\
         and (:boardName is null OR board_name = :boardName)                                            \n\
        order by distr, sub_distr, board_name, board_type, craft
CrewProfileSimpleFilter.findAll=\n\
        SELECT DISTINCT CREW_ORGN_OS,     \n\
               PROFILE_ID                 \n\
        FROM   CREW_PROFILE               \n\
        ORDER  BY CREW_ORGN_OS,           \n\
                  PROFILE_ID 
CrewProfilePlan.findPlansByCrewOrgnOsAndProfileId=\n\
        SELECT DISTINCT P.PLAN_ID,                \n\
              p.PLAN_NAME,                        \n\
              p.PLAN_DESCRIPTION                  \n\
        FROM CREW_PROFILE cp,                     \n\
             PLAN p                               \n\
        WHERE cp.PLAN_ID = p.PLAN_ID              \n\
        AND cp.CREW_ORGN_OS = :crewOrgnOs         \n\
        AND cp.PROFILE_ID = :profileId            \n\
        ORDER BY p.PLAN_NAME, p.PLAN_DESCRIPTION

TransitTimeDAOImpl.getHistoricalTransitTimes1=\n\
select trn_typ as trn_type,trn_crew_distr,from_os,to_os,                                         \n\
td_day_of_week(from_ts) as dow,count(1) trn_cnt,                                                 \n\
avg(case when CREW_SEG_TRANSIT_DAYS<0.001 then sch_crew_seg_transit_days else                    \n\
  CREW_SEG_TRANSIT_DAYS end *1440) as transit_minutes                                            \n\
from ACT_TRN_CREW_SEG c                                                                          \n\
left outer join SCH_TRN_SYMBOL_EXTRA s on c.trn=s.trn and c.trn_orgn_dt=s.trn_orgn_Dt and        \n\
c.trn_section=s.trn_section                                                                      \n\
where c.trn_orgn_dt > current_date - interval '30' day                                           \n\
and CREW_SEG_TRANSIT_DAYS is not null                                                            \n\
and c.trn_section='0'                                                                            \n\
and c.annul_ct=0 and c.combo_from_ct=0 and c.divert_ct=0                                         \n\
group by trn_typ,trn_crew_distr,from_os,to_os, td_day_of_week(from_ts)

TransitTimeDAOImpl.getHistoricalTransitTimes2=\n\
select trn_typ as trn_type,trn_crew_distr,from_os,to_os,                                         \n\
count(1) trn_cnt,                                                                                \n\
avg(case when CREW_SEG_TRANSIT_DAYS<0.001 then sch_crew_seg_transit_days else                    \n\
  CREW_SEG_TRANSIT_DAYS end *1440) as transit_minutes                                            \n\
from ACT_TRN_CREW_SEG c                                                                          \n\
left outer join SCH_TRN_SYMBOL_EXTRA s on c.trn=s.trn and c.trn_orgn_dt=s.trn_orgn_Dt and        \n\
c.trn_section=s.trn_section                                                                      \n\
where c.trn_orgn_dt > current_date - interval '30' day                                           \n\
and CREW_SEG_TRANSIT_DAYS is not null                                                            \n\
and c.trn_section='0'                                                                            \n\
and c.annul_ct=0 and c.combo_from_ct=0 and c.divert_ct=0                                         \n\
group by trn_typ,trn_crew_distr,from_os,to_os

TransitTimeDAOImpl.getHistoricalTransitTimes3=\n\
select trn_crew_distr,from_os,to_os,                                                             \n\
count(1) trn_cnt,                                                                                \n\
avg(case when CREW_SEG_TRANSIT_DAYS<0.001 then sch_crew_seg_transit_days else                    \n\
  CREW_SEG_TRANSIT_DAYS end *1440) as transit_minutes                                            \n\
from ACT_TRN_CREW_SEG c                                                                          \n\
left outer join SCH_TRN_SYMBOL_EXTRA s on c.trn=s.trn and c.trn_orgn_dt=s.trn_orgn_Dt and        \n\
c.trn_section=s.trn_section                                                                      \n\
where c.trn_orgn_dt > current_date - interval '30' day                                           \n\
and CREW_SEG_TRANSIT_DAYS is not null                                                            \n\
and c.trn_section='0'                                                                            \n\
and c.annul_ct=0 and c.combo_from_ct=0 and c.divert_ct=0                                         \n\
group by trn_crew_distr,from_os,to_os

TransitTimeDAOImpl.getHistoricalTransitTimes4=\n\
select from_os,to_os,                                         \n\
td_day_of_week(from_ts) as dow,count(1) trn_cnt,                                                 \n\
avg(case when CREW_SEG_TRANSIT_DAYS<0.001 then sch_crew_seg_transit_days else                    \n\
  CREW_SEG_TRANSIT_DAYS end *1440) as transit_minutes                                            \n\
from ACT_TRN_CREW_SEG c                                                                          \n\
left outer join SCH_TRN_SYMBOL_EXTRA s on c.trn=s.trn and c.trn_orgn_dt=s.trn_orgn_Dt and        \n\
c.trn_section=s.trn_section                                                                      \n\
where c.trn_orgn_dt > current_date - interval '30' day                                           \n\
and CREW_SEG_TRANSIT_DAYS is not null                                                            \n\
and c.trn_section='0'                                                                            \n\
and c.annul_ct=0 and c.combo_from_ct=0 and c.divert_ct=0                                         \n\
group by from_os,to_os, td_day_of_week(from_ts)

TransitTimeDAOImpl.getHistoricalTransitTimes5=\n\
select from_os,to_os,                                                             \n\
count(1) trn_cnt,                                                                                \n\
avg(case when CREW_SEG_TRANSIT_DAYS<0.001 then sch_crew_seg_transit_days else                    \n\
  CREW_SEG_TRANSIT_DAYS end *1440) as transit_minutes                                            \n\
from ACT_TRN_CREW_SEG c                                                                          \n\
left outer join SCH_TRN_SYMBOL_EXTRA s on c.trn=s.trn and c.trn_orgn_dt=s.trn_orgn_Dt and        \n\
c.trn_section=s.trn_section                                                                      \n\
where c.trn_orgn_dt > current_date - interval '30' day                                           \n\
and CREW_SEG_TRANSIT_DAYS is not null                                                            \n\
and c.trn_section='0'                                                                            \n\
and c.annul_ct=0 and c.combo_from_ct=0 and c.divert_ct=0                                         \n\
group by from_os,to_os

TsrStationControlDAOImpl.getTsrCrewPair=select crew_stn as crewStation, tsr_stn as tsrStation from dcccall.tsr_crew_pair

CostCtrDAOImpl.getCostCenterByTrnSql=                                                    \n\
with a as (                                                                              \n\
    select                                                                               \n\
        case when tsr_trn is null then substr(tsr_train_id,1,4) else tsr_trn             \n\
        end as tsr_trn,                                                                  \n\
        tsr_trn_crew_distr,                                                              \n\
        road_profile_orgn_os,                                                            \n\
        cost_ctr_rin_nbr,                                                                \n\
        count(1) as row_cnt                                                              \n\
    from                                                                                 \n\
        Crew_PVIEW.ROAD_ASGN_H h                                                         \n\
    where  asgn_dt >= current_date - interval '330' day                                  \n\
        and cost_ctr_rin_nbr is not null and cost_ctr_rin_nbr<>''                        \n\
    group by                                                                             \n\
        case when tsr_trn is null then substr(tsr_train_id,1,4)                          \n\
            else tsr_trn end,tsr_trn_crew_distr,road_profile_orgn_os, cost_ctr_rin_nbr   \n\
)                                                                                        \n\
select tsr_trn as trn_symb,                                                              \n\
    tsr_trn_crew_distr as trn_crew_distr,                                                \n\
    road_profile_orgn_os,                                                                \n\
    cost_ctr_rin_nbr as cost_ctr,                                                                    \n\
    cost_ctr_desc                                                                        \n\
from a left join HRPAY_PVIEW.COST_CTR c on a.cost_ctr_rin_nbr=c.cost_ctr_cd              \n\
qualify row_cnt= max(row_cnt) over                                                       \n\
(partition by tsr_trn,tsr_trn_crew_distr,road_profile_orgn_os)

CrewProfileMatcherDataMaintainerImpl.cacheRefreshHours=24

CrewStartsByHireGrp.findByScenarioId=select * from ccp.crew_starts_by_hire_group_view where scenario_id=:scenarioId

TargetHeadCountByHireGrp.findByScenarioId=                                                           \n\
select                                                                                               \n\
    target_headcount_id ,                                                                            \n\
    scenario_id,                                                                                     \n\
    hire_grp_desc ,                                                                                  \n\
    road_simulation_output_people_starts,                                                            \n\
    road_simulation_output_weekly_people_starts ,                                                    \n\
    road_simulation_output_headcount,                                                                \n\
    road_simulation_output_productivity ,                                                            \n\
    yard_local_simulation_output_people_starts,                                                      \n\
    yard_local_simulation_output_weekly_people_starts ,                                              \n\
    yard_local_simulation_output_headcount,                                                          \n\
    simulation_output_headcount ,                                                                    \n\
    simulation_output_headcount_percentile_85 as simulation_output_headcount_percentile85 ,          \n\
    spw_mean ,                                                                                       \n\
    spw_stdev,                                                                                       \n\
    historical_productivity_headcount_percentile_50 historical_productivity_headcount_percentile50 , \n\
    historical_productivity_headcount_percentile_85 historical_productivity_headcount_percentile85   \n\
from ccp.target_headcount_by_hire_group                                                              \n\
where scenario_id=:scenarioId

