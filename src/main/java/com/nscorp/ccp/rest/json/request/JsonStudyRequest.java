package com.nscorp.ccp.rest.json.request;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.study.StudyAlgorithm;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonStudyRequest {

    @NotNull(message = "Name  is required")
    @Size(min = 1, max = 128)
    String name;
    @Size(max = 1024)
    @NotNull(message = "Description  is required")
    String desc;
    @NotNull(message = "Start Date  is required")
    LocalDate startDate;
    @NotNull(message = "End Date is required")
    LocalDate endDate;
    LocalDate historyStartDate; // <-- CREWMAX-168: new field
    LocalDate historyEndDate; // <-- CREWMAX-168: OBSOLETE
    
    // Note: trnTypes and historicalTrnTypes are disjoint sets of train types.
    String trnTypes; // <-- CREWMAX-168: OBSOLETE
    String opdTrnTypes; // <-- CREWMAX-168: for OPD train types only
    String historicalTrnTypes; // <-- CREWMAX-168: new field
    StudyAlgorithm algorithm; // <-- CREWMAX-168: new field
    String trainSource; // <-- CREWMAX-168: Obsolete.
    Integer opdScenario;
    Integer opdPlan; // <-- CREWMAX-168: new field
    Boolean transitDist;
    Boolean deptDist;
    LocalDate localYardJobStartDate;
    LocalDate localYardJobEndDate; // CREWMAX-168: OBSOLETE
    Long upsideId;

    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
