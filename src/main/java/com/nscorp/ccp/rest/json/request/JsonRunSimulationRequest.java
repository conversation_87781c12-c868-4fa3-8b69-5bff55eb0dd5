package com.nscorp.ccp.rest.json.request;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonRunSimulationRequest {

    @NotEmpty(message = "Scenario Name is required")
     @Size(max = 128)
    String scenarioName;
    @Size(max = 1024)
    String scenarioDesc;

    @NotNull(message = "Study is required")
    Long study;
    @NotNull(message = "Plan is required")
    Long plan;
    @NotNull
    List<String> lineSegments;
    JsonScenarioConfigRequest scenarioConfig;
    JsonInternalSettings internalSettings;
    @NotNull(message = "Stats Start Date is required")
    LocalDate statsStartDate;
    @NotNull(message = "Stats End Date is required")
    LocalDate statsEndDate;
}
