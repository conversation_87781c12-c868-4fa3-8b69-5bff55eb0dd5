package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.Data;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonDepartureDistributionParams {
	String departureDistribution;
	Double deptLambda1;
	Double deptShape;
	Double deptLower;
	Double deptShiftConstant;
	Double deptMean1;
	Double deptMedium;
	Double deptRate;
	Double deptScale;
	Double deptVariance1;
	Double deptUpper;
}
