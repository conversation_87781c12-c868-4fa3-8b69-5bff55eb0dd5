package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.commonTypes.*;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

/**
 * @see com.nscorp.ccp.common.plan.Turn
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic=true)
@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonTurn {
	String id;
	Craft craft;
	Integer poolQHomeAway;
}
