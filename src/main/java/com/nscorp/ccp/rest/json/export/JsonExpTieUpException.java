package com.nscorp.ccp.rest.json.export;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.commonTypes.*;
import lombok.Builder;
import lombok.Data;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonExpTieUpException {

    String distr;
    String subDistr;
    Craft craft;
    Integer tueSeqNbr;

    String fromDistr;
    String fromSubDistr;
    String fromPoolName;
    String toDistr;
    String toSubDistr;
    String toPoolName;
    Integer toPoolHomeAway;
    Integer tueSubSeqNbr;

    String sp1Distr;
    String sp1SubDistr;
    String sp1PoolName;
    String sp2Distr;
    String sp2SubDistr;
    String sp2PoolName;
    String sp3Distr;
    String sp3SubDistr;
    String sp3PoolName;
    String sp4Distr;
    String sp4SubDistr;
    String sp4PoolName;
}
