package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.commonTypes.*;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonPoolRotationH {
	//~ Long poolRotationHId;
	Craft craft;
	int tieupShort;
	int tieupThru;
}
