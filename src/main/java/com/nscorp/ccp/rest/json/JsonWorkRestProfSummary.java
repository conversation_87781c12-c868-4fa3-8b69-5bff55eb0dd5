package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonWorkRestProfSummary {
    Long id;
    String distr;
    String subDistr;
    String poolName;
    String name;
}
