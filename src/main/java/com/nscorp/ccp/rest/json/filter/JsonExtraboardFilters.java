package com.nscorp.ccp.rest.json.filter;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;

import java.util.Set;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic=true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonExtraboardFilters {
    @With String href;
    Set<EbDistr> distrs;

    @Value
    @Builder(toBuilder = true)
    @Jacksonized
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonPropertyOrder(alphabetic=true)
    public static class EbDistr {
        String distr;
        Set<String> subDistrs;
    }
}

