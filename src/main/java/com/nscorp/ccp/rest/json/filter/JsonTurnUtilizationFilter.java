package com.nscorp.ccp.rest.json.filter;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.commonTypes.*;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonTurnUtilizationFilter {
    String distr;
    String subDistr;
    String boardName;
    String boardType;
    Craft craft;
}
