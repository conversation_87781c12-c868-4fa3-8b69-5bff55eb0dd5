package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.commonTypes.*;
import lombok.Data;
import lombok.Value;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import lombok.*;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic=true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonExtraboard {
    String distr;
    String subDistr;
    String name;
    int poolHomeAway;
    Craft craft;
}
