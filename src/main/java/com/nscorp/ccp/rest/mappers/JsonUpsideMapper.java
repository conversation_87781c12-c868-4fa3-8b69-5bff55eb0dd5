package com.nscorp.ccp.rest.mappers;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.upside.Upside;
import com.nscorp.ccp.common.upside.UpsideTrain;
import com.nscorp.ccp.rest.json.JsonUpside;
import com.nscorp.ccp.rest.json.JsonUpsideTrain;
import com.nscorp.ccp.utils.lang.Pure;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@Pure
public abstract class JsonUpsideMapper {
	public abstract JsonUpside toJson(Upside dto);
	public abstract JsonUpsideTrain toJson(UpsideTrain dto);

	public abstract Upside fromJson(JsonUpside json);
}
