package com.nscorp.ccp.rest.mappers;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.opd.OpdPlan;
import com.nscorp.ccp.common.opd.OpdScenario;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.common.plan.export.*;
import com.nscorp.ccp.common.plan.filter.CrewProfileSimpleFilter;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.json.export.*;
import com.nscorp.ccp.rest.json.filter.JsonCrewProfileSimpleFilter;

public interface JsonPlanMapper {
	JsonExpTieUpException toJson(ConsolidatedTieUpException dto);
	JsonPoolInfo toJson(PoolInfo dto);
	JsonExtraboardInfo toJson(ExtraboardInfo dto);
	JsonOpdPlan toJson(OpdPlan dto);
	JsonOpdScenario toJson(OpdScenario dto);
	JsonTieUpException toJson(TieUpException dto);
	JsonTieUpExceptionSummary toJson(TieUpExceptionSummary dto);
	JsonSearchPool toJson(SearchPool dto);
	JsonTueSubrule toJson(TueSubrule dto);
	JsonCrewProfile toJson(CrewProfile dto);
	JsonCrewProfile toJson(CrewProfileReport dto);
	JsonCrewProfileSimpleFilter toJson(CrewProfileSimpleFilter dto);
	JsonPool toJson(Pool dto);
	JsonPoolSetUp toJson(PoolSetUp dto);
	JsonTurn toJson(Turn dto);
	JsonPlan toJson(Plan dto);
	JsonPlan toJson(CrewProfilePlan dto);
	JsonPoolSummary toJson(PoolSummary dto);
	JsonExtraboardSummary toJson(ExtraboardSummary dto);
	JsonHubDetail toJsonHubDetail(Hub dto);
	JsonExbPoolSetUp toJson(ExbPoolSetUp dto);
	JsonExtraboard toJson(Extraboard dto);
	JsonExbTurn toJson(ExbTurn dto);
	JsonWorkRestProfSummary toJson(WorkRestProfSummary dto);
	JsonWorkRestProf toJson(WorkRestProf dto);
	JsonWorkRestProfRotn toJson(WorkRestProfRotn dto);
	JsonWorkRestProfTGrp toJson(WorkRestProfTGrp dto);
	JsonIdPool toJson(IdPool dto);
	JsonHub toJson(Hub dto);
	JsonCardedJobSummary toJson(CardedJobSummary dto);
	JsonCardedJobDetail toJson(CardedJobDetail dto);
	JsonExpCardedJob toJson(ExpCardedJob dto);
	JsonExpExbTurn toJson(ExpExbTurn dto);
	JsonExpExbSupportedPool toJson(ExpExbSupportedPool dto);
	JsonExpPoolSetUpTerminal toJson(ExpPoolSetUpTerminal dto);
	JsonExpPoolSetUpTurn toJson(ExpPoolSetUpTurn dto);
	JsonExpPoolSetUpOther toJson(ExpPoolSetUpOther dto);
	JsonExpWokRestProfTurn toJson(ExpWorkRestProfTurn dto);
	JsonExpWokRestProfRotation toJson(ExpWorkRestProfRotation dto);
	Pool fromJson(JsonPool json);
	CrewProfile fromJson(JsonCrewProfile json);
	Turn fromJson(JsonTurn json);
	PoolSetUp fromJson(JsonPoolSetUp json);
	PoolRotationH fromJson(JsonPoolRotationH json);

	Extraboard fromJson(JsonExtraboard json);
	ExbTurn fromJson(JsonExbTurn json);
	ExbPoolSetUp fromJson(JsonExbPoolSetUp jsonExbPoolSetUp);
	Hub fromJson(JsonHub json);
}

