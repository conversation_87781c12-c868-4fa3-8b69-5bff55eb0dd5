package com.nscorp.ccp.rest;

import com.google.common.base.Preconditions;
import com.nscorp.ccp.biz.study.*;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.common.study.filter.TrainViewerFilter;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.json.request.JsonStudyRequest;
import com.nscorp.ccp.rest.json.request.JsonTrainViewerRequest;
import com.nscorp.ccp.rest.mappers.JsonStudyMapper;
import com.nscorp.ccp.rest.mappers.filter.JsonStudyFiltersMapper;
import com.nscorp.ccp.utils.rest.RestMethodRunner;
import com.nscorp.ccp.utils.rest.SortUtil;
import com.nscorp.ccp.utils.testing.BackgroundInvoker;
import io.vavr.Tuple;
import io.vavr.control.Try;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Stream;

import static com.google.common.collect.Iterables.transform;
import static com.nscorp.ccp.rest.BoardTypeParser.parseBoardType;
import static com.nscorp.ccp.rest.FilterUtils.allToNull;
import static com.nscorp.ccp.rest.config.Constants.DEFAULT_PAGE_SIZE;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static com.nscorp.ccp.utils.logging.LogUtils.warn;
import static com.nscorp.ccp.utils.rest.RestUtil.*;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

/**
 * Provides rest API's for study management and study reports.
 */
@Slf4j
@AllArgsConstructor
@RestController
@Builder
public class StudyRestService {
    private final StudyManager manager;
    private final TrainProfileUpdater trainProfileUpdater;
    private final StudyReportManager reportManager;
    private final StudyDeleter studyDeleter;
    private final StudyCopier studyCopier;
    private final JsonStudyFiltersMapper jsonStudyFiltersMapper;
    private final BackgroundInvoker backgroundInvoker;
    private final StudyCreator studyCreator;
    private final JsonStudyMapper jsonStudyMapper;
    private final RestMethodRunner runner;
    private final StudyUpdater studyUpdater;
    private final TrainDeleter trainDeleter;
    private final TrainViewerReportFetcher trainViewerReportFetcher;
    private final TrainViewerReportExporter trainViewerReportExporter = new TrainViewerReportExporter();

    /**
     * Fetch all studies.
     *
     * @param request
     * @return Studies.
     * @see JsonStudyTree
     */
    @GetMapping(path = "/studies/")
    // unit test converted
    public ResponseEntity<JsonCollectionResponse> getAllStudies(HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getAllStudies");
            // Get all the non-deleted studies.
            val studies = reportManager.getStudiesTree();
            val result = jsonStudyFiltersMapper.mapStudiesTree(studies);
            return listResponse(request, result);
        });
    }

    private void validateTrnTypes(String trnTypes) {
        if ( trnTypes != null ) {
            // Split up the comma-delimeted list and interpret each part as a train type.
            //      Throw a IllegalArgumentException if any of the train types is non-alphanumeric.
            Stream.of(trnTypes.split(",")).
                    filter(s->! StringUtils.isAlphanumeric(s)).
                    findFirst().
                    ifPresent(s-> { throw new IllegalArgumentException("Illegal train types."); } );
        }
    }

    /**
     * Create a study from study request. Study will be created asynchronously in separate thread.
     *
     * @param request Study request.
     * @param httpServletRequest
     * @return HttpStatus and empty body.
     */
    @PostMapping(path = "/studies/")
    // unit test converted
    public ResponseEntity<JsonStudy> addStudy(
            @RequestBody @Validated JsonStudyRequest request,
            @RequestParam(required = false, value = "updater") String updater ) {
        return runner.run(() -> {
            val userId = defaultIfNull(updater, "sys");
            val algorithm = request.getAlgorithm();

            Preconditions.checkNotNull(algorithm);

            val opdTrnTypes = isEmpty(request.getOpdTrnTypes()) ? request.getTrnTypes() : request.getOpdTrnTypes();
            validateTrnTypes(opdTrnTypes);
            validateTrnTypes(request.getHistoricalTrnTypes());

            val study = buildNewStudy(request, userId, algorithm, opdTrnTypes);
            @NonNull val newStudy = studyCreator.createStudy(study);
            @NonNull val jsonStudy = jsonStudyMapper.toJson(newStudy);
            return new ResponseEntity<>(jsonStudy, HttpStatus.OK);
        });
    }

    private static Study buildNewStudy(final JsonStudyRequest request, final String userId, final StudyAlgorithm algorithm, final String opdTrnTypes) {
        val study = Study.builder().
                startDate(request.getStartDate()).
                endDate(request.getEndDate()).
                historyStartDate(request.getHistoryStartDate()).
                // historyEndDate(request.getHistoryEndDate()).
                userId(userId).
                studyType(StudyType.CCP).
                studyName(request.getName()).
                description(request.getDesc()).
                algorithm(algorithm).
                opdScenario(request.getOpdScenario()).
                opdPlan(request.getOpdPlan()).
                transitDist(request.getTransitDist()).
                deptDist(request.getDeptDist()).
                opdTrnTypes(opdTrnTypes).
                historicalTrnTypes(request.getHistoricalTrnTypes()).
                localYardJobStartDate(request.getLocalYardJobStartDate()).
                // localYardJobEndDate(request.getLocalYardJobEndDate()).
                upsideId(request.getUpsideId()).
                build();
        return study;
    }

    /**
     * Update study's name and description for a study id. If study isn't present return bad request.
     *
     * @param id Study id
     * @param name Study name
     * @param desc Study description
     * @param request
     * @return HttpStatus and empty body.
     */
    @PostMapping(path = "/studies/id/{id}")
    // unit test converted
    public ResponseEntity<Object> updateStudy(
            @PathVariable long id,
            @RequestParam(value = "name",defaultValue = "") String name,
            @RequestParam(value = "desc", defaultValue = "", required = false) String desc,
            @RequestParam(required = false, value = "updater") String updater ) {
        return runner.run(() -> {
            info(log, "updateStudy : id - {}, name - {}, desc - {}  ", id, name, desc);
            val userId = defaultIfNull(updater, "sys");
            return Try.of(()->studyUpdater.update(id, name, desc, userId)).
                    map(study->ResponseEntity.ok().build()).
                    getOrElseGet(e -> new ResponseEntity<>(JsonStatus.ofFailure(e.toString()), HttpStatus.BAD_REQUEST));
        });
    }

    /**
     * Create new study from existing study with provided name and description. If study isn't exists return bad request.
     * Study will be created in separate thread.
     *
     * @param id Study id
     * @param name Study name
     * @param desc Study description
     * @param request
     * @return Newly created study.
     */
    @PostMapping(path = "/studies/copy/id/{id}")
    public ResponseEntity<Object> copyStudy(
            @PathVariable long id,
            @RequestParam(value = "name") String name,
            @RequestParam(value = "desc") String desc,
            @RequestParam(required = false, value = "updater") String updater ) {
        return runner.run(() -> {
            info(log, "copyStudy : id - {}, name - {}, desc - {}", id, name, desc);
            val userId = defaultIfNull(updater, "sys");
            return Try.of(()-> doCopyStudy(id, name, desc, userId)).
                    map(jsonStudyMapper::toJson).
                    map(json->new ResponseEntity<Object>(json, HttpStatus.OK)).
                    getOrElseGet(e-> new ResponseEntity<>(JsonStatus.ofFailure(e.toString()), HttpStatus.BAD_REQUEST));
        });
    }

    @NonNull private Study doCopyStudy(final long id, final String name, final String desc, final String userId) {
        val copy = studyCopier.beginCopy(id, userId, name.trim(), desc.trim());
        val newStudyId = copy.getStudyId();
        backgroundInvoker.run(() -> studyCopier.finishCopy(newStudyId));
        return copy;
    }

    /**
     * Soft delete a study with provided id. If deletion fail return bad request.
     *
     * @param id Study id
     * @return Study delete status
     */
    @DeleteMapping(path = "/studies/id/{id}")
    public ResponseEntity<JsonStatus> deleteStudy( @PathVariable long id ) {
        return runner.run(() -> {
            info(log, "deleteStudy : id - {}", id);
            return Try.of(()->studyDeleter.markStudyDeleted(id)).
                    map(e->new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK)).
                    getOrElseGet(e->new ResponseEntity<>(JsonStatus.ofFailure(e.toString()), HttpStatus.BAD_REQUEST));
        });
    }

    /**
     * Fetch diagnostic report for a study and report type combination.
     *
     * @param studyId    Study id
     * @param reportType Report type for transit time duration ({@value com.nscorp.ccp.rest.config.Constants#STUDY_LONG_TRANSIT_TIME}
     *                   / {@value com.nscorp.ccp.rest.config.Constants#STUDY_MEDIUM_TRANSIT_TIME}
     *                   / {@value com.nscorp.ccp.rest.config.Constants#STUDY_MISSING_CREW_PROFILE})
     * @param request
     * @return Diagnostic report.
     * @see JsonDiagnostic
     */
    @GetMapping(path = "/diagnosticsReport/studyId/{studyId}")
    public ResponseEntity<JsonCollectionResponse> getDiagnosticsReport(
            @PathVariable Long studyId,
            @RequestParam(value = "reportType") String reportType,
            HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getDiagnosticsReport studyId - {}, reportType - {}", studyId, reportType);
            val rt = new DiagnosticReportTypeParser().parseReportType(reportType);
            val diagnostics = reportManager.getDiagnosticReport(studyId, rt);
            val result = transform(diagnostics, jsonStudyMapper::toJson);
            return listResponse(request, result);
        });
    }

    //Train Viewer

    /**
     * Fetch train viewer report for a study with pagination.
     * The results further filtered with train type, crew origin os, train, train crew district and board type combination.
     * And results can be multi sorted.
     *
     * @param studyId Study id
     * @param page Page number of the result. If not provided all crew profiles will be fetched for matching criteria.
     * @param trnCrewDistr Train crew district
     * @param trnType Train type
     * @param trnSymb Train Symbol
     * @param crewOrgnOs Crew origin os
     * @param boardType Board type
     * @param sortModels A collection of strings containing sort fields in <field>:<sort type> format.
     *                   Possible values for field are: trnSymb, fromOs, toOs, trnType, numTrains.
     *                   Possible values for type are: asc, desc.
     * @param request
     * @return Train viewer details
     * @see JsonTrainSummary
     */
    @GetMapping(path = "/trainViewer/studyId/{studyId}")
    public ResponseEntity<JsonCollectionResponse> getTrainViewerSummaries(
            @PathVariable long studyId,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "trnCrewDistr", required = false, defaultValue = "ALL") String trnCrewDistr,
            @RequestParam(value = "trnType", required = false, defaultValue = "ALL") String trnType,
            @RequestParam(value = "trnSymb", required = false, defaultValue = "ALL") String trnSymb,
            @RequestParam(value = "crewOrgnOs", required = false, defaultValue = "ALL") String crewOrgnOs,
            @RequestParam(value = "boardType", required = false, defaultValue = "ALL") String boardType,
            @RequestParam(name = "sortModels", required = false) List<String> sortModels
            , HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getTrainViewerSummaries studyId - {}, page - {}, trnCrewDistr - {}, trnType - {}, trnSymb - {},crewOrgnOs - {}, boardType - {}, sortModels - {}", studyId, page, trnCrewDistr, trnType, trnSymb, crewOrgnOs, boardType, sortModels);
            //board type  Pool :1, extra :0, other : -1, ALL: null.
            val sortModelObjs = SortUtil.getSortModels(sortModels);
            val boardTypeObj = parseBoardType(boardType);
            val trainSummaries = reportManager.getTrainSummaries(
                    studyId,
                    allToNull(trnCrewDistr),
                    allToNull(trnType),
                    allToNull(trnSymb),
                    allToNull(crewOrgnOs),
                    boardTypeObj,
                    page,
                    DEFAULT_PAGE_SIZE,
                    sortModelObjs
            );
            val result = transform(trainSummaries, jsonStudyMapper::toJson);
            return listResponse(request, result);
        });
    }

    /**
     * Fetch train viewer details of a study for export details purpose.
     * The results further filtered with train type, crew origin os, train, train crew district and board type combination.
     *
     * @param studyId Study id
     * @param trnCrewDistr Train crew district
     * @param trnType Train type
     * @param trnSymb Train symbol
     * @param crewOrgnOs Crew origin os
     * @param boardType Board type ({@value com.nscorp.ccp.rest.config.Constants#POOL}
     *                 / {@value com.nscorp.ccp.rest.config.Constants#EXTRA})
     * @param request
     * @return Train viewer details.
     * @see JsonTrainViewerReport
     */
    @GetMapping(path = "/trainViewerExportDetails/studyId/{studyId}")
    public ResponseEntity<JsonCollectionResponse> getTrainViewerExportDetails(
            @PathVariable final long studyId,
            @RequestParam(value = "trnCrewDistr", required = false, defaultValue = "ALL") final String trnCrewDistr,
            @RequestParam(value = "trnType", required = false, defaultValue = "ALL") final String trnType,
            @RequestParam(value = "trnSymb", required = false, defaultValue = "ALL") final String trnSymb,
            @RequestParam(value = "crewOrgnOs", required = false, defaultValue = "ALL") final String crewOrgnOs,
            @RequestParam(value = "boardType", required = false, defaultValue = "ALL") final String boardType,
            @RequestParam(value = "numRows", required = false) final Integer numRows,
            @RequestParam(value = "startRow", required = false) final Integer startRow
            , final HttpServletRequest request) {
        // If numRows and startRow are both null, then assign a default.
        val tup = (numRows == null && startRow == null) ? Tuple.of(1, 1_000_000) : Tuple.of(startRow, numRows);
        val finalStartRow = tup._1;
        val finalNumRows = tup._2;
        return runner.run(() -> {
            info(log, "getTrainViewerExportDetails studyId - {}, trnCrewDistr - {}, trnType - {}, trnSymb - {},crewOrgnOs - {}, boardType - {}, numRows - {}, startRow - {}", studyId, trnCrewDistr, trnType, trnSymb, crewOrgnOs, boardType, finalNumRows, finalStartRow);
            //board type  Pool :1, extra :0, other : -1, ALL: null.
            val boardTypeObj = parseBoardType(boardType);
            val trains = reportManager.getTrainExportDetails(
                    studyId,
                    allToNull(trnCrewDistr),
                    allToNull(trnType),
                    allToNull(trnSymb),
                    allToNull(crewOrgnOs),
                    boardTypeObj,
                    finalNumRows,
                    finalStartRow
            );
            val result = transform(trains, jsonStudyMapper::toJson);
            return listResponse(request, result);
        });
    }

    @GetMapping(path = "/trainViewerExportDetailsAsExcel/studyId/{studyId}")
    public ResponseEntity<Resource> getTrainViewerExportDetailsAsExcel(
            @PathVariable final long studyId,
            @RequestParam(value = "trnCrewDistr", required = false, defaultValue = "ALL") final String trnCrewDistr,
            @RequestParam(value = "trnType", required = false, defaultValue = "ALL") final String trnType,
            @RequestParam(value = "trnSymb", required = false, defaultValue = "ALL") final String trnSymb,
            @RequestParam(value = "crewOrgnOs", required = false, defaultValue = "ALL") final String crewOrgnOs,
            @RequestParam(value = "boardType", required = false, defaultValue = "ALL") final String boardType,
            final HttpServletRequest request) {
        return runner.run(() -> {
            val trains = trainViewerReportFetcher.getTrains(studyId, trnCrewDistr, trnType, trnSymb, crewOrgnOs, boardType);
            val bytes = trainViewerReportExporter.export(trains);
            val resource = new ByteArrayResource(bytes);
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(resource.contentLength())
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        ContentDisposition.builder("attachment")
                            .filename("whatever")
                            .build().toString())
                .body(resource);
        });
    }

    /**
     * Fetch trains for a given study, train symbol, from os, to os and train type combination.
     * The results further filtered with train crew district, crew origin os and board type combination.
     *
     * @param studyId Study id
     * @param trnSymb Train symbol
     * @param fromOs From os
     * @param toOs To os
     * @param trnType Train type
     * @param trnCrewDistr Train crew district
     * @param crewOrgnOs Crew origin os
     * @param boardType Board type
     * @param request
     * @return Trains.
     * @see JsonTrainViewerReport
     */
    @GetMapping(path = "/trainViewerDetails")
    public ResponseEntity<JsonCollectionResponse> getTrainViewerDetails(
            @RequestParam(name = "studyId") Long studyId,
            @RequestParam(name = "trnSymb") String trnSymb,
            @RequestParam(name = "fromOs") String fromOs,
            @RequestParam(name = "toOs") String toOs,
            @RequestParam(name = "trnType") String trnType,
            @RequestParam(value = "trnCrewDistr", required = false, defaultValue = "ALL") String trnCrewDistr,
            @RequestParam(value = "crewOrgnOs", required = false, defaultValue = "ALL") String crewOrgnOs,
            @RequestParam(value = "boardType", required = false, defaultValue = "ALL") String boardType,
            HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getTrainViewerDetails : studyId - {}, trnSymb - {}, fromOs - {} , toOs - {} , trnType - {}, trnCrewDistr - {}, crewOrgnOs - {}, boardType - {}", studyId, trnSymb, fromOs, toOs, trnType, trnCrewDistr, crewOrgnOs, boardType);
            val boardTypeObj = parseBoardType(boardType);
            val trains = reportManager.getTrainsBySummaryInfo(
                    studyId,
                    trnSymb,
                    fromOs,
                    toOs,
                    trnType,
                    allToNull(trnCrewDistr),
                    allToNull(crewOrgnOs),
                    boardTypeObj
            );
	        return listResponse(request, transform(trains, jsonStudyMapper::toJson));
        });
    }

    //Train Viewer filters

    /**
     * Fetch train viewer filters for a study.
     *
     * @param studyId Study id
     * @param request
     * @return Train viewer filter combinations.
     * @see TrainViewerFilter
     */
    @GetMapping(path = "/trainViewerFilters/studyId/{studyId}")
    public ResponseEntity<JsonCollectionResponse> getTrainViewerFilters(
            @PathVariable long studyId,
            HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getTrainViewerFilters studyId - {}", studyId);
            val filters = reportManager.getTrainViewerFiltersByStudyId(studyId);
	        return listResponse(request, transform(filters, jsonStudyMapper::toJson));
        });
    }

    @DeleteMapping("/train")
    public ResponseEntity<JsonStatus> deleteTrains(
            @RequestParam(value="studyId", required = true) Long studyId,
            @RequestParam(value="trainId", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trainIdStr,
            @RequestParam(value="trnSymb", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trnSymb,
            @RequestParam(value="trnCrewDistr", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trnCrewDistr,
            @RequestParam(value="fromOs", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String fromOs,
            @RequestParam(value="toOs", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String toOs ) {
        return runner.run(()-> {
            final var params = getTrainDeletionParams(trainIdStr, trnSymb, trnCrewDistr, fromOs, toOs);
            warn(log, String.format("deleteTrains().  studyId=%s, params=%s", studyId, params));
            val n = trainDeleter.delete(studyId, params);
            return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }

    private static TrainDeletionParams getTrainDeletionParams(final String trainIdStr, final String trnSymb, final String trnCrewDistr, final String fromOs, final String toOs) {
	    return TrainDeletionParams.builder().
	            trainId(parseLongRestParam(trainIdStr)).
	            trnSymb(parseStringRestParam(trnSymb)).
	            trnCrewDistr(parseStringRestParam(trnCrewDistr)).
	            fromOs(parseStringRestParam(fromOs)).
	            toOs(parseStringRestParam(toOs)).
	            build();
    }

    @PostMapping("/train/updateCrewProfile")
    public ResponseEntity<JsonStatus> updateTrains(
            @RequestParam(value="studyId", required = true) Long studyId,
            @RequestParam(value="trainId", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trainIdStr,
            @RequestParam(value="trnSymb", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trnSymb,
            @RequestParam(value="trnCrewDistr", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String trnCrewDistr,
            @RequestParam(value="fromOs", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String fromOs,
            @RequestParam(value="toOs", required = false, defaultValue = ValueConstants.DEFAULT_NONE) String toOs,
            @RequestBody JsonTrainViewerRequest trainViewerRequest ) {
        return runner.run(()-> {
            final var trainProfileUpdateInfo = getTrainProfileUpdateInfo(trainIdStr, trnSymb, trnCrewDistr, fromOs, toOs, trainViewerRequest);
            warn(log, String.format("updateTrains(). studyId=%s, trainProfileUpdateInfo=%s", studyId, trainProfileUpdateInfo));
            val n = trainProfileUpdater.update(studyId, trainProfileUpdateInfo);
            return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }

    private static TrainProfileUpdateInfo getTrainProfileUpdateInfo(final String trainIdStr, final String trnSymb, final String trnCrewDistr, final String fromOs, final String toOs, final JsonTrainViewerRequest trainViewerRequest) {
	    return TrainProfileUpdateInfo.builder().
	            profileId(trainViewerRequest.getProfileId()).
	            crewOrgnOs(trainViewerRequest.getCrewOrgnOs()).
	            deptArrAdj(trainViewerRequest.getDeptArrAdj()).
	            transitTimeAdj(trainViewerRequest.getTransitTimeAdj()).
	            trainId(parseLongRestParam(trainIdStr)).
	            trnSymb(parseStringRestParam(trnSymb)).
	            trnCrewDistr(parseStringRestParam(trnCrewDistr)).
	            fromOs(parseStringRestParam(fromOs)).
	            toOs(parseStringRestParam(toOs)).
	            build();
    }
}
