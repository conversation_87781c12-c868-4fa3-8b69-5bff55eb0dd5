package com.nscorp.ccp.rest;
import com.nscorp.ccp.utils.rest.*;
import lombok.val;

import com.nscorp.ieorcommons.guava.*;
import static com.google.common.collect.ImmutableList.*;
import static com.nscorp.ccp.rest.FilterUtils.allToNull;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.dao.plan.CrewProfileReportDAO;
import com.nscorp.ccp.biz.opd.OpdIntegrationManager;
import com.nscorp.ccp.biz.plan.*;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.json.export.*;
import com.nscorp.ccp.rest.json.filter.*;
import com.nscorp.ccp.rest.mappers.JsonPlanMapper;
import com.nscorp.ccp.rest.mappers.export.JsonPlanExportMapper;
import com.nscorp.ccp.rest.mappers.filter.JsonPlanFilterOptionsMapper;
import com.nscorp.ccp.utils.commonTypes.sort.SortModel;
import com.nscorp.ccp.utils.json.JsonUtils;
import com.nscorp.ccp.utils.testing.BackgroundInvoker;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

import static com.google.common.collect.ImmutableList.copyOf;
import static com.google.common.collect.Iterables.filter;
import static com.google.common.collect.Iterables.transform;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.rest.config.Constants.*;
import static com.nscorp.ccp.utils.rest.RestUtil.*;
import static io.vavr.Function0.constant;
import static io.vavr.Function0.of;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;

/**
 * Provides rest API's for plan management and plan reports.
 */
@Slf4j
@AllArgsConstructor
@RestController
@Builder
public class PlanRestService {
    private final PlanCopier planCopier;
    private final PlanManager planManager;
    private final PoolCreator poolCreator;
    private final PlanReportManager planReportManager;
    private final CrewProfileReportDAO crewProfileReportDAO;
    private final JsonPlanMapper jsonPlanMapper;
    private final JsonPlanFilterOptionsMapper jsonPlanFilterOptionsMapper;
    private final JsonPlanExportMapper jsonPlanExportMapper;
    private final BackgroundInvoker backgroundInvoker;
    private final OpdIntegrationManager opdIntegrationManager;
    private final PoolUpdater poolUpdater;
    private final PlanUpdater planUpdater;
    private final ExbPoolSetUpUpdater exbPoolSetUpUpdater;
    private final ExbPoolSetUpCreator exbPoolSetUpCreator;
    private final RestMethodRunner runner;

    /**
     * Fetch all opd plans.
     *
     * @param request
     * @return Opd plans.
     * @see JsonOpdPlan
     */
    @GetMapping(path = "/opdPlans")
    public ResponseEntity<JsonCollectionResponse> getAllOpdPlans(HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getAllOpdPlans");
            // Fetch the OPD plans.
            val plans = opdIntegrationManager.getPlans();

            // Transform into JsonOpdPlan's and return.
            return listResponse(request, transform(plans, jsonPlanMapper::toJson));
        });
    }

    /**
     * Fetch all plans except deleted.
     *
     * @param request
     * @return Plans.
     * @see JsonPlan
     */
    @GetMapping(path = "/plans")
    public ResponseEntity<JsonCollectionResponse> getAllPlans(HttpServletRequest request) {
        return runner.run(() -> {
            // Get all the plans.
            val allPlans = filter(planManager.getAllPlans(), plan -> plan.getStatus() != PlanStatus.DELETED);

            // Translate to json objects and return.
	        return listResponse(request, transform(allPlans, jsonPlanMapper::toJson));
        });
    }

    /**
     * Update plan's name and description for a given plan id. If plan isn't present returns bad request.
     *
     * @param id      Plan id
     * @param name    Plan name
     * @param desc    Plan description
     * @param request Request to read url and user id
     * @return HttpStatus and empty body.
     */
    @PostMapping(path = "/plans/id/{id}")
    public ResponseEntity<Object> updatePlan(
            @PathVariable long id,
            @RequestParam(value = "name", defaultValue = "") String name,
            @RequestParam(value = "desc", defaultValue = "", required = false) String desc,
            @RequestParam(required = false, value = "updater") String updater ) {
        return runner.run(() -> {
            if ( log.isWarnEnabled() ) {
                warn(log, "updatePlan : id - {}, name - {}, desc - {}, updater - {}  ", id, name, desc, updater);
            }
            val userId = defaultIfNull(updater, "sys");
            val upd = PlanUpdate.builder().
                    planId(id).
                    planName(name.trim()).
                    planDescription(desc.trim()).
                    userId(userId).
                    build();
            return Try.of(()->planUpdater.updatePlan(upd)).
                    map(plan->ResponseEntity.<Object>ok().build()).
                    getOrElseGet(e->new ResponseEntity<>(JsonStatus.ofFailure(e.toString()), HttpStatus.BAD_REQUEST));
        });
    }

    /**
     * Create new plan from existing plan with provided name and description. If plan isn't present returns bad request.
     * Plan will be created in separate thread.
     *
     * @param id      Plan id
     * @param name    Plan name
     * @param desc    Plan description
     * @param request Request object to get request url and user id
     * @return New plan.
     * @see Plan
     */
    @PostMapping(path = "/plans/copy/id/{id}")
    public ResponseEntity<JsonStatus> copyPlan(
            @PathVariable long id,
            @RequestParam(value = "name", defaultValue = "") String name,
            @RequestParam(value = "desc", defaultValue = "") String desc,
            @RequestParam(required = false, value = "updater") String updater) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "copyPlan : id - {}, name - {}, desc - {}  ", id, name, desc);
            }
            val userId = defaultIfNull(updater, "sys");
            return Try.of(()->planCopier.beginCopy(id, userId, name, desc)).
                    andThen( inProgressPlan->backgroundInvoker.run(() -> planCopier.finishCopy(inProgressPlan.getPlanId()))).
                    map(plan->new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK)).
                    getOrElseGet(e->new ResponseEntity<>(JsonStatus.ofFailure(e.toString()), HttpStatus.BAD_REQUEST));
        });
    }

    /**
     * Soft delete a plan with provided id. If plan isn't present return bad request.
     *
     * @param id      long Plan id
     * @param request Request object to get request url and user id
     * @return Plan delete status.
     * @see JsonStatus
     */
    @DeleteMapping(path = "/plans/id/{id}")
    public ResponseEntity<JsonStatus> deletePlan(
            @PathVariable long id) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "deletePlan : id - {}", id);
            }
            val success = planManager.markPlanDeletedIfNotLocked(id) != null;
	        return new ResponseEntity<>(JsonStatus.of(success), HttpStatus.OK);
        });
    }

    @GetMapping(path = "/plans/terminals/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getUniqueTerminals(
            final @PathVariable Long planId,
            final HttpServletRequest request) {
        return runner.run(()-> RestUtil.listResponse(request, planReportManager.getUniqueTerminalsByPlanId(planId)));
    }

    //Carded jobs
    /**
     * Fetch carded jobs summaries for a plan. The results further filtered with district, sub district and pool combination.
     *
     * @param planId  Plan id
     * @param distr  District
     * @param subDistr Sub district
     * @param pool Pool name
     * @param request
     * @return Carded job summaries.
     * @see JsonCardedJobSummary
     */
    @GetMapping(path = "/plans/cardedJobs/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getCardedJobs(
            @PathVariable Long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getCardedJobs : planId - {}, distr - {}, subDistr - {}, pool - {}", planId, distr, subDistr, pool);
            }
            val cardedJobSummaries = planReportManager.getCardedJobSummariesByPlanIdAndDistrAndSubDistrAndPoolName(
                    planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool));
	        return listResponse(request, transform(cardedJobSummaries, jsonPlanMapper::toJson));
        });
    }

    //Carded job Details

    /**
     * Fetch carded job details for a carded job id.
     *
     * @param cardedPoolId Carded job id
     * @param request
     * @return Carded job details.
     * @see JsonCardedJobDetail
     */
    @GetMapping(path = "/plans/cardedJobDetails/id/{cardedPoolId}")
    public ResponseEntity<JsonCollectionResponse> getCardedJobDetails(
            @PathVariable long cardedPoolId
            , HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "cardedJobDetails : cardedPoolId - {}", cardedPoolId);
            }
            val cardedJobDetails = planReportManager.getCardedJobDetailsByCardedPoolId(cardedPoolId);
	        return listResponse(request, transform(cardedJobDetails, jsonPlanMapper::toJson));
        });
    }

    //Carded Job export details

    /**
     * Fetch carded job details of a plan for export details purpose. The result includes information from carded job and carded job details.
     * And results further filtered with district, sub district and pool combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param pool     Pool name
     * @param request
     * @return Carded job details.
     * @see JsonExpCardedJob
     */
    @GetMapping(path = "/plans/cardedJobExportDetails/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getCardedJobExportDetails(
            @PathVariable Long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            HttpServletRequest request) {
        if ( log.isInfoEnabled() ) {
            info(log, "getCardedJobExportDetails : planId - {}, distr - {}, subDistr - {}, pool - {}", planId, distr, subDistr, pool);
        }
        return runner.run(() -> {
            val expCardedJobs = planReportManager.getExpCardedJobsByPlanIdAndDistrAndSubDistrAndPoolName(
                    planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool));
	        return listResponse(request, transform(expCardedJobs, jsonPlanMapper::toJson));
        });
    }


    //Crew Profiles

    /**
     * Fetch crew profiles for a plan with pagination. The results further filtered with district, sub district and pool combination.
     * And sorted with district, sub district, pool name, crew origin os, profile id, crew destin os and craft combination.
     *
     * @param planId     Plan id
     * @param page       Page number of the result. If not provided all crew profiles will be fetched for matching criteria.
     * @param distr      District
     * @param subDistr   Sub district
     * @param pool       Pool name
     * @param sortModels  A collection of strings containing sort fields in <field>:<sort type> format.
     *                   Possible values for field are: distr, subDistr, poolName, crewOrgnOs, profileId, crewDestinOs, craft.
     *                   Possible values for type are: asc, desc.
     * @param request
     * @return Crew profiles.
     * @see JsonCrewProfile
     * @see SortModel
     */
    @GetMapping(path = "/plans/crewProfiles/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getCrewProfiles(
            @PathVariable Long planId,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            @RequestParam(name = "sortModels", required = false) List<String> sortModels,
            HttpServletRequest request) {
        return runner.run(() -> {
            val sortModelObjs = SortUtil.getSortModels(sortModels);
            val crewProfiles = crewProfileReportDAO.getCrewProfileReport(
                    planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool),
                    page,
                    DEFAULT_PAGE_SIZE,
                    sortModelObjs);
            warn(log, String.format("crewProfiles=%s", crewProfiles));
	        return listResponse(request, transform(crewProfiles, jsonPlanMapper::toJson));
        });
    }

    @GetMapping(path = "/plans/crewProfiles/id/{crewProfileId}")
    public ResponseEntity<JsonCrewProfile> getCrewProfileByCrewProfileId(
            @PathVariable Long crewProfileId,
            HttpServletRequest request) {
        return runner.run(() -> Optional.ofNullable(planManager.getCrewProfileById(crewProfileId)).
                map(jsonPlanMapper::toJson).
                map(result->new ResponseEntity<>(result.withHref(""+request.getRequestURL()), HttpStatus.OK)).
                orElseGet(()->new ResponseEntity(JsonStatus.ofFailure("Crew Profile not found with Id: " + crewProfileId), HttpStatus.NOT_FOUND)));
    }

    @PostMapping(path="/plans/crewProfiles")
    public ResponseEntity<JsonCrewProfile> createCrewProfile(
            @Validated @RequestBody JsonCrewProfile jsonCrewProfile ) {
        return runner.run(() -> constant(jsonCrewProfile).
                andThen(jsonPlanMapper::fromJson).
                andThen(planManager::saveIfNotLocked).
                andThen(jsonPlanMapper::toJson).
                andThen(savedAsJson->new ResponseEntity<>(savedAsJson, HttpStatus.OK)).
                get());
    }

    @PostMapping(path="/plans/crewProfiles/id/{crewProfileId}")
    public ResponseEntity<Object> updateCrewProfile(
            @PathVariable long crewProfileId,
            @Validated @RequestBody JsonCrewProfile jsonCrewProfile ) {
        return runner.run(()-> constant(jsonCrewProfile).
		        andThen(e -> e.withCrewProfileId(crewProfileId)).
		        andThen(jsonPlanMapper::fromJson).
		        andThen(planManager::saveIfNotLocked).
		        andThen(e -> ResponseEntity.ok().build()).
                get());
    }

    @DeleteMapping(path = "/plans/crewProfiles/id/{crewProfileId}")
    public ResponseEntity<JsonStatus> deleteCrewProfileId(
            final @PathVariable long crewProfileId) {
        return runner.run(() -> {
            planManager.deleteCrewProfileByIdIfNotLocked(crewProfileId);
	        return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }


    //Extraboard Summaries

    /**
     * Fetch extra board summaries for a plan. The results further filtered with district, sub district combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param request
     * @return Extra board summaries.
     * @see JsonExtraboardSummary
     */
    @GetMapping(path = "/plans/extraboardSummaries/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getExtraBoardSummaries(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            HttpServletRequest request) {
        return runner.run(() -> {
	        val extraboardSummaries = planReportManager.getExtraBoardSummariesByPlanIdAndFilterCriteria(
                    planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr);
	        return listResponse(request, transform(extraboardSummaries, jsonPlanMapper::toJson));
        });
    }

    //Extraboard Details

    /**
     * Fetch extraboard details for a given extraboard id.
     *
     * @param exbId   Extraboard id
     * @param request
     * @return Extraboard pool setup detail.
     * @see JsonExbPoolSetUp
     */
    @GetMapping(path = "/plans/extraboardDetails/id/{exbId}")
    public ResponseEntity<JsonExbPoolSetUp> getExtraBoardDetails(
            @PathVariable long exbId, HttpServletRequest request) {
        return runner.run(() -> Optional.ofNullable(planManager.getExbPoolSetUpById(exbId)).
                map(jsonPlanMapper::toJson).
                map(jsonExbPoolSetUp -> new ResponseEntity<>(jsonExbPoolSetUp.withHref(""+request.getRequestURL()), HttpStatus.OK)).
                orElseGet(()-> new ResponseEntity(JsonStatus.ofFailure("Extraboard not found with Id: " + exbId), HttpStatus.NOT_FOUND)));
    }

    /**
     * Fetch extraboard details of a plan for export details purpose. This will include turns and supported pools information.
     * The results further filtered with district, sub district combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param request
     * @return Extraboard details containing turns and supported pools.
     * @see JsonExpExb
     */
    @GetMapping(path = "/plans/extraboardExportDetails/planId/{planId}")
    public ResponseEntity<JsonExpExb> getExtraBoardExportDetails(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            HttpServletRequest request) {
        return runner.run(() -> {
	        val details = planReportManager.getExtraBoardExportDetailsByPlanIdAndFilterCriteria(
                    planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr);

            val result = jsonPlanExportMapper.mapExpExb(details.getTurns(), details.getSupportedPools(), "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Id pools

    /**
     * Fetch id pool summaries for a plan. The results further filtered with district, sub district and craft combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param craft    Craft
     * @param request
     * @return Id pools.
     * @see JsonIdPool
     */
    @GetMapping(path = "/plans/idPools/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getIdPools(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "craft", defaultValue = "ALL") String craft,
            HttpServletRequest request) {
        return runner.run(() -> {
	        val idPools = planManager.getIdPoolsByPlanIdAndFilterCriteria(planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr,
                    craft.equals("ALL") ? null : craft);
	        return listResponse(request, transform(idPools, jsonPlanMapper::toJson));
        });
    }


    //Pool Setup

    /**
     * Fetch pool summaries for a plan. The results further filtered with district, sub district and pool combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param pool     Pool name
     * @param request
     * @return Pool summaries.
     * @see JsonPoolSummary
     */
    @GetMapping(path = "/plans/poolSetups/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getPoolSetups(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            HttpServletRequest request) {
        return runner.run(() -> {
	        val poolSummaries = planReportManager.getPoolSummariesByPlanIdAndFilterCriteria(planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr,
                    pool.equals("ALL") ? null : pool);
	        return listResponse(request, transform(poolSummaries, jsonPlanMapper::toJson));
        });
    }

    /**
     * Fetch pool setups of a plan for export details purpose. This will include terminals, turns, mark-off, pool size and other information.
     * The results further filtered with district, sub district and pool combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param pool     Pool name
     * @param request
     * @return Pool setup details containing terminals, turns, mark-off and other information.
     * @see JsonExpPoolSetUp
     */
    @GetMapping(path = "/plans/poolSetupExportDetails/planId/{planId}")
    public ResponseEntity<JsonExpPoolSetUp> getPoolSetupExportDetails(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getPoolSetupExportDetails planId - {}, distr - {}, subDistr - {}, pool - {}", planId, distr, subDistr, pool);
            }

	        val details = planReportManager.getPoolSetupExportDetails(planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool));

            return new ResponseEntity<>(jsonPlanExportMapper.mapExpPoolSetUp(details.getTerminals(), details.getTurns(), details.getOthers(), "" + request.getRequestURL()), HttpStatus.OK);
        });
    }

    //Pool Setup details

    /**
     * Fetch pool setup for a pool setup id.
     *
     * @param id  Pool setup id
     * @param request
     * @return Pool setup.
     * @see JsonPool
     */
    @GetMapping(path = "/plans/poolSetup/id/{id}")
    public ResponseEntity<JsonPool> getPoolSetup(
            @PathVariable long id,
            HttpServletRequest request) {
        return runner.run(() -> Optional.ofNullable(planManager.getPoolById(id)).
                map(jsonPlanMapper::toJson).
                map(jsonPool->new ResponseEntity<>(jsonPool.withHref(""+request.getRequestURL()), HttpStatus.OK)).
                orElseGet(()-> new ResponseEntity(JsonStatus.ofFailure("Pool setup not found with Id: " + id), HttpStatus.NOT_FOUND)));
    }

    @DeleteMapping(path = "/plans/pool/poolId/{poolId}")
    public ResponseEntity<JsonStatus> deletePool(
            final @PathVariable long poolId) {
        return runner.run(() -> {
            planManager.deletePoolByIdIfNotLocked(poolId);
	        return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }

    @PostMapping(path="/plans/exbPoolSetUp")
    public ResponseEntity<JsonExbPoolSetUp> createExbPoolSetUp(
            @Validated @RequestBody JsonExbPoolSetUp jsonExbPoolSetUp ) {
	    // Creating a new pool.
	    return runner.run(()-> constant(jsonExbPoolSetUp).
			    andThen(jsonPlanMapper::fromJson).
			    andThen(exbPoolSetUpCreator::create).
			    andThen(jsonPlanMapper::toJson).
			    andThen(e -> new ResponseEntity<>(e, HttpStatus.OK)).
                get());
    }

    @PostMapping(path="/plans/exbPoolSetUp/id/{id}")
    public ResponseEntity<Object> updateExbPoolSetUp(
            @PathVariable long id,
            @Validated @RequestBody JsonExbPoolSetUp jsonExbPoolSetUp ) {
        return runner.run(() -> constant(jsonExbPoolSetUp).
                andThen(jsonPlanMapper::fromJson).
                andThen(fromRequest->exbPoolSetUpUpdater.update(fromRequest, id)).
                andThen(e->ResponseEntity.ok().build()).
                get());
    }

    @DeleteMapping(path="/plans/exbPoolSetUp/id/{id}")
    public ResponseEntity<JsonStatus> deleteExbPoolSetUp(
            @PathVariable long id ) {
        return runner.run(() -> {
            planManager.deleteExbPoolSetUpByIdIfNotLocked(id);
	        return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }

    /**
     * Returns a list of {@link JsonHub}'s, each corresponding to a hub defined for plan {@code planId}.
     * @param planId The plan ID.
     * @return a {@link JsonCollectionResponse} containing a list of {@link JsonHub}'s.
     * @see JsonHub
     */
    @GetMapping(path = "/plans/hub/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getHubs(
            @PathVariable Long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getHubs : planId - {}", planId);
            }
            val hubs = planManager.getHubsByPlanId(planId);
	        return listResponse(request, transform(hubs, jsonPlanMapper::toJson));
        });
    }

    @GetMapping(path = "/plans/hubDetail/hubId/{hubId}")
    public ResponseEntity<JsonHubDetail> getHubDetail(
            @PathVariable Long hubId ) {
        val hubDetails = planManager.getHubDetails(hubId);
        if ( hubDetails == null ) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
        val jsonHubDetail = jsonPlanMapper.toJsonHubDetail(hubDetails.getHub());
        val pools = hubDetails.getPools().stream().map(jsonPlanMapper::toJson).collect(toImmutableList());
        val extraboards = hubDetails.getExtraboards().stream().map(jsonPlanMapper::toJson).collect(toImmutableList());
        val result = jsonHubDetail.toBuilder().pools(pools).extraboards(extraboards).build();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping(path="/plans/hub")
    public ResponseEntity<JsonHub> createHub(
            @Validated @RequestBody JsonHub jsonHub ) {
        return runner.run(() -> {
            // Creating a new pool.
            if ( log.isWarnEnabled() ) {
                warn(log, String.format("createHub(): jsonHub=%s", JsonUtils.prettyPrint(jsonHub)));
            }
            return constant(jsonHub).
                    andThen(jsonPlanMapper::fromJson).
                    andThen(planManager::saveIfNotLocked).
                    andThen(jsonPlanMapper::toJson).
                    andThen(e->new ResponseEntity<>(e, HttpStatus.OK)).
                    get();
        });
    }

    @PostMapping(path="/plans/hub/hubId/{hubId}")
    public ResponseEntity<Object> updateHub(
            @PathVariable long hubId,
            @Validated @RequestBody JsonHub jsonHub) {
        return runner.run(() -> constant(jsonHub).
                andThen(e->e.withId(hubId)).
                andThen(jsonPlanMapper::fromJson).
                andThen(planManager::saveIfNotLocked).
                andThen(e->ResponseEntity.ok().build()).
                get());
    }

    @DeleteMapping(path = "/plans/hub/hubId/{hubId}")
    public ResponseEntity<JsonStatus> deleteHub(
            final @PathVariable long hubId) {
        return runner.run(() -> {
            planManager.deleteHubByIdIfNotLocked(hubId);
	        return new ResponseEntity<>(JsonStatus.SUCCESS, HttpStatus.OK);
        });
    }

    @PostMapping(path="/plans/pool/poolId/{poolId}")
    public ResponseEntity<String> updatePool(
            @PathVariable long poolId,
            @Validated @RequestBody JsonPool jsonPool ) {
        return runner.run(() -> {
            val poolFromRequest = jsonPlanMapper.fromJson(jsonPool);
            val savedPool = poolUpdater.update(poolFromRequest, poolId);
            return ResponseEntity.ok().build();
        });
    }

    @PostMapping(path="/plans/pool")
    public ResponseEntity<JsonPool> createPool(
        @Validated @RequestBody JsonPool jsonPool ) {
        return runner.run(() -> {
            // Creating a new pool.
            if ( log.isWarnEnabled() ) {
                warn(log, String.format("createPool(): jsonPool=%s", JsonUtils.prettyPrint(jsonPool)));
            }
            return constant(jsonPool).
                    andThen(jsonPlanMapper::fromJson).
                    andThen(poolCreator::create).
                    andThen(jsonPlanMapper::toJson).
                    andThen(e->new ResponseEntity<>(e, HttpStatus.OK)).
                    get();
        });
    }

    //Tieup exception rules

    /**
     * Fetch tieup exception summaries for a plan. The results further filtered with district and sub district combination.
     *
     * @param planId   Plan id
     * @param distr    District
     * @param subDistr Sub district
     * @param request
     * @return Tieup exception summaries.
     * @see JsonTieUpExceptionSummary
     */
    @GetMapping(path = "/plans/tieUpExceptionRules/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getTieUpExceptionRules(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            HttpServletRequest request) {
        return runner.run(() -> {
	        val tieUpExceptions = planReportManager.getTieUpExceptionSummariesByPlanIdAndFilterCriteria(planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr);
	        return listResponse(request, transform(tieUpExceptions, jsonPlanMapper::toJson));
        });
    }

    /**
     * Fetch tieup exception details of a plan for export details purpose. This will include tieup exception rule, sub-rule and search pool information.
     * The results further filtered with district and sub district combination.
     *
     * @param planId  Plan id
     * @param distr District
     * @param subDistr Sub district
     * @param request
     * @return Tieup exception details containing sub-rule and search pool information.
     * @see JsonExpTieUpException
     */
    @GetMapping(path = "/plans/tieUpExceptionRuleExportDetails/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getTieUpExceptionRuleExportDetails(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getTieUpExceptionRuleExportDetails planId - {}, distr - {}, subDistr - {}", planId, distr, subDistr);
            }
	        val tieUpExceptions = planManager.getConsolidatedTieUpExceptionsByPlanIdAndFilterCriteria(
                    planId,
                    distr.equals("ALL") ? null : distr,
                    subDistr.equals("ALL") ? null : subDistr);
	        return listResponse(request, transform(tieUpExceptions, jsonPlanMapper::toJson));
        });
    }

    //Tieup exception details

    /**
     * Fetch tieup exception details for a tieup exception id.
     *
     * @param tieUpExceptionId Tieup exception id
     * @return Tieup exception details.
     * @see JsonTieUpException
     */
    @GetMapping(path = "/plans/tieUpExceptionRuleDetails/id/{tieUpExceptionId}")
    public ResponseEntity<JsonTieUpException> getTieUpExceptionRuleDetails(
            @PathVariable long tieUpExceptionId) {
        return runner.run(() -> Optional.ofNullable(planManager.getTieUpExceptionById(tieUpExceptionId)).
                map(jsonPlanMapper::toJson).
                map(jsonTieUpException -> new ResponseEntity<>(jsonTieUpException, HttpStatus.OK)).
                orElseGet(() -> new ResponseEntity(JsonStatus.ofFailure("Tieup Exception not found with Id: " + tieUpExceptionId), HttpStatus.NOT_FOUND)));
    }

    //Work rest profiles

    /**
     * Fetch work rest profile summaries for a plan.
     * The results further filtered with district, sub district, pool  and pool type  combination.
     *
     * @param planId Plan id
     * @param distr District
     * @param subDistr Sub district
     * @param pool Pool name
     * @param poolType Pool type ({@value com.nscorp.ccp.rest.config.Constants#EXTRABOARD} / {@value com.nscorp.ccp.rest.config.Constants#SELFSUSTAINING})
     * @param request
     * @return Work rest profile summaries.
     * @see JsonWorkRestProfSummary
     */
    @GetMapping(path = "/plans/workRestProfiles/planId/{planId}")
    public ResponseEntity<JsonCollectionResponse> getWorkRestProfiles(@PathVariable long planId,
                                                                      @RequestParam(value = "distr", defaultValue = "ALL") String distr,
                                                                      @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
                                                                      @RequestParam(value = "pool", defaultValue = "ALL") String pool,
                                                                      @RequestParam(value = "poolType", defaultValue = "ALL") String poolType,
                                                                      HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getWorkRestProfiles planId - {}, distr - {}, subDistr - {}, pool - {}, poolTye - {}", planId, distr, subDistr, pool, poolType);
            }
	        val workRestProfSummaries = planReportManager.getWorKRestProfSummariesByPlanIdAndDistrAndSubDistrAndPoolNameAndPoolType(planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool),
			        mapWrpPooltype(poolType));
            val result = ImmutableCollectionUtils.mapToImmutableList(workRestProfSummaries, jsonPlanMapper::toJson);
            return new ResponseEntity<>(JsonCollectionResponse.builder().href("" + request.getRequestURL()).items(result).build(), HttpStatus.OK);
        });
    }

    private String mapWrpPooltype(String poolType) {
        final String result;
        if ( EXTRABOARD.equals(poolType) ) {
            result = DB_EB;
        }
        else if ( SELFSUSTAINING.equals(poolType) ) {
            result = DB_SS;
        }
        else {
            result = null;
        }
        return result;
    }

    /**
     * Fetch work rest profiles of a plan for export details purpose. This will include turns and rotations information.
     * The results further filtered with district, sub district, pool and pool type combination.
     *
     * @param planId Plan id
     * @param distr District
     * @param subDistr Sub district
     * @param pool Pool name
     * @param poolType Pool type ({@value com.nscorp.ccp.rest.config.Constants#EXTRABOARD} / {@value com.nscorp.ccp.rest.config.Constants#SELFSUSTAINING})
     * @param request
     * @return Work rest profile details containing turns and rotations.
     * @see JsonExpWorkRestProf
     */
    @GetMapping(path = "/plans/workRestProfileExportDetails/planId/{planId}")
    public ResponseEntity<JsonExpWorkRestProf> getWorkRestProfileExportDetails(
            @PathVariable long planId,
            @RequestParam(value = "distr", defaultValue = "ALL") String distr,
            @RequestParam(value = "subDistr", defaultValue = "ALL") String subDistr,
            @RequestParam(value = "pool", defaultValue = "ALL") String pool,
            @RequestParam(value = "poolType", defaultValue = "ALL") String poolType,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getWorkRestProfileExportDetails planId - {}, distr - {}, subDistr - {}, pool - {}, poolTye - {}", planId, distr, subDistr, pool, poolType);
            }

	        val expWorkRestProf = planReportManager.getExpWorkRestProfByPlanIdAndFilterCriteria(planId,
                    allToNull(distr),
                    allToNull(subDistr),
                    allToNull(pool),
                    mapWrpPooltype(poolType));
            val result = jsonPlanExportMapper.mapExpWorkRestProf(
                    expWorkRestProf.getTurns(),
                    expWorkRestProf.getRotations(),
                    "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Work rest profile details

    /**
     * Fetch work rest profile summary for a work rest profile id.
     *
     * @param workRestProfileId Work rest profile id
     * @param request
     * @return Work rest profile summary.
     * @see JsonWorkRestProf
     */
    @GetMapping(path = "/plans/workRestProfile/id/{workRestProfileId}")
    public ResponseEntity<JsonWorkRestProf> getWorkRestProfileDetails(@PathVariable long workRestProfileId,
                                                                      HttpServletRequest request) {
        return runner.run(() -> Optional.ofNullable(planManager.getWorkRestProfById(workRestProfileId)).
                map(jsonPlanMapper::toJson).
                map(result->new ResponseEntity<>(result.withHref(""+request.getRequestURL()), HttpStatus.OK)).
                orElseGet(()-> new ResponseEntity(JsonStatus.ofFailure("Work rest profile not found with Id: " + workRestProfileId), HttpStatus.NOT_FOUND)));
    }

    //Carded Jobs filters

    /**
     * Fetch carded job filters for a plan.
     *
     * @param planId Plan id
     * @param request
     * @return Carded job filters hierarchy.
     * @see JsonCardedJobsFilters
     */
    @GetMapping(path = "/plans/cardedJobsFilters/planId/{planId}")
    public ResponseEntity<JsonCardedJobsFilters> getCardedJobsFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getCardedJobsFilters planId - {}", planId);
            }
            val cardedJobFilters = planReportManager.getCardedJobFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapCardedJobFilters(cardedJobFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Crew Profile filters

    /**
     * Fetch crew profile filters for a plan.
     *
     * @param planId Plan id
     * @param request
     * @return Crew profile filters hierarchy.
     * @see JsonCrewProfilesFilters
     */
    @GetMapping(path = "/plans/crewProfilesFilters/planId/{planId}")
    public ResponseEntity<JsonCrewProfilesFilters> getCrewProfileFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getCrewProfileFilters planId - {}", planId);
            }
            val crewProfileFilters = planReportManager.getCrewProfileFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapCrewProfileFilters(crewProfileFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    /**
     * Fetch all Crew Profiles unique combination of Crew Origin Os and Crew Profile Id in system.
     *
     * @param request
     * @return
     */
    @GetMapping(path = "/plans/crewProfilesFilters/all")
    public ResponseEntity<JsonCollectionResponse> getCrewProfileSimpleFilters(
            HttpServletRequest request) {
        return runner.run(() -> listResponse(request, transform(planReportManager.getCrewProfileSimpleFilters(), jsonPlanMapper::toJson)));
    }

    /**
     * Fetch all plans for a given Crew Origin Os and Crew Profile combination.
     *
     * @param crewOriginOs Crew Origin Os
     * @param profileId Profile Id
     * @param request
     * @return Collection of plans
     */
    @GetMapping(path = "/plans/crewOriginOs/{crewOriginOs}/profileId/{profileId}")
    public ResponseEntity<JsonCollectionResponse> getCrewProfilePlans(
            @PathVariable String crewOriginOs,
            @PathVariable String profileId,
            HttpServletRequest request) {
        return runner.run(() -> of(()->planReportManager.getPlans(crewOriginOs, profileId)).
                andThen(e->transform(e, jsonPlanMapper::toJson)).
                andThen(e->listResponse(request, e)).
                get());
    }

    //Extraboard Summaries filters

    /**
     * Fetch extraboard filters for a plan.
     *
     * @param planId Plan id
     * @param request
     * @return Extraboard filters hierarchy.
     */
    @GetMapping(path = "/plans/extraboardSummariesFilters/planId/{planId}")
    public ResponseEntity<JsonExtraboardFilters> getExtraboardSummariesFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "extraboardSummariesFilters planId - {}", planId);
            }
            val extraboardFilters = planReportManager.getExtraboardFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapExtraboardFilters(extraboardFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //IdPool filters

    /**
     * Fetch id pool filters for a plan.
     *
     * @param planId  Plan id
     * @param request
     * @return Id pool filter hierarchy.
     * @see JsonIdPoolFilters
     */
    @GetMapping(path = "/plans/idPoolsFilters/planId/{planId}")
    public ResponseEntity<JsonIdPoolFilters> getIdPoolFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            if ( log.isInfoEnabled() ) {
                info(log, "getIdPoolFilters planId - {}", planId);
            }
            val idPoolFilters = planReportManager.getIdPoolFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapIdPoolFilters(idPoolFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Pool setup filters

    /**
     * Fetch pool setup filters for a plan.
     *
     * @param planId Plan id
     * @param request
     * @return Pool setup filters hierarchy.
     * @see JsonPoolSetupFilters
     */
    @GetMapping(path = "/plans/poolSetupsFilters/planId/{planId}")
    public ResponseEntity<JsonPoolSetupFilters> getPoolSetupFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {

            if ( log.isInfoEnabled() ) {
                info(log, "getPoolSetupFilters planId - {}", planId);
            }
            val poolSetupFilters = planReportManager.getPoolSetupFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapPoolSetupFilters(poolSetupFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Tie up exception filters

    /**
     * Fetch tieup exception filters for a plan.
     *
     * @param planId  Plan id
     * @param request
     * @return Pool setup filters hierarchy.
     * @see JsonPoolSetupFilters
     */
    @GetMapping(path = "/plans/tieUpExceptionRulesFilters/planId/{planId}")
    public ResponseEntity<JsonTueFilters> getTueFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getTueFilters planId - {}", planId);
            val tueFilters = planReportManager.getTueFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapTueFilters(tueFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }

    //Work rest profiles  filters

    /**
     * Fetch work rest profile filters for a plan.
     *
     * @param planId Plan id
     * @param request
     * @return Work rest profile filters hierarchy.
     * @see JsonWrpFilters
     */
    @GetMapping(path = "/plans/workRestProfilesFilters/planId/{planId}")
    public ResponseEntity<JsonWrpFilters> getWrpFilters(
            @PathVariable long planId,
            HttpServletRequest request) {
        return runner.run(() -> {
            info(log, "getWrpFilters planId - {}", planId);
            val wrpFilters = planReportManager.getWrpFiltersByPlanId(planId);
            val result = jsonPlanFilterOptionsMapper.mapWrpFilters(wrpFilters, "" + request.getRequestURL());
            return new ResponseEntity<>(result, HttpStatus.OK);
        });
    }
}
