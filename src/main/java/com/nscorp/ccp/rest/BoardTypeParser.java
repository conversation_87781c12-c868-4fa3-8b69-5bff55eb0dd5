package com.nscorp.ccp.rest;

import com.nscorp.ccp.common.study.BoardType;
import com.nscorp.ccp.rest.config.Constants;
import lombok.experimental.UtilityClass;

import static com.nscorp.ccp.rest.config.Constants.POOL;

@UtilityClass
public class BoardTypeParser {
	public static BoardType parseBoardType(String typ) {
		final BoardType result;
		if ( typ == null || typ.equals("ALL") ) {
			result = null;
		}
		else if ( typ.equals(POOL) ) {
			result = BoardType.POOL;
		}
		else if ( typ.equals(Constants.EXTRA) ) {
			result = BoardType.EXTRABOARD;
		}
		else {
			throw new IllegalArgumentException(String.format("Unknown board type %s.", typ));
		}
		return result;
	}
}
