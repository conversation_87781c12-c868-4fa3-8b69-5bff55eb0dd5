package com.nscorp.ccp.rest;

import com.google.common.collect.Iterables;
import com.nscorp.ccp.biz.upside.UpsideManager;
import com.nscorp.ccp.biz.upside.UpsideUploader;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.mappers.JsonUpsideMapper;
import com.nscorp.ccp.utils.rest.RestMethodRunner;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;
import static com.nscorp.ccp.utils.rest.RestUtil.listResponse;

@Slf4j
@AllArgsConstructor
@RestController
@Builder
public class UpsideRestService {
	private final UpsideManager upsideManager;
	private final RestMethodRunner runner;
	private final JsonUpsideMapper mapper;
	private final UpsideUploader upsideUploader;

	@GetMapping(path = "/upsides")
	public ResponseEntity<JsonCollectionResponse> getAllUpsides(HttpServletRequest request) {
		return runner.run(() -> {
			val upsides = upsideManager.getAllUpsides();
			return listResponse(request, Iterables.transform(upsides, mapper::toJson));
		});
	}

	@GetMapping(path = "/upsides/trains/id/{id}")
	public ResponseEntity<JsonCollectionResponse> getTrainsByUpsideId(HttpServletRequest request,
		@PathVariable long id ) {
		return runner.run(() -> {
			val trains = upsideManager.getTrains(id);
			return listResponse(request, Iterables.transform(trains, mapper::toJson));
		});
	}

	@DeleteMapping(path = "/upsides/id/{id}")
	public ResponseEntity<JsonStatus> deleteUpside(
			@PathVariable long id) {
        return runner.run(() -> {
			upsideManager.deleteUpsideById(id);
	        return new ResponseEntity<>(JsonStatus.of(true), HttpStatus.OK);
        });
	}

	@SneakyThrows @PostMapping("/upsides/upload")
	public ResponseEntity<JsonUpsideUploadResponse> upload(
		@RequestParam String name,
		@RequestParam String desc,
		@RequestParam String userName,
		@RequestParam("file") MultipartFile file) {
		val result = upsideUploader.upload(name, desc, userName, file.getBytes());
		val response = JsonUpsideUploadResponse.builder().
				success(true).
				warnings(imlist()).
				errors(imlist()).
				build();
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
