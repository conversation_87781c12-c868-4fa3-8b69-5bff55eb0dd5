package com.nscorp.ccp.integration.impl.opd;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.integration.opd.*;
import retrofit2.http.*;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface OpdRetrofitClient {
	// @GET("/public/tpt/getOpdTrns")
	// CompletableFuture<List<JsonOpdTrain>> getOpdTrainsByScenSa(@Query("scenSa") Integer scenSa, @Query("startDt") LocalDate startDt, @Query("endDt") LocalDate endDt);

	@GET("/tpt/v1/trains/scen-sa/{scenSa}/{startDt}/{endDt}")
	CompletableFuture<List<JsonOpdTrain>> getOpdTrainsByScenSa(@Path("scenSa") Integer scenSa, @Path("startDt") LocalDate startDt, @Path("endDt") LocalDate endDt);

	// @GET("/public/tpt/getOpdTrns")
	// CompletableFuture<List<JsonOpdTrain>> getOpdTrainsByPlanSa(@Query("planSa") Integer planSa, @Query("startDt") LocalDate startDt, @Query("endDt") LocalDate endDt);

	@GET("/tpt/v1/trains/plan-sa/{planSa}/{startDt}/{endDt}")
	CompletableFuture<List<JsonOpdTrain>> getOpdTrainsByPlanSa(@Path("planSa") Integer planSa, @Path("startDt") LocalDate startDt, @Path("endDt") LocalDate endDt);

	@GET("/tpt/v1/scenarios")
	CompletableFuture<List<JsonOpdScenario>> getOpdScenarios();

	@GET("/tpt/v1/plans")
	CompletableFuture<List<JsonOpdPlan>> getOpdPlans();
}
