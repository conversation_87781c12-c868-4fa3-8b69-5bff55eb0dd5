package com.nscorp.ccp.integration.impl.opd;

import com.nscorp.ccp.common.opd.OpdScenOrPlanSa;
import com.nscorp.ccp.integration.opd.*;
import lombok.*;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.time.LocalDate;
import java.util.Optional;

import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;
import static com.nscorp.ccp.utils.logging.LogUtils.warn;

@Component
@Profile("! hsqldb-test")
public class OpdClientImpl implements OpdClient {
	private static final Logger log = org.slf4j.LoggerFactory.getLogger(OpdClientImpl.class);
	@Value("${tptUrl}")
	private String tptUrl;

	private OpdRetrofitClient getRetrofitClient() {
		val client = new OkHttpClient();
		val retrofit = new Retrofit.Builder().baseUrl(tptUrl).
				addConverterFactory(JacksonConverterFactory.create()).
				client(client).
				build();
		return retrofit.create(OpdRetrofitClient.class);
	}
	@SneakyThrows @Override public Iterable<JsonOpdScenario> getOpdScenarios() {
		return imlist( getRetrofitClient().getOpdScenarios().get());
	}
	@SneakyThrows @Override public Iterable<JsonOpdPlan> getOpdPlans() {
		return imlist(getRetrofitClient().getOpdPlans().get());
	}
	@Override public Iterable<JsonOpdTrain> getOpdTrains(@NonNull OpdScenOrPlanSa sa, @NonNull LocalDate startDt, @NonNull LocalDate endDt) {
		return Optional.ofNullable(sa.getScenSa()).
			map(theScenSa -> getOpdTrainsByScenSa(theScenSa, startDt, endDt)).
			orElseGet(() -> getOpdTrainsByPlanSa(sa.getPlanSa(), startDt, endDt));
	}
	@SneakyThrows private Iterable<JsonOpdTrain> getOpdTrainsByScenSa( Integer scenSa,  LocalDate startDt,  LocalDate endDt) {
		if ( log.isWarnEnabled() ) {
			warn(log, String.format("getOpdTrainsByScenSa() called with scenSa=%s, startDat=%s, endDat=%s",
					scenSa, startDt, endDt));
		}

		return getRetrofitClient().getOpdTrainsByScenSa(scenSa, startDt, endDt).get();
	}

	@SneakyThrows private Iterable<JsonOpdTrain> getOpdTrainsByPlanSa( Integer planSa,  LocalDate startDt,  LocalDate endDt) {
		return getRetrofitClient().getOpdTrainsByPlanSa(planSa, startDt, endDt).get();
	}
}
