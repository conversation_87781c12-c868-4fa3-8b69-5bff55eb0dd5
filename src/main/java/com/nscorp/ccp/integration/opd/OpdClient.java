package com.nscorp.ccp.integration.opd;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.opd.OpdScenOrPlanSa;

import java.time.LocalDate;

public interface OpdClient {
	Iterable<JsonOpdScenario> getOpdScenarios();
	Iterable<JsonOpdPlan> getOpdPlans();
	Iterable<JsonOpdTrain> getOpdTrains(OpdScenOrPlanSa sa, LocalDate startDt, LocalDate endDt);
}
