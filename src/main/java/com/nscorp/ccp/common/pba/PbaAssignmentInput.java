package com.nscorp.ccp.common.pba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nscorp.ccp.utils.commonTypes.FullTrainKey;
import com.nscorp.ccp.utils.commonTypes.ProfileKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.jetbrains.annotations.Nullable;

@Value
@Builder(toBuilder = true)
@JsonPropertyOrder(alphabetic = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class PbaAssignmentInput {
	String fromOs;
	String toOs;
	String trnCrewDistr;
	String trnType;
	@Nullable String profileId;
	@Nullable String crewOrgnOs;

	@JsonSerialize(using = ToStringSerializer.class)
	FullTrainKey fullTrainKey;

	@JsonSerialize(using = ToStringSerializer.class)
	@Nullable public ProfileKey getProfileKey() {
		return (profileId == null || crewOrgnOs == null) ?
		       null :
		       ProfileKey.builder().profileId(profileId).crewOrgnOs(crewOrgnOs).build();
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
