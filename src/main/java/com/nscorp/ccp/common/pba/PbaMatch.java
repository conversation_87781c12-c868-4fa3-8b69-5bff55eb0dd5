package com.nscorp.ccp.common.pba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.utils.commonTypes.FullTrainKey;
import com.nscorp.ccp.utils.commonTypes.ProfileKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class PbaMatch {
	@NonNull PbaRuleCode ruleCode;
	@NonNull PbaLevelKey levelKey;
	@NonNull ProfileKey profileKey;
	int count;

	@JsonSerialize(using = ToStringSerializer.class)
	List<FullTrainKey> trains;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
