package com.nscorp.ccp.common.pba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;
import org.jetbrains.annotations.Nullable;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class PbaLevelKey {
	@Nullable String fromOs;
	@Nullable String toOs;
	@Nullable String trnCrewDistr;
	@Nullable String trnType;

	@Override
	public String toString() {
		return String.format(
				"pbalk[f=%s,t=%s,y=%s,l=%s]",
				fromOs,
				toOs,
				trnType,
				trnCrewDistr);
	}
}
