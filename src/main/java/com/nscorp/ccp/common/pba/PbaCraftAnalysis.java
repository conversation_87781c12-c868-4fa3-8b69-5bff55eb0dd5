package com.nscorp.ccp.common.pba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.common.plan.CrewProfileAnalysisReasonCode;
import com.nscorp.ccp.utils.commonTypes.*;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class PbaCraftAnalysis {
	@JsonSerialize(using = ToStringSerializer.class)
	Set<ExbPoolSetUpKey> optimizableExbPoolSetUps;
	@JsonSerialize(using = ToStringSerializer.class)
	Set<ExbPoolSetUpKey> nonEmptyExbPoolSetUps;
	CrewProfileAnalysisReasonCode reasonCode;
	Craft craft;
	@JsonSerialize(using = ToStringSerializer.class)
	ProfileKey profileKey;
	@JsonSerialize(using = ToStringSerializer.class)
	ProfileKeyWithCraft profileKeyWithCraft;
	@JsonSerialize(using = ToStringSerializer.class)
	Set<ExbPoolSetUpKey> protectingExbPoolSetUpKeys;
	@JsonSerialize(using = ToStringSerializer.class)
	Set<ExbTurnKey> protectingExbTurnKeys;
	@JsonSerialize(using = ToStringSerializer.class)
	PoolKey poolKey;
	@JsonSerialize(using = ToStringSerializer.class)
	Set<TurnKey> turnKeys;
	int numPoolTurns;
	int numExbPoolTurns;
	boolean poolOptimizeSize;
	Integer poolTurnRangeLow;
	Integer poolTurnRangeHi;
	boolean poolOptimizable;

	public boolean isUseable() {
		return reasonCode.isGood();
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
