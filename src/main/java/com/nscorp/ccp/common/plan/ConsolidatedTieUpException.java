package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.commonTypes.Craft;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ConsolidatedTieUpException {
	String distr;
	String subDistr;
	Craft craft;
	Integer tueSeqNbr;

	String fromDistr;
	String fromSubDistr;
	String fromPoolName;
	String toDistr;
	String toSubDistr;
	String toPoolName;
	Integer toPoolHomeAway;
	Integer tueSubSeqNbr;

	String sp1Distr;
	String sp1SubDistr;
	String sp1PoolName;
	String sp2Distr;
	String sp2SubDistr;
	String sp2PoolName;
	String sp3Distr;
	String sp3SubDistr;
	String sp3PoolName;
	String sp4Distr;
	String sp4SubDistr;
	String sp4PoolName;
}
