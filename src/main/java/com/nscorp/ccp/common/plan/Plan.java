package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@Table("PLAN")
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class Plan {
	@With String planDescription;
	@Nullable @With @Id Long planId;
	@NonNull
	@With String planName;
	@NonNull
	@With
	PlanStatus status;
	@NonNull
	@With String userId;

	@With Long copiedFrom;
	
	Instant createTs;
	@Nullable String errorMessage;
	
	@Nullable Instant updateTs;
	@Nullable String updateUserId;
	@With Long crewproGroupId;
	PlanType planType;
	@With Boolean locked;

	public static Plan defaultPlan(String userId, String planName, String planDescription) {
		return Plan.builder().
				userId(userId).
				planName(planName).
				planDescription(planDescription).
				planType(PlanType.CCP).
				status(PlanStatus.COMPLETE).
				locked(false).
				build();
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
