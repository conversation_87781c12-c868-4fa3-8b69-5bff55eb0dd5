package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
@JsonPropertyOrder(alphabetic = true)
public class PoolInfo {
	Long poolId;
	String distr;
	String subDistr;
	String poolName;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
