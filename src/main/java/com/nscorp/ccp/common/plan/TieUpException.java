package com.nscorp.ccp.common.plan;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.utils.commonTypes.Craft;
import com.nscorp.ccp.utils.commonTypes.TueKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.MappedCollection;
import org.springframework.data.relational.core.mapping.Table;

import java.util.Optional;
import java.util.Set;

import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.google.common.collect.Streams.stream;

@Value
@Builder(toBuilder = true)
@Table("TIE_UP_EXCEPTION")
@JsonPropertyOrder(alphabetic = true)
@Slf4j
public class TieUpException {
	@PersistenceCreator
	public TieUpException(Long planId, Long tieUpExceptionId, Craft craft, String description, int tueSeqNbr, String subDistr, String distr, Iterable<TueSubrule> tueSubrules) {
		this.planId = planId;
		this.tieUpExceptionId = tieUpExceptionId;
		this.craft = craft;
		this.description = description;
		this.tueSeqNbr = tueSeqNbr;
		this.subDistr = subDistr;
		this.distr = distr;
		val tueKey = getTueKey();
		val myTueSubrules = Optional.ofNullable(tueSubrules).orElse(ImmutableSet.of());
		this.tueSubrules = stream(myTueSubrules).map(t -> t.withTueKey(tueKey)).collect(toImmutableSet());
		this.hubId = null; // <-- transient
	}

	public TieUpException(Long planId, Long hubId, Long tieUpExceptionId, Craft craft, String description, int tueSeqNbr, String subDistr, String distr, Iterable<TueSubrule> tueSubrules) {
		this.planId = planId;
		this.hubId = hubId;
		this.tieUpExceptionId = tieUpExceptionId;
		this.craft = craft;
		this.description = description;
		this.tueSeqNbr = tueSeqNbr;
		this.subDistr = subDistr;
		this.distr = distr;
		val tueKey = getTueKey();
		val myTueSubrules = Optional.ofNullable(tueSubrules).orElse(ImmutableSet.of());
		this.tueSubrules = stream(myTueSubrules).map(t -> t.withTueKey(tueKey)).collect(toImmutableSet());
	}

	@JsonSerialize(using = ToStringSerializer.class)
    @Transient
	public TueKey getTueKey() {
		return distr == null || subDistr == null || craft == null ? null : TueKey.builder().
			distr(distr).
			subDistr(subDistr).
			craft(craft).
			seqNbr(tueSeqNbr).
			build();
	}

	@With Long planId;
	//CREWMAX-165: @With Long scenarioId;
	@With @Transient Long hubId;
	@With @Id Long tieUpExceptionId;

	Craft craft;
	String description;
	int tueSeqNbr;
	String subDistr;
	String distr;

	@MappedCollection(idColumn = "TIE_UP_EXCEPTION_ID")
	@With Set<TueSubrule> tueSubrules;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
