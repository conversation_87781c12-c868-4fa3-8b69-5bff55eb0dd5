package com.nscorp.ccp.common.plan.export;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@JsonPropertyOrder(alphabetic = true)
public class ExpPoolSetUpTurn {
    String distr;
    String subDistr;
    String poolName;
    String turnId;
    String craft;
    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
