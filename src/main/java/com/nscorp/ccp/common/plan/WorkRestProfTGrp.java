package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.commonTypes.Craft;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@Table("WORK_REST_PROF_TGRP")
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class WorkRestProfTGrp {
	@With @Id @Column("WORK_REST_PROF_TGRP_ID") Long workRestProfTGrpId;
	String turnId;
	Craft craft;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
