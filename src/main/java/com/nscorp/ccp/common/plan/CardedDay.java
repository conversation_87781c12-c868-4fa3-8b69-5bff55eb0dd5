package com.nscorp.ccp.common.plan;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Value
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@Table("CARDED_DAY")
@Builder(toBuilder = true)
public class CardedDay {
	@With @Id @Column("CARDED_DAY_ID") Long cardedDayId;
	Integer dayOfCycle;
	String turnName;
	String locPoolName;
	String locSubDistr;
	Integer locHomeAway;
	String locDistr;
	String homeSubDistr;
	String homePoolName;
	String homeDistr;
}
