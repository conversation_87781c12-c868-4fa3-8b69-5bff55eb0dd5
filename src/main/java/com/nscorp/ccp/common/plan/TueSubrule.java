package com.nscorp.ccp.common.plan;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nscorp.ccp.utils.commonTypes.*;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.util.List;

@Value
@Builder(toBuilder = true)
@Table("TUE_SUBRULE")
@JsonPropertyOrder(alphabetic = true)
public class TueSubrule {
	@PersistenceCreator
	public TueSubrule(Long tueSubruleId, String toPoolName, String description, Integer toPoolHomeAway, String fromDistr, String fromPoolName, String toSubDistr, String fromSubDistr, int tueSubSeqNbr, String toDistr,
			List<SearchPool> searchPools) {
		this.tueSubruleId = tueSubruleId;
		this.toPoolName = toPoolName;
		this.description = description;
		this.toPoolHomeAway = toPoolHomeAway;
		this.fromDistr = fromDistr;
		this.fromPoolName = fromPoolName;
		this.toSubDistr = toSubDistr;
		this.fromSubDistr = fromSubDistr;
		this.tueSubSeqNbr = tueSubSeqNbr;
		this.toDistr = toDistr;
		this.searchPools = searchPools;
		this.tueKey = null; // <-- transient
	}

	public TueSubrule(TueKey tueKey, Long tueSubruleId, String toPoolName, String description, Integer toPoolHomeAway, String fromDistr, String fromPoolName, String toSubDistr, String fromSubDistr, int tueSubSeqNbr, String toDistr,
			List<SearchPool> searchPools) {
		this.tueKey = tueKey;
		this.tueSubruleId = tueSubruleId;
		this.toPoolName = toPoolName;
		this.description = description;
		this.toPoolHomeAway = toPoolHomeAway;
		this.fromDistr = fromDistr;
		this.fromPoolName = fromPoolName;
		this.toSubDistr = toSubDistr;
		this.fromSubDistr = fromSubDistr;
		this.tueSubSeqNbr = tueSubSeqNbr;
		this.toDistr = toDistr;
		this.searchPools = searchPools;
	}

	@Transient
	@With TueKey tueKey;
	@With @Id @Column("TUE_SUBRULE_ID") Long tueSubruleId;

	String toPoolName;
	String description;
	Integer toPoolHomeAway;
	String fromDistr;
	String fromPoolName;
	String toSubDistr;
	String fromSubDistr;
	int tueSubSeqNbr;
	String toDistr;

	@MappedCollection(idColumn = "TUE_SUBRULE_ID", keyColumn = "SEQ_NBR")
	@With
	List<SearchPool> searchPools;

	@JsonSerialize(using = ToStringSerializer.class)
    @Transient
	public TueSubruleKey getSubruleKey() {
		return tueKey.toSubruleKey(tueSubSeqNbr);
	}

	@JsonSerialize(using = ToStringSerializer.class)
    @Transient
	@Nullable public PoolKey getFromPoolKey() {
		return fromDistr == null || fromSubDistr ==  null || fromPoolName == null ? null :
		       PoolKey.builder().
				       distr(fromDistr).
				       subDistr(fromSubDistr).
				       poolName(fromPoolName).
				       build();
	}

	@JsonSerialize(using = ToStringSerializer.class)
    @Transient
	@Nullable public PoolKey getToPoolKey() {
		return toDistr == null || toSubDistr ==  null || toPoolName == null ? null :
		       PoolKey.builder().
				       distr(toDistr).
				       subDistr(toSubDistr).
				       poolName(toPoolName).
				       build();
	}

	@JsonSerialize(using = ToStringSerializer.class)
    @Transient
	@Nullable public PoolSetUpKey getToPoolSetUpKey() {
		val toKey = getToPoolKey();
		return toKey == null || toPoolHomeAway == null ? null :
		       toKey.toPsuKey(toPoolHomeAway);
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
