package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class CardedJobDetail {
    Long cardedDayId;
    Integer dayOfCycle;
    String homeDistr;
    String homeSubDistr;
    String homePoolName;
    String turnName;
    String locDistr;
    String locPoolName;
    String locHomeAway;
}
