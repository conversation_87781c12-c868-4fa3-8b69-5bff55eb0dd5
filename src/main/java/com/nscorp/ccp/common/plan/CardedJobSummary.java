package com.nscorp.ccp.common.plan;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import lombok.*;

import java.time.LocalTime;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class CardedJobSummary {
    Long planId;
    Long cardedPoolId;
    String distr;
    String subDistr;
    String poolName;
    String craft;
    Integer poolHomeAway;
    String cardedTrain;
    String useScheduleForTrain;
    Integer cycleDays;
    LocalTime startTime;
    LocalTime endTime;
    String altDistr;
    String altSubDistr;
    String altPoolName;
    String altPoolHomeAway;
}
