package com.nscorp.ccp.common.study;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class StudyHub {
	long studyId;
	Long hubId;
	@NonNull Set<Train> trains;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
