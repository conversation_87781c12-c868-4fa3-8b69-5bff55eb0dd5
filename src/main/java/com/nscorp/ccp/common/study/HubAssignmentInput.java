package com.nscorp.ccp.common.study;
import java.util.*;
import lombok.val;

import com.nscorp.ccp.utils.commonTypes.*;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class HubAssignmentInput {
	FullTrainKey fullTrainKey;
	Integer assignFlag;
	ExbPoolSetUpKey coExbKey;
	ExbPoolSetUpKey enExbKey;
	String profileId;
	String crewOrgnOs;
	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
