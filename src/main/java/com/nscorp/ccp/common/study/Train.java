package com.nscorp.ccp.common.study;
import static org.apache.commons.lang3.StringUtils.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nscorp.ccp.common.costCtr.CostCtrSource;
import com.nscorp.ccp.common.hireGrp.HireGrpSource;
import com.nscorp.ccp.common.crewProfile.SbaAssignmentReasonCode;
import com.nscorp.ccp.logic.scenario.HubAssignmentReasonCode;
import com.nscorp.ccp.common.upside.TransitTimeSource;
import com.nscorp.ccp.utils.commonTypes.ExbPoolSetUpKey;
import com.nscorp.ccp.utils.commonTypes.ExbKey;
import com.nscorp.ccp.utils.commonTypes.FullTrainKey;
import com.nscorp.ccp.utils.commonTypes.ProfileKey;
import com.nscorp.ccp.utils.commonTypes.TrainKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Embedded;
import org.springframework.data.relational.core.mapping.Table;

import java.time.Instant;
import java.time.LocalDate;

@Value
@Builder(toBuilder = true)
@Table("TRAIN")
@JsonPropertyOrder(alphabetic = true)
public class Train {
	@PersistenceCreator
	public Train(String profMatchCode, Long trainId, Long studyId, String hub, LocalDate effDt, LocalDate expDt, Instant fromTs,
			String assignEnXbSubDistr, String assignCoXbDistr, String toOs, String crewOrgnOs, String assignCoXbExb, Integer fromLocSeqNr, String trnType, Integer assignFlag, String fromOs, String assignEnXbDistr, String assignCoXbSubDistr,
			Instant toTs, Integer initTerminalTime, Integer toLocSeqNr, String trn, String trnSymb, TrainSource trnSource, LocalDate trnOrgnDt, String trnCrewDistr, int segTransitTime, String profileId, String assignEnXbExb, String trnSection,
			String rte, HubAssignmentReasonCode hubAssignmentReasonCode, SbaAssignmentReasonCode profAssignmentReasonCode, String tsrCrewStn, DepartureDistributionParams departureDistributionParams,
			TransitDistributionParams transitDistributionParams, String comments, Integer costCtr, String costCtrDesc, String hireGrp, String hireGrpDesc, Integer enCnt, Integer trCnt, Boolean skipSimulation,
			SkipSimulationReason skipSimulationReason, CostCtrSource costCtrSource, HireGrpSource hireGrpSource, Long upsideTrainId) {
		this.profMatchCode = profMatchCode;
		this.trainId = trainId;
		this.studyId = studyId;
		this.hub = hub;
		this.effDt = effDt;
		this.expDt = expDt;
		this.fromTs = fromTs;
		this.assignEnXbSubDistr = assignEnXbSubDistr;
		this.assignCoXbDistr = assignCoXbDistr;
		this.toOs = toOs;
		this.crewOrgnOs = crewOrgnOs;
		this.assignCoXbExb = assignCoXbExb;
		this.fromLocSeqNr = fromLocSeqNr;
		this.trnType = trnType;
		this.assignFlag = assignFlag;
		this.fromOs = fromOs;
		this.assignEnXbDistr = assignEnXbDistr;
		this.assignCoXbSubDistr = assignCoXbSubDistr;
		this.toTs = toTs;
		this.initTerminalTime = initTerminalTime;
		this.toLocSeqNr = toLocSeqNr;
		this.trn = trn;
		this.trnSymb = trnSymb;
		this.trnSource = trnSource;
		this.trnOrgnDt = trnOrgnDt;
		this.trnCrewDistr = trnCrewDistr;
		this.segTransitTime = segTransitTime;
		this.profileId = profileId;
		this.assignEnXbExb = assignEnXbExb;
		this.trnSection = trnSection;
		this.rte = rte;
		this.hubAssignmentReasonCode = hubAssignmentReasonCode;
		this.profAssignmentReasonCode = profAssignmentReasonCode;
		this.tsrCrewStn = tsrCrewStn;
		this.departureDistributionParams = departureDistributionParams;
		this.transitDistributionParams = transitDistributionParams;
		this.comments = comments;
		this.costCtr = costCtr;
		this.costCtrDesc = costCtrDesc;
		this.hireGrp = hireGrp;
		this.hireGrpDesc = hireGrpDesc;
		this.enCnt = enCnt;
		this.trCnt = trCnt;
		this.skipSimulation = skipSimulation;
		this.skipSimulationReason = skipSimulationReason;
		this.costCtrSource = costCtrSource;
		this.hireGrpSource = hireGrpSource;
		this.upsideTrainId = upsideTrainId;
		this.origCrewOrgnOs = null; // <-- transient field
		this.origProfileId = null; // <-- transient field
		this.origProfMatchCode = null; // <-- transient field
		this.scenarioId = null; // <-- transient field
		this.hubId = null; // <-- transient field
		this.transitTimeSource = null; // <-- transient field
	}
	public Train(String origCrewOrgnOs, String origProfileId, String origProfMatchCode, String profMatchCode, Long trainId, Long studyId, Long scenarioId, String hub, Long hubId, LocalDate effDt, LocalDate expDt, Instant fromTs,
			String assignEnXbSubDistr, String assignCoXbDistr, String toOs, String crewOrgnOs, String assignCoXbExb, Integer fromLocSeqNr, String trnType, Integer assignFlag, String fromOs, String assignEnXbDistr, String assignCoXbSubDistr,
			Instant toTs, Integer initTerminalTime, Integer toLocSeqNr, String trn, String trnSymb, TrainSource trnSource, LocalDate trnOrgnDt, String trnCrewDistr, int segTransitTime, String profileId, String assignEnXbExb, String trnSection,
			String rte, HubAssignmentReasonCode hubAssignmentReasonCode, SbaAssignmentReasonCode profAssignmentReasonCode, String tsrCrewStn, DepartureDistributionParams departureDistributionParams,
			TransitDistributionParams transitDistributionParams, String comments, Integer costCtr, String costCtrDesc, String hireGrp, String hireGrpDesc, Integer enCnt, Integer trCnt, Boolean skipSimulation,
			SkipSimulationReason skipSimulationReason, CostCtrSource costCtrSource, HireGrpSource hireGrpSource, Long upsideTrainId, TransitTimeSource transitTimeSource) {
		this.origCrewOrgnOs = origCrewOrgnOs;
		this.origProfileId = origProfileId;
		this.origProfMatchCode = origProfMatchCode;
		this.profMatchCode = profMatchCode;
		this.trainId = trainId;
		this.studyId = studyId;
		this.scenarioId = scenarioId;
		this.hub = hub;
		this.hubId = hubId;
		this.effDt = effDt;
		this.expDt = expDt;
		this.fromTs = fromTs;
		this.assignEnXbSubDistr = assignEnXbSubDistr;
		this.assignCoXbDistr = assignCoXbDistr;
		this.toOs = toOs;
		this.crewOrgnOs = crewOrgnOs;
		this.assignCoXbExb = assignCoXbExb;
		this.fromLocSeqNr = fromLocSeqNr;
		this.trnType = trnType;
		this.assignFlag = assignFlag;
		this.fromOs = fromOs;
		this.assignEnXbDistr = assignEnXbDistr;
		this.assignCoXbSubDistr = assignCoXbSubDistr;
		this.toTs = toTs;
		this.initTerminalTime = initTerminalTime;
		this.toLocSeqNr = toLocSeqNr;
		this.trn = trn;
		this.trnSymb = trnSymb;
		this.trnSource = trnSource;
		this.trnOrgnDt = trnOrgnDt;
		this.trnCrewDistr = trnCrewDistr;
		this.segTransitTime = segTransitTime;
		this.profileId = profileId;
		this.assignEnXbExb = assignEnXbExb;
		this.trnSection = trnSection;
		this.rte = rte;
		this.hubAssignmentReasonCode = hubAssignmentReasonCode;
		this.profAssignmentReasonCode = profAssignmentReasonCode;
		this.tsrCrewStn = tsrCrewStn;
		this.departureDistributionParams = departureDistributionParams;
		this.transitDistributionParams = transitDistributionParams;
		this.comments = comments;
		this.costCtr = costCtr;
		this.costCtrDesc = costCtrDesc;
		this.hireGrp = hireGrp;
		this.hireGrpDesc = hireGrpDesc;
		this.enCnt = enCnt;
		this.trCnt = trCnt;
		this.skipSimulation = skipSimulation;
		this.skipSimulationReason = skipSimulationReason;
		this.costCtrSource = costCtrSource;
		this.hireGrpSource = hireGrpSource;
		this.upsideTrainId = upsideTrainId;
		this.transitTimeSource = transitTimeSource;
	}
	@Transient String origCrewOrgnOs;
	@Transient String origProfileId;
	@Transient String origProfMatchCode;
	@With String profMatchCode;
	@With @Id Long trainId;
	@With Long studyId;
	@With @Transient Long scenarioId;
	@With String hub;

	@With @Transient Long hubId;

	LocalDate effDt;

	LocalDate expDt;

	Instant fromTs;
	String assignEnXbSubDistr;
	String assignCoXbDistr;
	String toOs;
	@With String crewOrgnOs;
	String assignCoXbExb;
	Integer fromLocSeqNr;
	String trnType;
	Integer assignFlag;
	String fromOs;
	String assignEnXbDistr;
	String assignCoXbSubDistr;

	@With Instant toTs;
	@With Integer initTerminalTime;
	Integer toLocSeqNr;
	String trn;
	String trnSymb;
	TrainSource trnSource;

	LocalDate trnOrgnDt;
	String trnCrewDistr;
	@With int segTransitTime;
	@With String profileId;
	String assignEnXbExb;
	String trnSection;
	String rte;
	@With HubAssignmentReasonCode hubAssignmentReasonCode;
	@With SbaAssignmentReasonCode profAssignmentReasonCode;
	@With String tsrCrewStn;
	@Embedded(onEmpty = Embedded.OnEmpty.USE_EMPTY)
	@With DepartureDistributionParams departureDistributionParams;
	@Embedded(onEmpty = Embedded.OnEmpty.USE_EMPTY)
	@With TransitDistributionParams transitDistributionParams;
	@With String comments;
	Integer costCtr;
	String costCtrDesc;
	String hireGrp;
	@With String hireGrpDesc;
	Integer enCnt;
	Integer trCnt;
	@With Boolean skipSimulation;
	SkipSimulationReason skipSimulationReason;
	CostCtrSource costCtrSource;
	HireGrpSource hireGrpSource;
	Long upsideTrainId;
	@Transient TransitTimeSource transitTimeSource;

	public boolean isAssignFlag() {
		return getAssignFlag() != null && getAssignFlag().intValue() == 1;
	}

	@JsonIgnore
	public boolean isCrewProfileUsed() {
		return ! isAssignFlag();
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public ExbPoolSetUpKey getCoExbKey() {
		if ( getAssignCoXbDistr() != null &&
		     getAssignCoXbSubDistr() != null &&
		     getAssignCoXbExb() != null ) {
			return ExbPoolSetUpKey.builder().distr(getAssignCoXbDistr()).subDistr(getAssignCoXbSubDistr()).poolName(getAssignCoXbExb()).build();
		}
		return null;
	}
	@JsonSerialize(using = ToStringSerializer.class)
	public ExbPoolSetUpKey getEnExbKey() {
		if ( getAssignEnXbDistr() != null &&
		     getAssignEnXbSubDistr() != null &&
		     getAssignEnXbExb() != null ) {
			return ExbPoolSetUpKey.builder().distr(getAssignEnXbDistr()).subDistr(getAssignEnXbSubDistr()).poolName(getAssignEnXbExb()).build();
		}
		return null;
	}
	@JsonSerialize(using = ToStringSerializer.class)
	public TrainKey getTrainKey() {
		return TrainKey.builder().trnNr(getTrn()).trnOrgnDt(getTrnOrgnDt()).build();
	}
	@JsonSerialize(using = ToStringSerializer.class)
	public ProfileKey getProfileKey() {
		if ( getProfileId() == null || getCrewOrgnOs() == null ) {
			return null;
		}
		return ProfileKey.builder().profileId(getProfileId()).crewOrgnOs(getCrewOrgnOs()).build();
	}
	@JsonSerialize(using = ToStringSerializer.class)
	public FullTrainKey getFullTrainKey() {
		return FullTrainKey.builder().
				trnNr(trn).
				trnOrgnDt(trnOrgnDt).
				fromLocSeqNr(fromLocSeqNr).
				trnSection(trnSection).
				rte(rte).
				build();
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public ExbKey getAssignCoKey() {
		final ExbKey result;
		if ( isAssignFlag() &&
			isNotEmpty(getAssignCoXbDistr()) &&
			isNotEmpty(getAssignCoXbSubDistr()) &&
			isNotEmpty(getAssignCoXbExb() ) ) {
			result = new ExbKey(getAssignCoXbDistr(), getAssignCoXbSubDistr(), getAssignCoXbExb());
		}
		else {
			result = null;
		}
		return result;
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public ExbKey getAssignEnKey() {
		final ExbKey result;
		if (isAssignFlag() &&
		    isNotEmpty(getAssignEnXbDistr()) &&
		    isNotEmpty(getAssignEnXbSubDistr()) &&
		    isNotEmpty(getAssignEnXbExb() ) ) {
			result = new ExbKey(getAssignEnXbDistr(), getAssignEnXbSubDistr(), getAssignEnXbExb());
		}
		else {
			result = null;
		}
		return result;
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
