package com.nscorp.ccp.common.opd;
import java.util.*;
import lombok.val;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;

import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Table;

@Value
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@Table("OPD_SCENARIO")
@JsonPropertyOrder(alphabetic = true)
public class OpdScenario {
	@PersistenceCreator
	public OpdScenario(Long opdScenarioId, String scenarioName, Integer scenSa, String planName, Integer planSa, String studyName, Integer studySa, LocalDate studyStartDt, LocalDate studyEndDt, LocalDate targetStartDt, LocalDate targetEndDt) {
		this.opdScenarioId = opdScenarioId;
		this.scenarioName = scenarioName;
		this.scenSa = scenSa;
		this.planName = planName;
		this.planSa = planSa;
		this.studyName = studyName;
		this.studySa = studySa;
		this.studyStartDt = studyStartDt;
		this.studyEndDt = studyEndDt;
		this.targetStartDt = targetStartDt;
		this.targetEndDt = targetEndDt;
		this.firstMileCd = null; // <-- transient
	}

	public OpdScenario(Long opdScenarioId, String scenarioName, Integer scenSa, String planName, Integer planSa, String studyName, Integer studySa, LocalDate studyStartDt, LocalDate studyEndDt, LocalDate targetStartDt, LocalDate targetEndDt,
			String firstMileCd) {
		this.opdScenarioId = opdScenarioId;
		this.scenarioName = scenarioName;
		this.scenSa = scenSa;
		this.planName = planName;
		this.planSa = planSa;
		this.studyName = studyName;
		this.studySa = studySa;
		this.studyStartDt = studyStartDt;
		this.studyEndDt = studyEndDt;
		this.targetStartDt = targetStartDt;
		this.targetEndDt = targetEndDt;
		this.firstMileCd = firstMileCd;
	}

	@With @Id Long opdScenarioId;
	String scenarioName;
	@With Integer scenSa;
	String planName;
	Integer planSa;
	String studyName;
	Integer studySa;
	LocalDate studyStartDt;
	LocalDate studyEndDt;
	LocalDate targetStartDt;
	LocalDate targetEndDt;
	@Transient
	String firstMileCd;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}

