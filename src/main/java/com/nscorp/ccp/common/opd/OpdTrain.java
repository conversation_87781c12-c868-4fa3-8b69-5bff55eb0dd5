package com.nscorp.ccp.common.opd;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.extern.jackson.Jacksonized;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.integration.opd.JsonOpdTrain;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.time.LocalDate;

import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Table;

@Value
@FieldDefaults(level = AccessLevel.PRIVATE)
@Jacksonized
@Builder(toBuilder = true)
@Table("OPD_TRAIN")
@JsonPropertyOrder(alphabetic = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpdTrain {
	@PersistenceCreator
	public OpdTrain(Long opdTrainId, Integer scenSa, Integer planSa, String trnSa, String symb, String rte, String typ, LocalDate trnOrgnDt, Integer section, String lnSeg, Integer fromLocSeqNr, String fromOs, String fromRd, Instant fromTs,
			Integer toLocSeqNr, String toOs, String toRdm, Instant toTs, Integer segTransitTime, Integer initTerminalTime, Integer costCtr, String costCtrDesc, String hireGrp, String hireGrpDesc, Integer enCnt,
			Integer trCnt) {
		this.opdTrainId = opdTrainId;
		this.scenSa = scenSa;
		this.planSa = planSa;
		this.trnSa = trnSa;
		this.symb = symb;
		this.rte = rte;
		this.typ = typ;
		this.trnOrgnDt = trnOrgnDt;
		this.section = section;
		this.lnSeg = lnSeg;
		this.fromLocSeqNr = fromLocSeqNr;
		this.fromOs = fromOs;
		this.fromRd = fromRd;
		this.fromTs = fromTs;
		this.toLocSeqNr = toLocSeqNr;
		this.toOs = toOs;
		this.toRdm = toRdm;
		this.toTs = toTs;
		this.segTransitTime = segTransitTime;
		this.initTerminalTime = initTerminalTime;
		this.costCtr = costCtr;
		this.costCtrDesc = costCtrDesc;
		this.hireGrp = hireGrp;
		this.hireGrpDesc = hireGrpDesc;
		this.enCnt = enCnt;
		this.trCnt = trCnt;
		this.trnEffDt = null; // <-- transient
		this.trnExpDt = null; // <-- transient
	}

	public OpdTrain(Long opdTrainId, Integer scenSa, Integer planSa, String trnSa, String symb, String rte, String typ, LocalDate trnOrgnDt, Integer section, String lnSeg, Integer fromLocSeqNr, String fromOs, String fromRd, Instant fromTs,
			Integer toLocSeqNr, String toOs, String toRdm, Instant toTs, Integer segTransitTime, Integer initTerminalTime, LocalDate trnEffDt, LocalDate trnExpDt, Integer costCtr, String costCtrDesc, String hireGrp, String hireGrpDesc, Integer enCnt,
			Integer trCnt) {
		this.opdTrainId = opdTrainId;
		this.scenSa = scenSa;
		this.planSa = planSa;
		this.trnSa = trnSa;
		this.symb = symb;
		this.rte = rte;
		this.typ = typ;
		this.trnOrgnDt = trnOrgnDt;
		this.section = section;
		this.lnSeg = lnSeg;
		this.fromLocSeqNr = fromLocSeqNr;
		this.fromOs = fromOs;
		this.fromRd = fromRd;
		this.fromTs = fromTs;
		this.toLocSeqNr = toLocSeqNr;
		this.toOs = toOs;
		this.toRdm = toRdm;
		this.toTs = toTs;
		this.segTransitTime = segTransitTime;
		this.initTerminalTime = initTerminalTime;
		this.trnEffDt = trnEffDt;
		this.trnExpDt = trnExpDt;
		this.costCtr = costCtr;
		this.costCtrDesc = costCtrDesc;
		this.hireGrp = hireGrp;
		this.hireGrpDesc = hireGrpDesc;
		this.enCnt = enCnt;
		this.trCnt = trCnt;
	}

	@With @Id Long opdTrainId;
	@With Integer scenSa;
	@With Integer planSa;
	String trnSa;
	String symb;
	String rte;
	String typ;

	LocalDate trnOrgnDt;
	Integer section;
	String lnSeg;
	Integer fromLocSeqNr;
	String fromOs;
	String fromRd;

	@With Instant fromTs;
	Integer toLocSeqNr;
	String toOs;
	String toRdm;

	@With Instant toTs;
	Integer segTransitTime;
	Integer initTerminalTime;

	@Transient
	LocalDate trnEffDt;

	@Transient LocalDate trnExpDt;
	Integer costCtr;
	String costCtrDesc;
	String hireGrp;
	String hireGrpDesc;
	Integer enCnt;
	Integer trCnt;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
