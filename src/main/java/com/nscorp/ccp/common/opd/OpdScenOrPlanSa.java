package com.nscorp.ccp.common.opd;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;

@ToString
public final class OpdScenOrPlanSa {
	public static enum Which { PLAN, SCEN }
	@Getter private final Integer scenSa;
	@Getter private final Integer planSa;

	public Which which() {
		return planSa != null ? Which.PLAN : Which.SCEN;
	}

	private OpdScenOrPlanSa(Integer scenSa, Integer planSa) {
		// Exactly one of scenSa, planSa should be null.
		Preconditions.checkArgument(scenSa == null || planSa == null);
		Preconditions.checkArgument(scenSa != null || planSa != null);
		this.scenSa = scenSa;
		this.planSa = planSa;
	}

	public static OpdScenOrPlanSa fromScenSa(Integer scenSa) {
		return new OpdScenOrPlanSa(scenSa, null);
	}

	public static OpdScenOrPlanSa fromPlanSa(Integer planSa) {
		return new OpdScenOrPlanSa(null, planSa);
	}
}
