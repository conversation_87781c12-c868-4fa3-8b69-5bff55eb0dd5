package com.nscorp.ccp.common.sba;
import java.util.*;
import lombok.val;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

import java.time.Instant;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class ApcnHistData {
	@With
	String trnType;
	String toOs;
	String fromOs;
	String crewOrgnOs;
	String crewProfileId;
	@With String trainOrgnOs;
	String trnSymb;
	String lineSegment;
	String trnId;
	Instant loadTs;
	Instant onDutyTs;
	@With SbaDataSource dataSource;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
