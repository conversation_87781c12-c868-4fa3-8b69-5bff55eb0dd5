package com.nscorp.ccp.common.sba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class TsrStationControlVO {
	String tsrStation;
	String crewStation;
	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
