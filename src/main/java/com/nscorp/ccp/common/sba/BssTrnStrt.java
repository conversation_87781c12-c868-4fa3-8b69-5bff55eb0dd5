package com.nscorp.ccp.common.sba;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class BssTrnStrt {
	String trnSymb;
	String trnType;
	String fromOs;
	String toOs;
	String lineSegment;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
