package com.nscorp.ccp.common.hireGrp;
import java.util.*;
import lombok.val;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder
@Table("HIRE_GROUP_NAME_MAPPING")
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class HireGroupNameMapping {
	@With @Id Long hireGroupNameMappingId;
	String hireGrpDescFrom;
	String hireGrpDescTo;
	String description;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
