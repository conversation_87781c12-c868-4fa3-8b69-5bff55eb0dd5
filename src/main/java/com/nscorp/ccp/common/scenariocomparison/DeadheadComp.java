package com.nscorp.ccp.common.scenariocomparison;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class DeadheadComp {
    String distr;
    String subDistr;
    String boardName;
    String boardType;
    String craft;
    Double baseCount;
    Double testCount;
    Double countDiff;
}
