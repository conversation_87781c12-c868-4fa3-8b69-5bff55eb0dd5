package com.nscorp.ccp.common.monitor;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.Instant;

@Value
@Builder(toBuilder = true)
@Table("MONITOR_LOG")
public class MonitorLog {
	@With @Id Long monitorLogId;
	Instant createTs;
	String errorMessage;
	MonitorStatus status;
	Instant notificationTs;
}
