package com.nscorp.ccp.common.markoff;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.nscorp.ccp.logic.markoff.SafeLocalDateTimeDeserializer;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
@With
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class MarkoffInputData {
	int empNbr;
	String attendState;
	String attendSubState;
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
	Instant startTs;
	String startFuncCd;
	String startFuncDesc;
	String startSubFuncCd;
	String startSubFuncDesc;
	Instant endStartDeltaDayHourMin;
	int startDOWSunToSat;
	// @JsonDeserialize(using = SafeLocalDateTimeDeserializer.class)
	Instant endTs;
	String endFuncCd;
	String endFuncDesc;
	String endSubFuncCd;
	String endSubFuncDesc;
	String rc;
	String loType;
	String cycleTransInd;
	Instant asgnTs;
	String ds;
	String sd;
	String yardAsgn;
	String yardAsgnSeq;
	String yardAsgnType;
	String trainId;
	String trnCrewDistr;
	String trnOrgnDay;
	String pool;
	String departOs;
	String arvlOs;
	String finalOs;
	String cc;
	Integer stateMins;
	String eahAsgnType;
	String eahXb;
	String eahDs;
	String eahSd;
	String eahPool;
	String eahCc;

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
