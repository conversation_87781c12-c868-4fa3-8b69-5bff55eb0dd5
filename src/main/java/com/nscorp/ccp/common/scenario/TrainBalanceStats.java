package com.nscorp.ccp.common.scenario;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@Table("TRAIN_BALANCE_STATS")
public class TrainBalanceStats {
	@With @Id Long trainBalanceStatsId;
	long scenarioId;
	String crewOrgnOs;
	String trnCrewDistr;
	String trnType;
	int outbound;
	int inbound;
	int difference;
	double outboundPerWeek;
	double inboundPerWeek;
	double differencePerWeek;
}
