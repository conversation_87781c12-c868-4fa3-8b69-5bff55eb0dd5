package com.nscorp.ccp.common.scenario;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

import java.time.Instant;
import java.util.Optional;

import static com.google.common.collect.ImmutableSet.toImmutableSet;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.MappedCollection;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@Table("SCENARIO")
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class Scenario {
	@With @Id Long scenarioId;
	@NonNull  Long planId;
	@NonNull  Long studyId;
	@NonNull  String scenName;
	String description;
	@NonNull  String userId;
	
	@With Instant createTs;
	@With String errorMessage;
	
	@With Instant updateTs;
	String updateUserId;
	@With ScenarioStatus status;
	@With Long crewproGroupId;
	ScenarioType scenarioType;

	@MappedCollection(idColumn = "SCENARIO_ID")
	@With ScenarioCfg scenarioCfg;

    @JsonIgnore // <-- ignore in favor of getHubsAsString()
	@MappedCollection(idColumn = "SCENARIO_ID")
	@With Set<ScenHub> hubs;

    @Transient
	public Set<Long> getHubIds() {
		return Optional.ofNullable(hubs).
				map( h->h.stream().map(ScenHub::getHubId).collect(toImmutableSet()) ).
				orElse(ImmutableSet.of());
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
