package com.nscorp.ccp.common.scenario;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class TrainBalance {
    String crewOs;
    String lineSegment;
    String trainType;
    Double inbound;
    Double outbound;
    Double difference;

    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
