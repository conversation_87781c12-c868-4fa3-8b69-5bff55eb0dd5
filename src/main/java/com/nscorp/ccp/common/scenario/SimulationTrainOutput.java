package com.nscorp.ccp.common.scenario;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nscorp.ccp.utils.commonTypes.FullTrainKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Table;

@Value
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
@Table("SIMULATION_TRAIN_OUTPUT")
@JsonPropertyOrder(alphabetic = true)
public class SimulationTrainOutput {

	@With Long scenarioId;
    @With Long hubId;
	@With @Id Long simulationTrainOutputId;

	String crewOs;
	String enProfileSubDistr;

	Instant enOnDutyStartTime;
	Instant fromTs;
	Instant coOnDutyStartTime;
	Instant toTs;

	String toOs;
	Double delayDueToCrewDuration;
	String enProfilePoolOs;
	Integer enProfilePoolHomeAway;
	Double enOnDutyDuration;
	Integer fromLocSeqNr;
	String trnType;
	String coProfilePoolName;

	String coProfileSubDistr;
	String coProfilePoolOs;
	String fromOs;
	String enProfileDistr;
	Double coOnDutyDuration;
	String crewProfileId;

	Integer coProfilePoolHomeAway;
	Double recrewDuration;
	String trn;
	String trnOrgnDt;
	String trnCrewDistr;
	Integer arvlFlag;
	String coProfileDistr;
	String enProfilePoolName;
	String trnSection;
	String rte;
	String status;

    @Transient
	public LocalDate getTrnOrgnDtAsLocalDate() {
		return Optional.ofNullable(trnOrgnDt).
				map(LocalDate::parse).
				orElse(null);
	}

    @Transient
	@JsonSerialize(using = ToStringSerializer.class)
	public FullTrainKey getFullTrainKey() {
		return FullTrainKey.builder().
				trnNr(getTrn()).
				trnOrgnDt(getTrnOrgnDtAsLocalDate()).
				fromLocSeqNr(getFromLocSeqNr()).
				trnSection(getTrnSection()).
				rte(getRte()).
				build();
	}

	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
