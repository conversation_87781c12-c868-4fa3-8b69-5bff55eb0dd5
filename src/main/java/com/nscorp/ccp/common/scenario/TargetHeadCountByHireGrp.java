package com.nscorp.ccp.common.scenario;
import java.util.*;
import lombok.val;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@Table("TARGET_HEADCOUNT_BY_HIRE_GROUP")
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class TargetHeadCountByHireGrp {
	@With @Id Long targetHeadcountId;
    String hireGrpDesc;
    Double historicalProductivityHeadcountPercentile50;
    Double historicalProductivityHeadcountPercentile85;
    Integer roadSimulationOutputHeadcount;
    Integer roadSimulationOutputPeopleStarts;
    Double roadSimulationOutputProductivity;
    Double roadSimulationOutputWeeklyPeopleStarts;
    Long scenarioId;
    Double simulationOutputHeadcount;
    Double simulationOutputHeadcountPercentile85;
    Double spwMean;
    Double spwStdev;
    Double yardLocalSimulationOutputHeadcount;
    Integer yardLocalSimulationOutputPeopleStarts;
    Double yardLocalSimulationOutputWeeklyPeopleStarts;
}
