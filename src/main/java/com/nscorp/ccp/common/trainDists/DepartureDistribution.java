package com.nscorp.ccp.common.trainDists;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;

import java.time.Instant;
import java.time.LocalDate;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@JsonPropertyOrder(alphabetic = true)
public class DepartureDistribution {
	LocalDate processDate;
	String  trnSymbol;
	String  lineSegment;
	String  fromOs;
	Integer dow;
	String  departureDistribution;
	Double  departureShiftConstant;
	Double  departureShape;
	Double  departureRate;
	Double  departureScale;
	Double  departureLamda1;
	Double  departureMean1;
	Double  departureVariance1;
	Double  departureLower;
	Double  departureMedium;
	Double  departureUpper;
	Integer totalCount;
	Instant    updateTs;
	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}
}
