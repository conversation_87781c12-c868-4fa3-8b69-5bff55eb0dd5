package com.nscorp.ccp.common.modelJson;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nscorp.ccp.utils.commonTypes.ExbPoolSetUpKey;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

/**
 * extraboard. Must refer to an existing distr-subDistr-poolName defined in planPools.
 * @see ./doc/crewpro_input_schema.json
 */
@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class ModelJsonPlanExtraboard {
	@JsonIgnore
	public ExbPoolSetUpKey getExbPoolSetUpKey() {
		return ((distr == null) || (subDistr == null) || (exbBoardName == null)) ? null :
		       new ExbPoolSetUpKey(distr, subDistr, exbBoardName);
	}

	/**
	 * Returns the "poolCraft" property.
	 * <p/>
	 * Property description:  Craft EN or CO
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	String poolCraft;

	/**
	 * Returns the "subDistr" property.
	 * <p/>
	 * Property description:  Sub-Division/Sub-District. Must refer to an existing subDistr defined in planPools.
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	String subDistr;

	/**
	 * Returns the "poolHomeAway" property.
	 * <p/>
	 * Property description:  Indicator of Home or Away
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	int poolHomeAway;

	/**
	 * Returns the "exbBoardName" property.
	 * <p/>
	 * Property description:  Extraboard board name
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	String exbBoardName;

	/**
	 * Returns the "distr" property.
	 * <p/>
	 * Property description:  Division/District. Must refer to an existing distr defined in planPools.
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	String distr;

	/**
	 * Returns the "poolName" property.
	 * <p/>
	 * Property description:  Pool name. Must refer to an existing poolName defined in planPools.
	 * <p/>
	 * This property is required.
	 * @see ./doc/crewpro_input_schema.json
	 */
	String poolName;

    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
