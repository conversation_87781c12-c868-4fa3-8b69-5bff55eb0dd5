package com.nscorp.ccp.common.crewpro;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.scenario.ScenarioHub;
import lombok.Builder;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

@Value
@Builder(toBuilder = true)
@Slf4j
public class CrewProLoadResponseResults {
	CrewProResponse crewProResponse;
	ScenarioHub scenarioHub;
}
