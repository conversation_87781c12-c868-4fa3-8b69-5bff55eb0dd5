package com.nscorp.ccp.launcher;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.db.*;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import static org.apache.commons.lang3.StringUtils.defaultString;

@UtilityClass
@Slf4j
public class RunDbInitializer {
	public static void initDb() {
		val jdbcUrl = defaultString(System.getenv("SPRING_SCPM_DATASOURCE_JDBCURL"), "*************************************");
		val username = defaultString(System.getenv("SPRING_SCPM_DATASOURCE_USERNAME"), "sa");
		val password = defaultString(System.getenv("SPRING_SCPM_DATASOURCE_PASSWORD"), "");
		val driverClassName = defaultString(System.getenv("SPRING_SCPM_DATASOURCE_DRIVER_CLASS_NAME"), "org.hsqldb.jdbc.JDBCDriver");
		if ( log.isWarnEnabled() ) {
			warn(log, String.format("jdbcUrl=%s", jdbcUrl));
			warn(log, String.format("username=%s", username));
			warn(log, String.format("driverClassName=%s", driverClassName));
		}
		val dbInitializer = new DbInitializer(
				new DbPopulator(),
				new DataImporter() );
		dbInitializer.initdb(
				jdbcUrl,
				username,
				password,
				driverClassName);
	}
}
