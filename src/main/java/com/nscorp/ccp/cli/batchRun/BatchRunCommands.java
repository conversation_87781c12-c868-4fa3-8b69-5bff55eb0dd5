package com.nscorp.ccp.cli.batchRun;

import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.biz.batchRun.*;
import com.nscorp.ccp.common.batchRun.*;
import com.nscorp.ccp.utils.cli.SpringShellUtils;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.*;

import java.time.Duration;
import java.time.Instant;

@ShellComponent
@RequiredArgsConstructor
@Slf4j
@Profile("shell")
public class BatchRunCommands {
	private final BatchRunManager batchRunManager;
	private final DateTimeProvider dateTimeProvider;

	@ShellMethod(group = CommandGroups.BATCH_RUN, key = "get-recent-batch-runs", value="get recent batch runs")
	public Iterable<BatchRun> getRecentBatchRuns(int days, @ShellOption(defaultValue = ShellOption.NULL) String filename) {
		Instant cutoff = dateTimeProvider.now().minus(Duration.ofDays(days));
		return SpringShellUtils.returnOrWriteToFile(batchRunManager.getRecentBatchRuns(cutoff), filename);
	}

	@ShellMethod(group = CommandGroups.BATCH_RUN, key = "create-batch-run", value="create batch run")
	public BatchRun createBatchRun(Instant now, BatchRunType type,  Instant startTs, Instant endTs, boolean successful) {
		BatchRun batchRun = BatchRun.builder().
				batchRunType(type).
				startTs(now).
				endTs(now).
				successful(successful).
				startTs(startTs).
				endTs(endTs).
				build();
		return batchRunManager.save(batchRun);
	}

	@ShellMethod(group = CommandGroups.BATCH_RUN, key = "get-batch-runs-of-type", value="get batch runs of type")
	public Iterable<BatchRun> getBatchRunsOfType(
			BatchRunType type,
			@ShellOption(defaultValue = ShellOption.NULL) String filename) {
		return SpringShellUtils.returnOrWriteToFile(batchRunManager.getMostRecentRunsOfType(type), filename);
	}

	@ShellMethod(group = CommandGroups.BATCH_RUN, key = "get-most-recent-run-of-type", value="get most recent run of type")
	public BatchRun getMostRecentRunOfType(
			BatchRunType type ) {
		return batchRunManager.getMostRecentRunOfType(type);
	}

	@ShellMethod(group = CommandGroups.BATCH_RUN, key = "get-last-successful-run-of-type", value="get last successful run of type")
	public BatchRun getLastSuccessfulRunOfType( BatchRunType type ) {
		return batchRunManager.getLastSuccessfulRunOfType(type);
	}
}
