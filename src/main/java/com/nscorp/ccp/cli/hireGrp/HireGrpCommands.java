package com.nscorp.ccp.cli.hireGrp;
import com.google.common.collect.Iterables;
import com.nscorp.ccp.logic.plan.HireGrpAssignerInputMapper;
import lombok.val;

import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.common.hireGrp.BoardCraftHireGrpMapping;
import com.nscorp.ccp.common.hireGrp.BoardHireGrpMapping;
import com.nscorp.ccp.common.plan.PlanFetchOptions;
import com.nscorp.ccp.logic.plan.HireGrpAssigner;
import com.nscorp.ccp.biz.hireGrp.*;
import com.nscorp.ccp.biz.plan.PlanManager;
import com.nscorp.ccp.biz.study.StudyManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;

import java.io.File;

@ShellComponent
@Slf4j
@RequiredArgsConstructor
@Profile("shell")
public class HireGrpCommands {
	private final HireGrpAssigner hireGrpAssigner = new HireGrpAssigner();
	private final StudyManager studyManager;
	private final PlanManager planManager;
	private final HireGrpManager hireGrpManager;
	private final HireGrpDownloader hireGrpDownloader;
	private final HireGroupNameMappingImporter hireGroupNameMappingImporter;
	private final HireGrpAssignerInputMapper mapper;

	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "import hire group name mappings from csv file to table")
	public void importHireGroupNameMappings() {
		hireGroupNameMappingImporter.importNameMappings();
	}

	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "get all hire group mappings")
	public void getAllBoardHireGrpMappings(String filename) {
		val hireGrpMappings = hireGrpManager.getAllBoardHireGrpMappings();
		FileUtils.writeStringToFile(new File(filename), hireGrpMappings.toString(), "UTF-8");
	}

	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "assign hire groups to trains")
	public void assignHireGroups(String filename, long studyId, long planId) {
		val plan =planManager.getCompletePlanByPlanId(planId, PlanFetchOptions.ALL);
		val trains = studyManager.getTrainsByStudyId(studyId);
		val hireGrpMappings = hireGrpManager.getAllBoardHireGrpMappings();
		val crewProfiles = plan.getCrewProfiles();
		val inputs = Iterables.transform(trains, mapper::map);
		val trainsWithCostCtr = hireGrpAssigner.assignHireGrp(inputs, crewProfiles, hireGrpMappings);
		FileUtils.writeStringToFile(new File(filename), trainsWithCostCtr.toString(), "UTF-8");
	}

	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "download latest hire groups ")
	public Iterable<BoardHireGrpMapping> downloadBoardHireGrpMappings() {
		return hireGrpDownloader.downloadBoardHireGrpMappings();
	}

	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "download latest hire groups ")
	public Iterable<BoardCraftHireGrpMapping> downloadBoardCraftHireGrpMappings() {
		return hireGrpDownloader.downloadBoardCraftHireGrpMappings();
	}

	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "download and save latest hire groups ")
	public void downloadAndSaveBoardHireGrpMappings() {
		hireGrpDownloader.downloadAndSaveBoardHireGrpMappings();
	}


	@SneakyThrows
	@ShellMethod(group = CommandGroups.HIRE_GRP, value = "download and save board craft hire group mappings")
	public void downloadAndSaveBoardCraftHireGrpMappings() {
		hireGrpDownloader.downloadAndSaveBoardCraftHireGrpMappings();
	}

}
