package com.nscorp.ccp.cli.simulation;
import lombok.val;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Streams;
import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.common.plan.PlanFetchOptions;
import com.nscorp.ccp.biz.plan.PlanManager;
import com.nscorp.ccp.biz.study.StudyManager;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.logic.simulation.TrainHubAssigner;
import com.nscorp.ccp.utils.cli.SpringShellUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.*;

import java.util.Comparator;

import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.Streams.*;

@ShellComponent
@RequiredArgsConstructor
@Slf4j
@Profile("shell")
public class AssignHubs {
	private final TrainHubAssigner hubAssigner = new TrainHubAssigner(); 
	private final PlanManager planManager;
	private final StudyManager studyManager;
	@ShellMethod(group = CommandGroups.JOB, key = "preprocess-simulation", value="preprocess simulation")
	public Iterable<Train> assignHubs(long planId, long studyId, @ShellOption(defaultValue = ShellOption.NULL) String filename) {
		val completePlan = Preconditions.checkNotNull(planManager.getCompletePlanByPlanId(planId, PlanFetchOptions.ALL));

		val completeStudy = Preconditions.checkNotNull(studyManager.getCompleteStudyById(studyId));

		// Assign hubs to the trains.
		val trains = hubAssigner.assignHubs(completeStudy.getTrains(), completePlan.getCrewProfiles(), completePlan.getExbPoolSetUps());

		val comparator = Comparator.comparing(Train::getTrn).thenComparing(Train::getTrnOrgnDt);
		val sortedTrains = stream(trains).
				sorted(comparator).
				collect(toImmutableList());

		return SpringShellUtils.returnOrWriteToFile(sortedTrains, filename);
	}
}
