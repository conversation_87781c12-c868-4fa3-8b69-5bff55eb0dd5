package com.nscorp.ccp.cli.crewpro;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;
import com.nscorp.ieorcommons.collection.*;
import com.nscorp.ieorcommons.guava.*;

import com.nscorp.ccp.biz.scenario.ScenarioStatsPopulator;
import com.nscorp.ccp.biz.crewpro.CrewProResponseLoader;
import com.nscorp.ccp.utils.cli.CommandLineTool;
import com.nscorp.ieorcommons.lang.exception.NSExceptionUtils;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;

@Component
@Slf4j
@RequiredArgsConstructor
@Profile("cli")
public class ImportCrewProResponses implements CommandLineTool {
	private final CrewProResponseLoader responseImporter;
	private final ScenarioStatsPopulator scenarioStatsPopulator;

	@Override public void go(final String[] argv) throws Exception {
		if ( argv.length < 2 ) {
			System.err.println("Usage: scripts/import_response_json.sh <scenarioId> <filename>*");
		}
		val list = imlist(argv);
		val scenarioId = Long.parseLong(list.get(0));
		list.stream().skip(1).
				forEachOrdered(filename-> {
					Try.of(()->FileUtils.readFileToString(new File(filename), "UTF-8")).
							onFailure(e->log.error(String.format("Unable to open file %s (ignored): %s", filename, e))).
							map(str-> responseImporter.loadResponseFromString(scenarioId, str)).
							onSuccess(e->log.warn(String.format("Import of %s succeeded.  Hub ID is %s.", filename, e.getScenarioHub().getHubId()))).
							onFailure(exc->log.warn(String.format("Import of %s failed: %s", filename, NSExceptionUtils.getChainedStackTrace(exc))));
				});
		log.warn(String.format("Populating stats for scenario #%s.", scenarioId));
		scenarioStatsPopulator.populateStats(scenarioId);
		/*
		while ( ai < argv.length ) {
			final String response = FileUtils.readFileToString(new File(argv[ai++]), "UTF-8");
			final CrewProLoadResponseResults results = responseImporter.loadResponseFromString(scenarioId, response);
			warn(log, String.format("results=%s", results));
		}
		*/
	}
}
