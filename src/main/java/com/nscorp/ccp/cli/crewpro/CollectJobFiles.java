package com.nscorp.ccp.cli.crewpro;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.crewpro.CrewProJobFiles;
import com.nscorp.ccp.biz.crewpro.CrewProJobFileCollector;
import com.nscorp.ccp.utils.cli.CommandLineTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;

import static com.google.common.collect.Iterables.transform;

@Component
@Slf4j
@RequiredArgsConstructor
@Profile("cli")
public class CollectJobFiles implements CommandLineTool {
	private final CrewProJobFileCollector collector;

	@Override public void go(final String[] argv) throws Exception {
		final Iterable<CrewProJobFiles> pairs = collector.collectFiles(transform(Arrays.asList(argv), File::new));
		pairs.forEach(pair-> {
			warn(log, String.format("Pair: %s / %s", pair.getRequestFile(), pair.getResponseFile()));
		} );
	}
}
