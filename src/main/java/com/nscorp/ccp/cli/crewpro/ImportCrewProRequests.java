package com.nscorp.ccp.cli.crewpro;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.crewpro.CrewProLoadRequestResults;
import com.nscorp.ccp.biz.crewpro.CrewProRequestLoader;
import com.nscorp.ccp.utils.cli.CommandLineTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;

@Component
@Slf4j
@RequiredArgsConstructor
@Profile("cli")
public class ImportCrewProRequests implements CommandLineTool {
    private final CrewProRequestLoader requestLoader;

    @Override public void go(final String[] argv) throws Exception {
        int ai = 0;
        final long scenarioId = Long.parseLong(argv[ai++]);
        while ( ai < argv.length ) {
            final String request = FileUtils.readFileToString(new File(argv[ai++]), "UTF-8");
            final CrewProLoadRequestResults results = requestLoader.loadRequestFromString(scenarioId, request);
            warn(log, String.format("results=%s", results));
        }
    }
}
