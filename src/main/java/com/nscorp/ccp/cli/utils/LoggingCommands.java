package com.nscorp.ccp.cli.utils;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.Iterables;
import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.utils.cli.SpringShellUtils;
import com.nscorp.ccp.utils.logging.LogLevelAdjuster;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.*;

@ShellComponent
@RequiredArgsConstructor
@Slf4j
@Profile("shell")
public class LoggingCommands {
	private final LogLevelAdjuster logLevelAdjuster;

	@ShellMethod(group = CommandGroups.LOGGING, key = "get-root-log-level", value="get root log level")
	public String getRootLogLevel() {
		return logLevelAdjuster.getLogLevel(LogLevelAdjuster.ROOT_LOGGER);
	}

	@ShellMethod(group = CommandGroups.LOGGING, key = "get-log-level", value="get log level")
	public String getLogLevel(String loggerName) {
		return logLevelAdjuster.getLogLevel(loggerName);
	}

	@ShellMethod(group = CommandGroups.LOGGING, key = "get-loggers", value="get loggers")
	public Iterable<String> getLoggers(@ShellOption(defaultValue = ShellOption.NULL) String prefix) {
		val loggerNames = logLevelAdjuster.getLoggerNames();
		if (! SpringShellUtils.isNull(prefix) ) {
			return Iterables.filter(loggerNames, s -> s.startsWith(prefix));
		}
		return loggerNames;
	}

	@ShellMethod(group = CommandGroups.LOGGING, key = "set-log-level", value="set log level")
	public void setLogLevel(String loggerName, String level) {
		logLevelAdjuster.adjust(loggerName, level);
	}

	@ShellMethod(group = CommandGroups.LOGGING, key = "set-root-log-level", value="set root log level")
	public String setRootLogLevel(String level) {
		return logLevelAdjuster.adjust(LogLevelAdjuster.ROOT_LOGGER, level);
	}
}
