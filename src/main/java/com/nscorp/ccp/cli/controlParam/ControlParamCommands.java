package com.nscorp.ccp.cli.controlParam;
import java.util.*;

import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.biz.controlParam.*;
import com.nscorp.ccp.common.controlParam.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;

@RequiredArgsConstructor
@Slf4j
@ShellComponent
@Profile("shell")
public class ControlParamCommands {
	private final ControlParamManager controlParamManager;
	private final ControlParamWriter controlParamWriter;
	private final ControlParamReader controlParamReader;
	private String toYN(boolean b) {
		return b ? "Y" : "N";
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "set-control-param", value="set a parameter")
	public ControlParam setParam(String key, String value) {
		return controlParamWriter.setParam(key, value);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "get-control-param", value="get a control parameter")
	public String getControlParam(String key) {
		return Optional.ofNullable(controlParamReader.getControlParamByKey(key)).
				map(ControlParam::getParamValue).
				orElse(null);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "lscp", value="get all control parameters")
	public Iterable<ControlParam> getAllControlParams() {
		return controlParamManager.getAllControlParams();
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "delete-control-param", value="delete a control parameter")
	public Long deleteControlParam(String key) {
		return controlParamWriter.deleteControlParamByKey(key);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-scenario-execution", value="scenario execution on/off")
	public ControlParam scenarioExecution(boolean off) {
		return controlParamManager.setParam(ControlParams.SCENARIO_EXECUTION_BATCH_JOB_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-import-crewpro-jobs", value="crewpro job import on/off")
	public ControlParam crewproImport(boolean off) {
		return controlParamManager.setParam(ControlParams.IMPORT_CREWPRO_JOBS_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-run-simulation", value="simulation runner on/off")
	public ControlParam runSimulation(boolean off) {
		return controlParamManager.setParam(ControlParams.RUN_SIMULATION_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-study-cleaner", value="study cleaner on/off")
	public ControlParam studyCleaner(boolean off) {
		return controlParamManager.setParam(ControlParams.STUDY_CLEANER_BATCH_JOB_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-study-creator", value="study creator on/off")
	public ControlParam studyCreator(boolean off) {
		return controlParamManager.setParam(ControlParams.STUDY_CREATOR_BATCH_JOB_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-scenario-cleaner", value="scenario cleaner on/off")
	public ControlParam scenarioCleaner(boolean off) {
		return controlParamManager.setParam(ControlParams.SCENARIO_CLEANER_BATCH_JOB_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-plan-cleaner", value="plan cleaner on/off")
	public ControlParam planCleaner(boolean off) {
		return controlParamManager.setParam(ControlParams.PLAN_CLEANER_BATCH_JOB_DISABLED, toYN(off));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-restart-svc", value="restart svc")
	public ControlParam restartSvc() {
		return controlParamManager.setParam(ControlParams.SVC_EXIT_REQUESTED, toYN(true));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, key = "param-restart-cron", value="restart cron")
	public ControlParam restartCron() {
		return controlParamManager.setParam(ControlParams.CRON_EXIT_REQUESTED, toYN(true));
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="set svc log level")
	public ControlParam setSvcLogLevel(String logLevel) {
		return controlParamManager.setParam(ControlParams.SVC_LOG_LEVEL, logLevel);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="get svc log level")
	public ControlParam getSvcLogLevel() {
		return controlParamManager.getControlParamByKey(ControlParams.SVC_LOG_LEVEL);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="set cron log level")
	public ControlParam setCronLogLevel(String logLevel) {
		return controlParamManager.setParam(ControlParams.CRON_LOG_LEVEL, logLevel);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="get cron log level")
	public ControlParam getCronLogLevel() {
		return controlParamManager.getControlParamByKey(ControlParams.CRON_LOG_LEVEL);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="set runsim log level")
	public ControlParam setRunsimLogLevel(String logLevel) {
		return controlParamManager.setParam(ControlParams.RUNSIM_LOG_LEVEL, logLevel);
	}
	@ShellMethod(group = CommandGroups.CONTROL_PARAM, value="get runsim log level")
	public ControlParam getRunsimLogLevel() {
		return controlParamManager.getControlParamByKey(ControlParams.RUNSIM_LOG_LEVEL);
	}
}
