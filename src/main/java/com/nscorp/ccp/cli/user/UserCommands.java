package com.nscorp.ccp.cli.user;
import lombok.val;

import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.biz.user.*;
import com.nscorp.ccp.common.user.*;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;
import org.springframework.transaction.annotation.Transactional;

@ShellComponent
@Slf4j
@RequiredArgsConstructor
@Profile("shell")
public class UserCommands {
	private final UserManager userManager;
	private final DateTimeProvider dateTimeProvider;

	@ShellMethod(group = CommandGroups.USER, key = "delete-user-by-id", value="delete a user by id")
	public User deleteUserById(String userId) {
		val user = userManager.getUserByUserId(userId);
		if ( user == null ) {
			return null;
		}
		userManager.deleteUser(user.getUserPk());
		return user;
	}

	@ShellMethod(group = CommandGroups.USER, key = "lsusers", value="get all users")
	public Iterable<User> getAllUsers() {
		return userManager.getAllUsers();
	}

	@ShellMethod(group = CommandGroups.USER, key = "init-users", value="init users")
	public void initUsers() {
		userManager.initUsers(true);
	}

	@ShellMethod(group = CommandGroups.USER, key = "get-user-by-id", value="get user by id")
	public User getUser(String userId) {
		return userManager.getUserByUserId(userId);
	}

	@ShellMethod(group = CommandGroups.USER, key = "new-user", value="new user")
	public User newUser(String userId, UserRole role) {
		val user = User.builder().
				userId(userId).
				role(role).
				updateUserId("sys").
				updateTs(dateTimeProvider.now()).
				build();
		return userManager.save(user);
	}

	@ShellMethod(group = CommandGroups.USER, key = "new-user-with-exc", value="new user with exception that should result in rollback")
	@Transactional
	public User newUserWithExc(String userId, UserRole role) {
		val user = User.builder().
				userId(userId).
				role(role).
				updateUserId("sys").
				updateTs(dateTimeProvider.now()).
				build();
		val result = userManager.save(user);
		throw new RuntimeException("ROLLBACK");
	}
}
