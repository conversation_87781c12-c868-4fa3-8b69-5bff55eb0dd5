package com.nscorp.ccp.cli.plan;

import com.nscorp.ccp.biz.plan.PlanManager;
import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ieorcommons.lang.exception.*;
import com.nscorp.ieorcommons.lang.exception.NSExceptionUtils;
import io.vavr.collection.Stream;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.*;

import java.io.File;
import java.util.Optional;

import static com.nscorp.ccp.utils.cli.SpringShellUtils.returnOrWriteToFile;
import static com.nscorp.ccp.utils.logging.LogUtils.warn;

@AllArgsConstructor
@Slf4j
@ShellComponent
@Profile("shell")
public class AnalyzeCrewProHubsCommand {
	private final PlanManager planManager;
	private final HubConsistencyChecker hubConsistencyChecker;

	@ShellMethod(group = CommandGroups.PLAN,  value="do hub analysis for all CrewPro plans")
	public void analyzeAllCrewProPlans(final @ShellOption( help = "Directory to place text files holding analyses of each plan.  Will be created if nonexistent.") String dirName) {
		new File(dirName).mkdirs();

		Stream.ofAll(planManager.getAllPlans()).
				sortBy(Plan::getCreateTs).
				reverse().
				filter(plan->plan.getPlanType() == PlanType.CREWPRO ).
				filter(plan->plan.getStatus() == PlanStatus.COMPLETE).
				forEach(plan-> {
					val planId = plan.getPlanId();
					warn(log, "analyzing {}.", planId);
					val filename = String.format("%s/plan_%d", dirName, planId);
					Try.of(()->analyzeCrewProPlan(planId, filename)).onFailure(NSExceptionUtils::logError);
				});
	}

	@ShellMethod(group = CommandGroups.PLAN,  value="do hub analysis for a CrewPro plan")
	public String analyzeCrewProPlan(
		final long planId,
		final @ShellOption(defaultValue = ShellOption.NULL) String filename ) {
		val cp = planManager.getCompletePlanByPlanId(planId, PlanFetchOptions.ALL);
		return Optional.ofNullable(cp).
				map(completePlan -> returnOrWriteToFile(hubConsistencyChecker.analyzeCrewProPlan(completePlan), filename)).
				orElse(null);
	}
}
