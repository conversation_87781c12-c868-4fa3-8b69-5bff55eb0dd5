package com.nscorp.ccp.cli.study;
import com.google.common.collect.Iterables;
import lombok.val;

import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.biz.study.StudyManager;
import com.nscorp.ccp.biz.trainDists.TrainDistsDownloader;
import com.nscorp.ccp.logic.study.TrainDistsAssigner;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;

import java.io.File;

import static com.google.common.collect.Iterables.filter;
import static com.google.common.collect.Iterables.transform;

@ShellComponent
@RequiredArgsConstructor
@Slf4j
@Profile("shell")
public class AssignTrainDistsCommands {
	private final StudyManager studyManager;
	private final TrainDistsAssigner trainDistsAssigner = new TrainDistsAssigner(); 
	private final TrainDistsDownloader trainDistsDownloader;

	@SneakyThrows @ShellMethod(group = CommandGroups.STUDY, key = "assign-train-dists", value="assign train dists")
	public void assignTrainDists(long studyId, String filename) {
		// Fetch the trains and exclude local trains and yard jobs.
		var trains = Iterables.filter(studyManager.getTrainsByStudyId(studyId), e -> e.getTrnSource() == TrainSource.UPSIDE);

		// Consider only non-historical trains.
		trains = filter(trains, t -> t.getTrnSource() != TrainSource.HISTORY);

		// Clear existing parameters.
		trains = transform(trains, this::excludeParams);

		val inputs = transform(trains, e -> TrainDistAssignmentInput.builder().
				fullTrainKey(e.getFullTrainKey()).
				trnType(e.getTrnType()).
				fromOs(e.getFromOs()).
				toOs(e.getToOs()).
				trnSymb(e.getTrnSymb()).
				trnOrgnDt(e.getTrnOrgnDt()).
				trnCrewDistr(e.getTrnCrewDistr()).
				build());

		// Assign the parameters.
		val results = trainDistsAssigner.assignTrainDists(
				inputs,
				trainDistsDownloader.getAllTrainDists(),
				true,
				true,
				true );
		FileUtils.writeStringToFile(new File(filename), results.toString(), "UTF-8");
	}
	private Train excludeParams(Train train) {
		return train.
				withDepartureDistributionParams(DepartureDistributionParams.builder().build()).
				withTransitDistributionParams(TransitDistributionParams.builder().build());
	}
}
