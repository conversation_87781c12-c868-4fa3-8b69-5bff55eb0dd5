package com.nscorp.ccp.biz.crewpro;
import com.nscorp.ccp.common.crewpro.*;

public interface CrewProGroupManager {
	CrewProGroup getLatestGroup();
	CrewProGroup deleteGroupByName(String groupName);
	CrewProGroup deleteGroupById(long id);
	CrewProGroup getGroupById(long id);
	CrewProGroup getGroupByName(String name);
	Iterable<CrewProGroup> getAllGroups();
	CrewProGroup save(CrewProGroup crewProGroup);
}
