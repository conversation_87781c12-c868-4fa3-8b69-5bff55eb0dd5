package com.nscorp.ccp.biz.opd;
import com.nscorp.ccp.common.opd.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

public interface OpdDownloader {
	void downloadRecentPlans(Set<Integer> interestingPlanSas, LocalDate cutoffDate);
	Iterable<OpdScenario> downloadRecentScenarios(Set<Integer> interestingScenSas, LocalDate cutoffDate, Integer maxScenarios, int trainKeepPercentage);
}
