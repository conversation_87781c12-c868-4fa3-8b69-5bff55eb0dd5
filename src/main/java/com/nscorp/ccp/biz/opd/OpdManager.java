package com.nscorp.ccp.biz.opd;
import com.nscorp.ccp.common.opd.*;

import java.time.LocalDate;

public interface OpdManager {
	Iterable<OpdTrain> replaceOpdTrainsByPlan(Integer planSa, Iterable<OpdTrain> trains);
	long deleteTrainsByScenSa(Integer scenSa);
	long deleteTrainsByPlanSa(Integer planSa);
	Iterable<OpdScenario> getLocalOpdScenarios();
	Iterable<OpdPlan> getLocalOpdPlans();
	Iterable<OpdTrain> getLocalOpdTrains(OpdScenOrPlanSa sa, LocalDate startDt, LocalDate endDt);
	long deleteOpdScenariosByScenSa(Integer scenSa);
	long deleteOpdPlansByScenSa(Integer planSa);
	OpdScenario replaceOpdScenario(OpdScenario scenario);
	OpdPlan replaceOpdPlan(OpdPlan opdPlan);
	Iterable<OpdTrain> replaceOpdTrainsByScenario(Integer scenSa, Iterable<OpdTrain> trains);
}
