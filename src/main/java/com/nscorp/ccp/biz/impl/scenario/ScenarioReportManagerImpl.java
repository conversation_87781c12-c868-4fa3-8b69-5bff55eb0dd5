package com.nscorp.ccp.biz.impl.scenario;
import com.nscorp.ccp.biz.scenario.*;
import com.nscorp.ccp.common.scenario.*;
import com.nscorp.ccp.common.scenario.filter.BoardSummaryFilter;
import com.nscorp.ccp.common.scenario.filter.DeadheadFilter;
import com.nscorp.ccp.common.scenario.filter.SimulationTrainFilter;
import com.nscorp.ccp.common.scenario.filter.TurnUtilizationFilter;
import com.nscorp.ccp.dao.scenario.ScenarioReportDAO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
@Slf4j
public class ScenarioReportManagerImpl implements ScenarioReportManager {
	private final ScenarioReportDAO scenarioReportDAO;
	@Transactional
	@Override
	public Iterable<TrainBalance> getTrainBalanceSimulationTotalsByScenarioId(long scenarioId) {
		return scenarioReportDAO.getTrainBalanceSimulationTotalsByScenarioId(scenarioId);
	}
	@Transactional
	@Override
	public Iterable<TrainBalance> getTrainBalanceSimulationAvgsByScenarioId(long scenarioId) {
		return scenarioReportDAO.getTrainBalanceSimulationAvgsByScenarioId(scenarioId);
	}
	@Transactional
	@Override
	public Iterable<TrainBalance> getTrainBalanceInputTrnTotalsByStudyId(long studyId) {
		return scenarioReportDAO.getTrainBalanceInputTrnTotalsByStudyId(studyId);
	}
	@Transactional
	@Override
	public Iterable<TrainBalance> getTrainBalanceInputTrnAvgsByStudyId(long studyId) {
		return scenarioReportDAO.getTrainBalanceInputTrnAvgsByStudyId(studyId);
	}
	@Transactional
	@Override
	public Iterable<Deadhead> getDeadheadTotalStartsReport(Long scenarioId, String distr, String subDistrict, String boardName, String craft) {
		return scenarioReportDAO.getDeadheadTotalStartsReport(scenarioId, distr, subDistrict, boardName, craft);
	}
	@Override
	public Iterable<Deadhead> getDeadheadAvgStartsPerWeekReport(Long scenarioId, String distr, String subDistrict, String boardName, String craft) {
		return scenarioReportDAO.getDeadheadAvgStartsPerWeekReport(scenarioId, distr, subDistrict, boardName, craft);
	}
	@Transactional
	@Override
	public Iterable<TrainDelay> getTrainDelayDistrictDurationByScenarioIdTrainType(Long scenarioId, String trainType) {
		return scenarioReportDAO.getTrainDelayDistrictDurationByScenarioIdTrainType(scenarioId, trainType);
	}
	@Transactional
	@Override
	public Iterable<TrainDelay> getTrainDelayLineSegDurationByScenarioIdTrainType(Long scenarioId, String trainType) {
		return scenarioReportDAO.getTrainDelayLineSegDurationByScenarioIdTrainType(scenarioId, trainType);
	}
	@Transactional
	@Override
	public Iterable<TrainDelay> getTrainDelayDistrictPercentageByScenarioIdTrainType(Long scenarioId, String trainType) {
		return scenarioReportDAO.getTrainDelayDistrictPercentageByScenarioIdTrainType(scenarioId, trainType);
	}
	@Transactional
	@Override
	public Iterable<TrainDelay> getTrainDelayLineSegPercentageByScenarioIdTrainType(Long scenarioId, String trainType) {
		return scenarioReportDAO.getTrainDelayLineSegPercentageByScenarioIdTrainType(scenarioId, trainType);
	}
	@Override
	public Iterable<TrainStatisticsSummaryByPool> getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(Long scenarioId, String summaryLevel, String craft){
		return scenarioReportDAO.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(scenarioId, summaryLevel, craft);
	}
	@Transactional
	public Iterable<TrainStatisticsSummaryByPool> getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(Long scenarioId, String summaryLevel, String craft) {
		return scenarioReportDAO.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(scenarioId, summaryLevel, craft);
	}
	@Transactional
	@Override
	public Iterable<BoardSummaryReportSummary> getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(Long scenarioId, Long planId, String boardType, String distr, String subDistr, String mode){
		return scenarioReportDAO.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(scenarioId,planId, boardType, distr, subDistr, mode);
	}
	@Transactional
	@Override
	public Iterable<BoardSummaryReportSummary> getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(Long scenarioId, Long planId, String boardType, String distr, String subDistr, String mode){
		return scenarioReportDAO.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(scenarioId,planId, boardType, distr, subDistr, mode);
	}
	@Transactional
	@Override
	public Iterable<BoardSummaryReportDetail> getBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft(
			Long scenarioId,
			String boardName,
			String distr,
			String subDistr,
			String craft
	) {
		return scenarioReportDAO.getBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft(scenarioId,boardName, distr, subDistr, craft);
	}
	@Transactional
	@Override
	public Iterable<BoardSummaryReportDetail> getBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft(
			Long scenarioId,
			String boardName,
			String distr,
			String subDistr,
			String craft
	) {
		return scenarioReportDAO.getBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft(scenarioId, boardName, distr, subDistr, craft);
	}

	@Transactional
	@Override
	public Iterable<ScenarioSelector> getScenarioSelectorsByStatus(ScenarioStatus status) {
		return scenarioReportDAO.getScenarioSelectorsByStatus(status);
	}

	@Transactional
	@Override
	public Iterable<BoardSummaryFilter> getBoardSummaryFiltersByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getBoardSummaryFiltersByScenarioId(scenarioId);
	}

	@Transactional
	@Override
	public Iterable<DeadheadFilter> getDeadheadFiltersByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getDeadheadFiltersByScenarioId(scenarioId);

	}

	@Transactional
	@Override
	public Iterable<SimulationTrainFilter> getSimulationTrainTrnTypesByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getSimulationTrainTrnTypesByScenarioId(scenarioId);
	}

	@Transactional
	@Override
	public Iterable<SimulationTrainFilter> getSimulationTrainFiltersByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getSimulationTrainFiltersByScenarioId(scenarioId);
	}

	@Transactional
	@Override
	public Iterable<TurnUtilizationFilter> getTurnUtilizationFiltersByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getTurnUtilizationFiltersByScenarioId(scenarioId);
	}
	@Transactional
	@Override
	public Iterable<ScenarioDetail> getScenarioDetailsByStatusInProgress() {
		return scenarioReportDAO.getScenarioDetailsByStatusInProgress();
	}

	@Transactional
	@Override
	public Iterable<SimulationTrainReport> getSimulationTrainReport(Long scenarioId, String distr, String trainType, String lineSeg, String trainId, String fromOs, String delay) {
		return scenarioReportDAO.getSimulationTrainReport(scenarioId,distr,trainType,lineSeg,trainId,fromOs,delay);
	}

	@Transactional
	@Override
	public Iterable<CrewStartsByHireGrp> getCrewStartsByHireGrpByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getCrewStartsByHireGrpByScenarioId(scenarioId);
	}

	@Transactional
	@Override
	public Iterable<TargetHeadCountByHireGrp> getTargetHeadCountByHireGrpByScenarioId(Long scenarioId) {
		return scenarioReportDAO.getTargetHeadCountByHireGrpByScenarioId(scenarioId);
	}
}
