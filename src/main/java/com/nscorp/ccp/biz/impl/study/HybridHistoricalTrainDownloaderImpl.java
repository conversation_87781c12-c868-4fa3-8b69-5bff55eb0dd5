package com.nscorp.ccp.biz.impl.study;
import lombok.val;

import com.nscorp.ccp.biz.historicalTrain.HistoricalTrainDownloader;
import com.nscorp.ccp.biz.study.HybridHistoricalTrainDownloader;
import com.nscorp.ccp.common.historicalTrain.HistoricalTrain;
import com.nscorp.ccp.common.study.TrainDownloadParams;
import com.nscorp.ccp.logic.study.HybridHistoricalTrainDateAdjuster;
import com.nscorp.ccp.utils.date.DateRange;
import com.nscorp.ccp.utils.date.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.google.common.collect.Iterables.transform;

@Service
@RequiredArgsConstructor
@Slf4j
public class HybridHistoricalTrainDownloaderImpl implements HybridHistoricalTrainDownloader {
	private final HistoricalTrainDownloader historicalTrainDownloader;
	private final HybridHistoricalTrainDateAdjuster adjuster = new HybridHistoricalTrainDateAdjuster(); 

	@Override public Iterable<HistoricalTrain> download(final TrainDownloadParams params) {
		val dateRange = adjuster.computeAdjustedDateRange(params.getCurrentDate(), DateRange.of(params.getHistoryStartDate(), params.getHistoryEndDate()));

		// Compute the day adjustment which must be added to each of the trains.  This is the difference in days
		//      between the historical start date and the study start date.
		val dayAdjustment = DateTimeUtils.daysBetween(dateRange.getStartDate(), params.getStartDate());

		// Fetch the local trains between the study start and end dates.
		val historicalTrains = historicalTrainDownloader.getHistoricalTrainsByDateRange(
				dateRange.getStartDate(), dateRange.getEndDate(), params.getHistoryTrnTypes());

		// Adjust the start and end dates.
		val adjustedHistoricalTrains = transform(historicalTrains, job->adjuster.adjustDates(job, dayAdjustment));

		return adjustedHistoricalTrains;
	}
}
