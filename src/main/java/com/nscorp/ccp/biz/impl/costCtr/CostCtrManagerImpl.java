package com.nscorp.ccp.biz.impl.costCtr;

import com.nscorp.ccp.common.costCtr.TrainCostCtr;
import com.nscorp.ccp.dao.costCtr.CostCtrDAO;
import com.nscorp.ccp.biz.costCtr.CostCtrManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class CostCtrManagerImpl implements CostCtrManager {
    private final CostCtrDAO costCtrDAO;

    @Transactional
    @Override
	public Iterable<TrainCostCtr> getAllTrainCostCtrs() {
        return costCtrDAO.getAllTrainCostCtrs();
    }
}


