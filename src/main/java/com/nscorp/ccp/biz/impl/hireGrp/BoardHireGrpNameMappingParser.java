package com.nscorp.ccp.biz.impl.hireGrp;
import lombok.val;

import com.nscorp.ccp.common.hireGrp.HireGroupNameMapping;
import com.opencsv.CSVReader;
import com.opencsv.bean.CsvToBeanBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.Reader;

import static com.google.common.collect.Iterables.transform;

@Slf4j
public class BoardHireGrpNameMappingParser {
	public Iterable<HireGroupNameMapping> parseMappings(Reader reader) {
		val builder = new CsvToBeanBuilder<TmpMapping>(new CSVReader(reader) );
		val csvToBean = builder.withStrictQuotes(true).withSeparator(',').withType(TmpMapping.class).build();
		val list = csvToBean.parse();
		return transform(list, e ->
				HireGroupNameMapping.builder().
						hireGrpDescFrom(e.getHireGrpDescFrom()).
						hireGrpDescTo(e.getHireGrpDescTo()).
						description(e.getDescription()).
						build());
	}
}
