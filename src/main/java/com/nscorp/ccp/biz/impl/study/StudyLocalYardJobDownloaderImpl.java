package com.nscorp.ccp.biz.impl.study;
import lombok.val;

import com.nscorp.ieorcommons.collection.*;
import com.nscorp.ieorcommons.guava.*;
import com.nscorp.ccp.common.localYardJob.LocalTrainYardJob;
import com.nscorp.ccp.common.study.TrainDownloadParams;
import com.nscorp.ccp.biz.localYardJob.LocalTrainYardJobDownloader;
import com.nscorp.ccp.biz.study.StudyLocalYardJobDownloader;
import com.nscorp.ccp.logic.study.LocalYardJobDateAdjuster;
import com.nscorp.ccp.utils.date.DateRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.google.common.collect.Iterables.concat;
import static com.google.common.collect.Iterables.transform;
import static com.nscorp.ccp.utils.date.DateTimeUtils.daysBetween;

@Service
@RequiredArgsConstructor
@Slf4j
public class StudyLocalYardJobDownloaderImpl implements StudyLocalYardJobDownloader {
	private final LocalTrainYardJobDownloader localYardJobManager;
	private final LocalYardJobDateAdjuster dateAdjuster = new LocalYardJobDateAdjuster();

	@Override public Iterable<LocalTrainYardJob> download(TrainDownloadParams params) {
		// Get the date range for local trains and yard jobs.
		val dateRange = dateAdjuster.computeAdjustedDateRange(params.getCurrentDate(),
				DateRange.of(params.getLocalYardJobStartDate(), params.getLocalYardJobEndDate()));

		// Compute the number of days between the requested start date and the adjusted start date.
		val dayAdjustment = daysBetween(dateRange.getStartDate(), params.getStartDate());

		// Fetch the local trains between the study start and end dates.
		val localTrains = localYardJobManager.downloadLocalTrains(dateRange.getStartDate(), dateRange.getEndDate());

		// Adjust the start and end dates.
		val adjustedLocalTrains = transform(localTrains, job->dateAdjuster.adjustDates(job, dayAdjustment));

		// Fetch the yard jobs between the study start and end dates.
		val yardJobs = localYardJobManager.downloadYardJobs(dateRange.getStartDate(), dateRange.getEndDate());

		// Adjust the start and end dates.
		val adjustedYardJobs = transform(yardJobs, job->dateAdjuster.adjustDates(job, dayAdjustment));

		// Return all the local trains and yard jobs.
		return ImmutableCollectionUtils.concatLists(adjustedLocalTrains, adjustedYardJobs);
	}
}
