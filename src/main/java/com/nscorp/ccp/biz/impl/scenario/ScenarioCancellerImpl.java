package com.nscorp.ccp.biz.impl.scenario;
import lombok.val;
import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.Streams.*;

import com.google.common.base.Preconditions;
import com.nscorp.ccp.utils.commonTypes.*;
import com.nscorp.ccp.biz.job.*;
import com.nscorp.ccp.biz.scenario.*;
import com.nscorp.ccp.common.scenario.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ScenarioCancellerImpl implements ScenarioCanceller {
	private final JobManager jobManager;
	private final ScenarioManager scenarioManager;
	@Override public Scenario cancelScenario(long scenarioId) {
		val scenario = Preconditions.checkNotNull(scenarioManager.getScenarioById(scenarioId));
		val savedScenario = scenarioManager.save(scenario.withStatus(ScenarioStatus.CANCELLED));
		val savedJobSummaries = stream(jobManager.getJobSummariesByScenarioId(scenarioId)).
				map(jobSummary -> Optional.ofNullable(jobManager.setJobStatus(jobSummary.getJobId(), JobStatus.TERMINATION_REQUESTED))).
				collect(toImmutableList());
		return savedScenario;
	}
}
