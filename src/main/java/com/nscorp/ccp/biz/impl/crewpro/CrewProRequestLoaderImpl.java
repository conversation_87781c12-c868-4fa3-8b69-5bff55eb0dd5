package com.nscorp.ccp.biz.impl.crewpro;

import com.nscorp.ccp.common.plan.PlanHub;
import io.vavr.Function0;
import lombok.val;

import com.nscorp.ccp.common.crewpro.CrewProLoadRequestResults;
import com.nscorp.ccp.common.crewpro.CrewProRequest;
import com.nscorp.ccp.common.study.StudyHub;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.logic.crewpro.PlanHubCreator;
import com.nscorp.ccp.logic.modelJson.ModelJsonRequestParser;
import com.nscorp.ccp.logic.modelJson.ModelJsonToDomainObjectMapper;
import com.nscorp.ccp.logic.pba.PbaAssignmentApplier;
import com.nscorp.ccp.biz.crewpro.CrewProRequestLoader;
import com.nscorp.ccp.biz.plan.PlanManager;
import com.nscorp.ccp.biz.scenario.ScenarioManager;
import com.nscorp.ccp.biz.scenario.StudyPlanScenarioFetcher;
import com.nscorp.ccp.biz.study.StudyManager;
import com.nscorp.ccp.utils.tx.TransactionRunner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.google.common.collect.Iterables.*;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.scenario.HubAssignmentReasonCode.ASSIGNED_BY_CREWPRO;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static com.nscorp.ccp.utils.logging.LogUtils.warn;
import static io.vavr.Function0.constant;

@Service
@Slf4j
@RequiredArgsConstructor
public class CrewProRequestLoaderImpl implements CrewProRequestLoader {
	private final PlanManager planManager;
	private final StudyManager studyManager;
	private final ScenarioManager scenarioManager;
	private final StudyPlanScenarioFetcher studyPlanScenarioFetcher;
	private final PlanHubCreator planHubCreator= new PlanHubCreator();

    private final TransactionRunner transactionRunner;
	private final ModelJsonRequestParser modelJsonRequestParser = new ModelJsonRequestParser();
	private final ModelJsonToDomainObjectMapper modelJsonToDomainObjectMapper = new ModelJsonToDomainObjectMapper();
	private final PbaAssignmentApplier pbaAssignmentApplier = new PbaAssignmentApplier();

	@Override
	@Transactional
	public CrewProLoadRequestResults loadRequestFromString(long scenarioId, String request) {
        return transactionRunner.getRequiredOrNotSupportedTemplate().execute(status->{
			// Parse the request and map to DTO's.
			val jsonRequest = modelJsonRequestParser.parseRequest(request);

			val crewProRequest = modelJsonToDomainObjectMapper.jsonToDto(jsonRequest);
			if (log.isInfoEnabled()) {
				info(log, String.format("crewProRequest=%s", crewProRequest));
			}

			// Load the request.
			return loadRequest(scenarioId, crewProRequest);
		});
	}

	@Override
	public CrewProLoadRequestResults loadRequest(
			final long scenarioId,
			final CrewProRequest crewProRequest) {
        return transactionRunner.getRequiredOrNotSupportedTemplate().execute(status->loadRequestInTx(scenarioId, crewProRequest));
	}

	private Iterable<Train> removeDuplicateTrains(final Iterable<Train> trains, final Iterable<Train> existingTrains) {
		// Get the set of all existing train keys for this study (note this includes trains from *all* hubs,
		//      rather than just the hub currently being processed).
		val existing = stream(existingTrains).
				map(Train::getFullTrainKey).
				collect(toImmutableSet());

		// Return all the trains passed in 'trains' that do not already exist in this study.
		return stream(trains).filter(t -> ! existing.contains(t.getFullTrainKey())).collect(toImmutableList());
	}

	private StudyHub buildStudyHub(CrewProRequest crewProRequest, long studyId, Long hubId) {
		val myTrains = crewProRequest.getTrains().stream().
				map(e -> e.toBuilder().studyId(studyId).hubId(hubId).build()).
				collect(toImmutableSet());
		return StudyHub.builder().studyId(studyId ).hubId(hubId).trains(myTrains).build();
	}

	private Iterable<Train> nullTrainId(final Iterable<Train> trains) {
		return transform(trains, train -> train.withTrainId(null));
	}

	private CrewProLoadRequestResults loadRequestInTx(final long scenarioId, final CrewProRequest crewProRequest) {
		// Fetch the scenario, plan, and study.
		val sps = studyPlanScenarioFetcher.getStudyPlanScenario(scenarioId);
		val planId = sps.getPlan().getPlanId();

		// Save the plan objects as a distinct hub.
		val hubId = savePlanHub(crewProRequest, planId);

		val studyId = sps.getStudy().getStudyId();
		val studyHub = buildStudyHub(crewProRequest, studyId, hubId);

		// Remove any trains that are already present in the study.
		val existingTrains = studyManager.getTrainsByStudyId(studyId);
		val nonduplicateTrains = removeDuplicateTrains(studyHub.getTrains(), existingTrains);

		// Take the nonduplicate trains and populate hub info.
		val studyTrains = stream(nonduplicateTrains).
				map(t -> t.toBuilder().hub(crewProRequest.getHubName()).hubAssignmentReasonCode(ASSIGNED_BY_CREWPRO).build()).
				collect(toImmutableList());

		// Remove any existing trains for this study and hub (normally there will not be any such trains), and
		//      then insert all the trains in 'studyTrains'.
		val savedTrainsForThisHub = studyManager.replaceTrainsByStudyIdAndHubName(studyId, crewProRequest.getHubName(), studyTrains);

		// Log all the line segments in the saved trains.
		val lineSegments = stream(savedTrainsForThisHub).map(Train::getTrnCrewDistr).collect(toImmutableSet());
		warn(log, String.format("Saving these line segments to study #%d: %s", studyId,
			lineSegments));

		// Translate the trains into ScenTrain's, which are a scenario-based copy of the train data with some additional data
		//      thrown in.
		val scenTrainsForThisHub = nullTrainId(pbaAssignmentApplier.applyAssignments(savedTrainsForThisHub, of()));

		// Save the ScenTrain's under the current scenario ID.
		val savedScenTrainsForThisHub = scenarioManager.replaceScenTrainsForScenarioAndHub(scenarioId, hubId, scenTrainsForThisHub);

		// Package results and return.
		return CrewProLoadRequestResults.builder().
				hubId(hubId).
				studyHub(studyHub).
				crewProRequest(crewProRequest).
				scenTrainsForThisHub(copyOf(savedScenTrainsForThisHub)).
				build();
	}

	private long savePlanHub(final CrewProRequest crewProRequest, final Long planId) {
		return constant(crewProRequest).
				andThen(e->planHubCreator.buildPlanHub(e, planId)).
				andThen(planManager::createPlanHub).
				andThen(PlanHub::getHubId).
				get();

		/**
		 WPM-199: no need to delete existing hub anymore because this function is only called on a fresh plan.

		// If the hub already exists, delete it along with all the plan objects associated with it.
		var hub = planManager.getHubByPlanIdAndHubName(planHub.getPlanId(), planHub.getHubName());
		if ( hub != null ) {
			planManager.deleteHub(planId, hub.getHubId());
		}
		**/
	}
}
