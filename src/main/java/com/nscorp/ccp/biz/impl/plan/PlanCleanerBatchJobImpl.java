package com.nscorp.ccp.biz.impl.plan;
import lombok.val;


import com.nscorp.ccp.common.batchRun.BatchRunType;
import com.nscorp.ccp.common.plan.PlanStatus;
import com.nscorp.ccp.biz.batchRun.BatchRunManager;
import com.nscorp.ccp.biz.controlParam.ControlParamReader;
import com.nscorp.ccp.common.controlParam.ControlParams;
import com.nscorp.ccp.biz.plan.PlanCleanerBatchJob;
import com.nscorp.ccp.biz.plan.PlanManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.Streams.stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class PlanCleanerBatchJobImpl implements PlanCleanerBatchJob {
	private final PlanManager planManager;
	private final ControlParamReader controlParamReader;
	private final BatchRunManager batchRunManager;
	@Override public boolean runOnce(boolean force) {
		final boolean result;
		if (force || ! controlParamReader.isTrue(ControlParams.PLAN_CLEANER_BATCH_JOB_DISABLED)) {
			batchRunManager.run(BatchRunType.STUDY_CLEANER, ()-> {
				val plans = stream(planManager.getAllPlans()).filter(study -> study.getStatus() == PlanStatus.DELETED).collect(toImmutableList());
				plans.forEach(plan -> planManager.deletePlanById(plan.getPlanId()));
			});
			result = true;
		}
		else {
			result = false;
		}
		return result;
	}
}
