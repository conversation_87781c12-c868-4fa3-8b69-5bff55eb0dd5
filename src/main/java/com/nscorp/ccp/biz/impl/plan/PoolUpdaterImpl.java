package com.nscorp.ccp.biz.impl.plan;
import com.nscorp.ccp.biz.plan.*;
import lombok.*;

import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.plan.Pool;
import com.nscorp.ccp.logic.plan.PoolUpdaterLogic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoolUpdaterImpl implements PoolUpdater {
	private final PlanManager planManager;
	private final PoolUpdaterLogic logic = new PoolUpdaterLogic(); 

	/*
	@NonNull @Override public Pool update(
			@NonNull final Pool fromRequest,
			final long poolId) {
		@NonNull val existing = Optional.ofNullable(planManager.getPoolById(poolId)).
				orElseThrow(()->new PoolUpdaterException(String.format("Pool #%d not found.", poolId)));
		val updated = logic.update(existing, fromRequest, poolId);

		// Save the updated pool if it is not locked.
		val saved = planManager.saveIfNotLocked(updated);
		if ( log.isWarnEnabled() ) {
			warn(log, String.format("Just saved the following pool: %s", saved));
		}
		return saved;
	}
	*/

	@NonNull @Override public Pool update(
			@NonNull final Pool fromRequest,
			final long poolId) {
		return Optional.
				// Get the pool by ID.
				ofNullable(planManager.getPoolById(poolId)).

				// Update the pool.
				map(existing->logic.update(existing, fromRequest, poolId)).

				// Save the updated pool if it is not locked.
				map(planManager::saveIfNotLocked).

				// If no such pool is found, throw an exception.
				orElseThrow(()->new PoolUpdaterException(String.format("Pool #%d not found.", poolId)));
	}
}
