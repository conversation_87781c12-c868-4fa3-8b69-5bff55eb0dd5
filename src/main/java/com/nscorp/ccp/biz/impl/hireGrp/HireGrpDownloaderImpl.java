package com.nscorp.ccp.biz.impl.hireGrp;
import lombok.val;

import com.nscorp.ccp.common.hireGrp.BoardCraftHireGrpMapping;
import com.nscorp.ccp.common.hireGrp.BoardHireGrpMapping;
import com.nscorp.ccp.dao.hireGrp.HireGrpDAO;
import com.nscorp.ccp.dao.hireGrp.HireGrpDownloaderDAO;
import com.nscorp.ccp.biz.hireGrp.HireGrpDownloader;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.google.common.collect.Iterables.transform;

@Service
@Slf4j
@RequiredArgsConstructor
public class HireGrpDownloaderImpl implements HireGrpDownloader {
	private final HireGrpDownloaderDAO downloaderDAO;
	private final HireGrpDAO dao;
	private final DateTimeProvider dateTimeProvider;
	@Transactional
	@Override public Iterable<BoardHireGrpMapping> downloadBoardHireGrpMappings() {
		return downloaderDAO.downloadBoardHireGrpMappings();
	}

	@Transactional
	@Override public Iterable<BoardCraftHireGrpMapping> downloadBoardCraftHireGrpMappings() {
		return downloaderDAO.downloadBoardCraftHireGrpMappings();
	}

	@Transactional
	@Override public void downloadAndSaveBoardHireGrpMappings() {
		val now = dateTimeProvider.now();
		val mappings = downloaderDAO.downloadBoardHireGrpMappings();
		dao.replaceAllBoardHireGroupMappings(transform(mappings, e -> e.withSnapshotTs(now)));
	}

	@Transactional
	@Override public void downloadAndSaveBoardCraftHireGrpMappings() {
		val now = dateTimeProvider.now();
		val mappings = downloaderDAO.downloadBoardCraftHireGrpMappings();
		dao.replaceAllBoardCraftHireGroupMappings(transform(mappings, e -> e.withSnapshotTs(now)));
	}
}
