package com.nscorp.ccp.biz.impl.sba;
import lombok.val;

import static com.google.common.collect.ImmutableMap.*;

import com.nscorp.ccp.biz.sba.*;
import com.nscorp.ccp.common.sba.*;
import com.nscorp.ccp.dao.sba.TsrStationControlDAO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Function;

import static com.google.common.collect.Streams.stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class TsrCrewStationFinderImpl implements TsrCrewStationFinder {
    private final TsrStationControlDAO tsrStationControlDAO;

	@Override public TsrStationMap getTsrStationMap(){
		return new TsrStationMap(getTsrStationControlMap().entrySet().stream().collect(toImmutableMap(Map.Entry::getKey, e -> e.getValue().getCrewStation())));
	}

	private Map<String,TsrStationControlVO> getTsrStationControlMap() {
		val tsrStationControlList = tsrStationControlDAO.getAllTsrStationControls();

		// Build a map of TsrStationControlVO's keyed by 'tsrStation'.
		return stream(tsrStationControlList).
				collect(toImmutableMap(TsrStationControlVO::getTsrStation, Function.identity()));
	}

}
