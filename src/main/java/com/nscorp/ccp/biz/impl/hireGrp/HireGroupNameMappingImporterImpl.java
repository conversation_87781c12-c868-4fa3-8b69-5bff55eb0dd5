package com.nscorp.ccp.biz.impl.hireGrp;
import lombok.val;

import com.nscorp.ccp.biz.hireGrp.HireGroupNameMappingImporter;
import com.nscorp.ccp.biz.hireGrp.HireGrpManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.StringReader;

@Service
@Slf4j
@RequiredArgsConstructor
public class HireGroupNameMappingImporterImpl implements HireGroupNameMappingImporter {
	@Value("classpath:hire_group_name_mapping.csv")
	private Resource mappingCsv;

	private final BoardHireGrpNameMappingParser parser = new BoardHireGrpNameMappingParser();
	private final HireGrpManager manager;

	@SneakyThrows @Override public void importNameMappings() {
		val file = IOUtils.toString(mappingCsv.getInputStream(), "UTF-8");
		val mappings = parser.parseMappings(new StringReader(file));
		manager.replaceAllBoardHireGroupNameMappings(mappings);
	}
}
