package com.nscorp.ccp.biz.impl.crewpro;
import lombok.val;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.biz.crewpro.CrewProJobFileCollector;
import com.nscorp.ccp.common.crewpro.CrewProJobFiles;
import com.nscorp.ccp.logic.crewpro.CrewProJobFileCollectorLogic;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.assertj.core.util.Streams;
import org.springframework.stereotype.Service;

import java.io.File;

import static com.nscorp.ccp.logic.crewpro.CrewProJobFileCollectorLogic.*;

@Service
@Slf4j
public class CrewProJobFileCollectorImpl implements CrewProJobFileCollector {
	private final CrewProJobFileCollectorLogic logic = new CrewProJobFileCollectorLogic(); 

	@Override public Iterable<CrewProJobFiles> collectFiles(final Iterable<File> files) {
		val list = Streams.stream(files).map(this::readFile).collect(ImmutableList.toImmutableList());
		return logic.collectFiles(list);
	}

	@SneakyThrows private NameAndContents readFile(File file) {
		val s = FileUtils.readFileToString(file, "UTF-8");
		return NameAndContents.builder().name(file).contents(s).build();
	}
}
