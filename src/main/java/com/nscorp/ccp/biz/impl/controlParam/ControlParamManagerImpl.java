package com.nscorp.ccp.biz.impl.controlParam;
import java.util.*;
import lombok.val;

import com.nscorp.ccp.common.controlParam.ControlParam;
import com.nscorp.ccp.dao.controlParam.ControlParamDAO;
import com.nscorp.ccp.biz.controlParam.ControlParamManager;
import com.nscorp.ieorcommons.lang.NSBooleanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ControlParamManagerImpl implements ControlParamManager {
	private final ControlParamDAO dao;
	@Override @Transactional
	public boolean checkAndSetFalse(String key) {
		final boolean value = isTrue(key);
		if ( value ) {
			setParam(key, "N");
		}
		return value;
	}
	@Transactional
	@Override public boolean isTrue(String key) {
		return Optional.ofNullable(getControlParamByKey(key)).
				map(ControlParam::getParamValue).
				map(NSBooleanUtils::flexibleValueOf).
				orElse(false);
	}
	@Transactional
	@Override public ControlParam setParam(String key, String value) {
		deleteControlParamByKey(key);
		val newParam = ControlParam.builder().paramKey(key).paramValue(value).build();
		return dao.save(newParam);
	}

	@Transactional
	@Override public Iterable<ControlParam> getAllControlParams() {
		return dao.getAllControlParams();
	}
	@Transactional
	@Override public ControlParam getControlParamByKey(String key) {
		return dao.getControlParamByKey(key);
	}
	@Transactional
	@Override public String getControlParamValueByKey(String key) {
		return Optional.ofNullable(dao.getControlParamByKey(key)).
				map(ControlParam::getParamValue).
				orElse(null);
	}
	@Transactional
	@Override public Long deleteControlParamByKey(String key) {
		return dao.deleteControlParamByKey(key);
	}
}
