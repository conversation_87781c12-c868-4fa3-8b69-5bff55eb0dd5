package com.nscorp.ccp.biz.impl.sba;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Iterables;
import com.nscorp.ccp.biz.sba.ApcnDataLoader;
import com.nscorp.ccp.biz.sba.TrnTypeFinder;
import com.nscorp.ccp.common.sba.ApcnHistData;
import com.nscorp.ccp.dao.sba.ApcnDataDAO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.google.common.collect.Iterables.transform;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApcnDataLoaderImpl implements ApcnDataLoader {
	private final ApcnDataDAO apcnDataDAO;
	private final TrnTypeFinder trnTypeFinder;

	@Override public Iterable<ApcnHistData> load() {
		val stopwatch = Stopwatch.createStarted();

		// Read the APCN records.
		val apcns = transform(apcnDataDAO.apcnMatchingEntireTable(), this::addTrnType);

		// Print stats on the ApcnHistData's:
		printApcnDataStats(apcns);

		if ( log.isInfoEnabled() ) {
			info(log, String.format("load() took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
		}
		return apcns;
	}

	private void printApcnDataStats(final Iterable<ApcnHistData> apcns) {
		val hasTrainOrgnOs = stream(apcns).filter(e -> isNotEmpty(e.getTrainOrgnOs())).count();
		val hasTrnType = stream(apcns).filter(e -> isNotEmpty(e.getTrnType())).count();
		val size = Iterables.size(apcns);
		val trainOrgnOsPercentage = 100.00 * (double) hasTrainOrgnOs / (double) size;
		val trnTypePercentage = 100.00 * (double) hasTrnType / (double) size;
		if ( log.isInfoEnabled() ) {
			info(log, String.format("apcns: %.2f%% have trainOrgnOs, %.2f%% have trnType.", trainOrgnOsPercentage, trnTypePercentage));
		}
	}

	private ApcnHistData addTrnType(ApcnHistData apcnData) {
		return apcnData.withTrnType(getTrnTypeForApcnData(apcnData).orElse(null));
	}

	private Optional<String> getTrnTypeForApcnData(ApcnHistData apcnData) {
		return trnTypeFinder.getTrnType(apcnData.getTrnSymb(), apcnData.getLineSegment(), apcnData.getFromOs(), apcnData.getToOs());
	}
}
