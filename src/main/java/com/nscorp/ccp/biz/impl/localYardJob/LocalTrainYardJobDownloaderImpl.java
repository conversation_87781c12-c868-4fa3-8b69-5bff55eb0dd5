package com.nscorp.ccp.biz.impl.localYardJob;

import com.google.common.collect.Iterables;
import com.nscorp.ccp.biz.localYardJob.LocalTrainYardJobDownloader;
import com.nscorp.ccp.common.localYardJob.LocalTrainYardJob;
import com.nscorp.ccp.dao.localYardJob.LocalYardJobDAO;
import com.nscorp.ccp.logic.study.TrnParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
@RequiredArgsConstructor
@Slf4j
public class LocalTrainYardJobDownloaderImpl implements LocalTrainYardJobDownloader {
	private final TrnParser trnParser = new TrnParser(); 
	private final LocalYardJobDAO localYardJobDAO;

	private LocalTrainYardJob populateTrn(LocalTrainYardJob job) {
		val trn = trnParser.buildTrn(job.getTrnSymb(), job.getTrnCrewDistr(), job.getTrnOrgnDt());
		return job.withTrn(trn).withTrnId(trn);
	}
	@Override public Iterable<LocalTrainYardJob> downloadLocalTrains(LocalDate studyStart, LocalDate studyEnd) {
		return Iterables.transform(localYardJobDAO.downloadLocalTrains(studyStart, studyEnd), this::populateTrn);
	}
	@Override public Iterable<LocalTrainYardJob> downloadYardJobs(LocalDate studyStart, LocalDate studyEnd) {
		return Iterables.transform(localYardJobDAO.downloadYardJobs(studyStart, studyEnd), this::populateTrn);
	}
}
