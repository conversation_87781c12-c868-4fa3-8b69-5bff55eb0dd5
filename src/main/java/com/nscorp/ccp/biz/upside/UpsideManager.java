package com.nscorp.ccp.biz.upside;

import com.nscorp.ccp.common.upside.*;

public interface UpsideManager {
	Iterable<Upside> getAllUpsides();
	UpsideAndTrains save(UpsideAndTrains upsideAndTrains);
	void deleteUpsideById(long upsideId);
	UpsideTrain save(UpsideTrain train);
	UpsideAndTrains getUpsideAndTrainsById(long upsideId);
	Upside getUpsideById(long upsideId);
	Iterable<UpsideTrain> getTrains(Long upsideId);
}
