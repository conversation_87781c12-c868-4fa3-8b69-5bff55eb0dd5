package com.nscorp.ccp.biz.hireGrp;

import com.nscorp.ccp.common.hireGrp.BoardHireGrpMapping;
import com.nscorp.ccp.common.hireGrp.HireGroupNameMapping;
import org.springframework.transaction.annotation.Transactional;

public interface HireGrpManager {
	Iterable<BoardHireGrpMapping> getAllBoardHireGrpMappings();

	@Transactional Iterable<HireGroupNameMapping> getAllHireGroupNameMappings();
	Iterable<HireGroupNameMapping> replaceAllBoardHireGroupNameMappings(Iterable<HireGroupNameMapping> mappings);
}

