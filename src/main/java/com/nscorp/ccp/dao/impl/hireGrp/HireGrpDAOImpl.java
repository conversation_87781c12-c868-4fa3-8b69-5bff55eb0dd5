package com.nscorp.ccp.dao.impl.hireGrp;
import lombok.*;

import com.nscorp.ccp.common.hireGrp.*;
import com.nscorp.ccp.dao.hireGrp.HireGrpDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;

import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.Streams.*;
import static io.vavr.Function0.constant;

@Repository
@Slf4j
@RequiredArgsConstructor
class HireGrpDAOImpl implements HireGrpDAO {
	private final HireGroupNameMappingRepository nameMappingRepository;
	private final BoardHireGrpMappingRepository repo;
	private final BoardCraftHireGrpMappingRepository craftRepo;

	@NonNull @Override public HireGroupNameMapping save(@NonNull HireGroupNameMapping dto) {
		return constant(dto).
				andThen(nameMappingRepository::save).
				apply();
	}

	@Override public Iterable<BoardHireGrpMapping> getAllBoardHireGrpMappings() {
		return repo.findAll();
	}

	@Override public Iterable<HireGroupNameMapping> getAllHireGroupNameMappings() {
		return nameMappingRepository.findAll();
	}

	@Override public Iterable<HireGroupNameMapping> replaceAllHireGroupNameMappings(Iterable<HireGroupNameMapping> mappings) {
		nameMappingRepository.deleteAll();
		return stream(mappings).
			map(this::save).
			collect(toImmutableList());
	}

	@NonNull @Override public BoardHireGrpMapping save(@NonNull BoardHireGrpMapping dto) {
		return constant(dto).
				andThen(repo::save).
				apply();
	}

	@NonNull @Override public BoardCraftHireGrpMapping save(@NonNull BoardCraftHireGrpMapping dto) {
		return constant(dto).
				andThen(craftRepo::save).
				apply();
	}

	@Override public Iterable<BoardHireGrpMapping> replaceAllBoardHireGroupMappings(Iterable<BoardHireGrpMapping> mappings) {
		repo.deleteAll();
		return stream(mappings).
				map(this::save).
				collect(toImmutableList());
	}

	@Override public Iterable<BoardCraftHireGrpMapping> replaceAllBoardCraftHireGroupMappings(Iterable<BoardCraftHireGrpMapping> mappings) {
		craftRepo.deleteAll();
		return stream(mappings).
			map(this::save).
			collect(toImmutableList());
	}

	@Override public Long deleteBySnapshotTs(Instant snapshotTs) {
		return repo.deleteBySnapshotTs(snapshotTs);
	}

    @Repository
	interface BoardCraftHireGrpMappingRepository extends CrudRepository<BoardCraftHireGrpMapping,Long> {
		Long deleteBySnapshotTs(Instant snapshotTs);
	}

    @Repository
	interface BoardHireGrpMappingRepository extends CrudRepository<BoardHireGrpMapping,Long> {
		Long deleteBySnapshotTs(Instant snapshotTs);
	}

    @Repository
	interface HireGroupNameMappingRepository extends CrudRepository<HireGroupNameMapping,Long> {
	}
}
