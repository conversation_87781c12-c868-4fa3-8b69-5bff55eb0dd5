package com.nscorp.ccp.dao.impl.historicalTrain;

import com.google.common.base.Stopwatch;
import com.nscorp.ccp.common.historicalTrain.HistCrewProfileMatchReasonCode;
import com.nscorp.ccp.common.historicalTrain.HistoricalTrain;
import com.nscorp.ccp.dao.historicalTrain.*;
import com.nscorp.ieorcommons.persistence.jdbc.NSDbUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.codecs.DB2Codec;
import org.owasp.esapi.reference.DefaultEncoder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

import static com.google.common.collect.Iterables.filter;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static java.util.stream.Collectors.joining;

@Slf4j
@Repository
class HistoricalTrainDAOImpl implements HistoricalTrainDAO {
    @Value("${HistoricalTrainDAOImpl.getHistoricalRoadTrainsQuery1Sql}")
    private String phase1Sql;

    @Value("${HistoricalTrainDAOImpl.getHistoricalRoadTrainsQuery2Sql}")
    private String phase2Sql;

    private final JdbcTemplate jdbcTemplate;
    public HistoricalTrainDAOImpl (final @Qualifier("dataWarehouseDataSource") DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }
    @Override public Iterable<HistoricalTrain> getPhase1Trains(@NonNull final LocalDate startDate, @NonNull final LocalDate endDate) {
        // Note the ESAPI encoding doesn't achieve anything here except to disable Veracode warnings.
        val startDateStr = DefaultEncoder.getInstance().encodeForSQL(new DB2Codec(), startDate.toString());
        val endDateStr = DefaultEncoder.getInstance().encodeForSQL(new DB2Codec(), endDate.toString());
        val sql = phase1Sql.
                replaceAll(":startDate", startDateStr).
                replaceAll(":endDate", endDateStr);
        val stopwatch = Stopwatch.createStarted();
        val result = jdbcTemplate.query(sql, PHASE_1_ROW_MAPPER);
        if ( log.isInfoEnabled() ) {
            info(log, String.format("Phase 1 query took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
        }
        result.stream().filter(historicalTrain ->historicalTrain.getToOs() == null).forEachOrdered(historicalTrain -> {
            if ( log.isInfoEnabled() ) {
                info(log, String.format("IGNORING THIS TRAIN BECAUSE TO_OS IS NULL: %s", historicalTrain));
            }
        });
        return filter(result, e->e.getToOs() != null);
    }
    @Override public Iterable<Phase2QueryResult> executePhase2Query(final Iterable<TrainCategoryKey> trainCategoryKeys) {
        val phase2Query = buildPhase2Query(trainCategoryKeys);
        if ( log.isInfoEnabled() ) {
            info(log, String.format("phase2Query=%s", phase2Query));
        }

        // Execute the query.
        val stopwatch = Stopwatch.createStarted();
        val result = jdbcTemplate.query(phase2Query, PHASE_2_ROW_MAPPER);
        if ( log.isInfoEnabled() ) {
            info(log, String.format("Phase 2 query took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
        }
        return result;
    }

    private static final RowMapper<HistoricalTrain> PHASE_1_ROW_MAPPER = (rs,rowNum)-> HistoricalTrain.builder().
        trn(NSDbUtils.getStringColumn(rs, "trn", null)).
        trnOrgnDt(NSDbUtils.getLocalDateColumn(rs, "trnOrgnDt", ZoneId.systemDefault())).
        trnSection(NSDbUtils.getStringColumn(rs, "trnSection", null)).
        trnCrewDistr(NSDbUtils.getStringColumn(rs, "trnCrewDistr", null)).
        fromLocSeqNr(NSDbUtils.getIntegerColumn(rs, "fromLocSeqNr", null)).
        fromOs(NSDbUtils.getStringColumn(rs, "fromOs", null)).
        fromTs(NSDbUtils.getTimestampColumnAsInstant(rs, "fromTs", null)).
        toLocSeqNr(NSDbUtils.getIntegerColumn(rs, "toLocSeqNr", null)).
        toTs(NSDbUtils.getTimestampColumnAsInstant(rs, "toTs", null)).
        toOs(NSDbUtils.getStringColumn(rs, "toOs", null)).
        trnType(NSDbUtils.getStringColumn(rs, "trnType", null)).
        profileCountNow(NSDbUtils.getIntegerColumn(rs, "profileCountNow", null)).
        profileId(NSDbUtils.getStringColumn(rs, "profileId", null)).
        crewOrgnOs(NSDbUtils.getStringColumn(rs, "crewOrgnOs", null)).
        crewProfileMatchCode(HistCrewProfileMatchReasonCode.PHASE_1_MATCH).
        build();

    private static final RowMapper<Phase2QueryResult> PHASE_2_ROW_MAPPER = (rs,rowNum)-> Phase2QueryResult.builder().
        trn(NSDbUtils.getStringColumn(rs, "trn", null)).
        trnOrgnDt(NSDbUtils.getLocalDateColumn(rs, "trnOrgnDt", ZoneId.systemDefault())).
        trnSection(NSDbUtils.getStringColumn(rs, "trnSection", null)).
        trnCrewDistr(NSDbUtils.getStringColumn(rs, "trnCrewDistr", null)).
        profileCountNow(NSDbUtils.getIntegerColumn(rs, "profileCountNow", null)).
        profileId(NSDbUtils.getStringColumn(rs, "profileId", null)).
        crewOrgnOs(NSDbUtils.getStringColumn(rs, "crewOrgnOs", null)).
        build();

    private String buildPhase2Query(final Iterable<TrainCategoryKey> trains) {
        val commaDelimetedList = stream(trains).
                map(train->String.format("'%s-%s-%s'", train.getTrnSymb(), train.getTrnCrewDistr(), train.getTrnSection())).
                collect(joining(","));
        val inClause = String.format("(%s)", commaDelimetedList);
        return StringUtils.replace(phase2Sql, ":inClause", inClause);
    }
}
