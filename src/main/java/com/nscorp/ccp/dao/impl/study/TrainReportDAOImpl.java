package com.nscorp.ccp.dao.impl.study;
import java.util.*;

import lombok.NonNull;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.ImmutableMap;
import com.nscorp.ccp.common.study.BoardType;
import com.nscorp.ccp.common.study.TrainSummary;
import com.nscorp.ccp.dao.study.TrainReportDAO;
import com.nscorp.ccp.utils.commonTypes.sort.SortColumns;
import com.nscorp.ccp.utils.commonTypes.sort.SortModel;
import com.nscorp.ccp.utils.commonTypes.sort.SortTypes;
import com.nscorp.ieorcommons.persistence.jdbc.NSDbUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
//@AllArgsConstructor
@Repository
class TrainReportDAOImpl implements TrainReportDAO {
    private static final int MAX_RECORDS  = 999999;
    private static final RowMapper<TrainSummary> ROW_MAPPER = (rs, rowNum) -> {
        final TrainSummary result = TrainSummary.builder().
                trnSymb(NSDbUtils.getStringColumn(rs, "trn_symb", null)).
                fromOs(NSDbUtils.getStringColumn(rs, "from_os", null)).
                toOs(NSDbUtils.getStringColumn(rs, "to_os", null)).
                trnType(NSDbUtils.getStringColumn(rs, "trn_type", null)).
                numTrains(NSDbUtils.getIntegerColumn(rs, "num_trains", null)).
                build();
        if (log.isInfoEnabled()) {
            info(log, String.format("TrainDAOImpl.ROW_MAPPER: result=%s", result));
        }
        return result;
    };
    private final NamedParameterJdbcTemplate jdbcTemplate;
    @Value("${TrainSummary.findByStudyIdAndTrnCrewDistrAndTrnTypeAndTrnSymbCrewOrgnOsAndAssignFlagAndRowNumBetween}")
    private String getTrainsSql;

    public TrainReportDAOImpl(@Qualifier("scpmDataSource") DataSource dataSource) {
        this.jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
    }

    private static final Map<SortColumns, String> getTrainDBColumns() {
        return ImmutableMap.<SortColumns,String>builder().
                put(SortColumns.FROMOS, "from_os").
                put(SortColumns.TOOS, "to_os").
                put(SortColumns.TRNSYMB, "trn_symb").
                put(SortColumns.TRNTYPE, "trn_type").
                put(SortColumns.NUMTRAINS, "num_trains").
                build();
    }

    private static Map<SortTypes, String> getTrainDBSortTypes() {
        return ImmutableMap.<SortTypes,String>builder().
                put(SortTypes.ASC, "asc").
                put(SortTypes.DESC, "desc").
                build();
    }

    @Override
    public Iterable<TrainSummary> getTrainSummaries(
            long studyId,
            String trnCrewDistr,
            String trnType,
            String trnSymb,
            String crewOrgnOs,
            BoardType boardType,
            Integer pageNo,
            Integer pageSize,
            List<SortModel> sortModels) {
        val startRowNum = (pageNo != null && pageSize != null) ? pageNo * pageSize : 0;
        val noOfRows = (pageNo != null && pageSize != null) ? pageSize : MAX_RECORDS;

        val orderBy = getOrderBy(sortModels);

        val assignFlag = getAssignFlag(boardType);

        val sql = getTrainsSql.replace(":ORDER_BY_CLAUSE", orderBy);
        val params = new MapSqlParameterSource();
        params.addValue("studyId", studyId);
        params.addValue("trnCrewDistr", trnCrewDistr);
        params.addValue("trnType", trnType);
        params.addValue("trnSymb", trnSymb);
        params.addValue("crewOrgnOs", crewOrgnOs);
        params.addValue("assignFlag", assignFlag);
        params.addValue("noOfRows", noOfRows);
        params.addValue("startRowNum", startRowNum);
        return jdbcTemplate.query(sql, params, ROW_MAPPER);
    }

    @Nullable private static Integer getAssignFlag(final BoardType boardType) {
        final Integer assignFlag;
        if (BoardType.POOL.equals(boardType)) {
            assignFlag = 0;
        }
        else {
            if (BoardType.EXTRABOARD.equals(boardType)) {
                assignFlag = 1;
            }
            else {
                assignFlag = boardType == null ? null : - 1;
            }
        }
        return assignFlag;
    }

    @NonNull private static String getOrderBy(final List<SortModel> sortModels) {
        val sortColumnsMap = getTrainDBColumns();
        val sortTypesMap = getTrainDBSortTypes();
        val orderByInitial = sortModels.stream().map(item -> sortColumnsMap.get(item.getColId()) + " " + sortTypesMap.get(item.getSort())).collect(Collectors.joining(","));
        final String orderBy;
        if (StringUtils.isEmpty(orderByInitial)) {
            orderBy = new StringBuilder()
                    .append(sortColumnsMap.get(SortColumns.TRNSYMB)).append(" ").append(sortTypesMap.get(SortTypes.ASC))
                    .append(", ").append(sortColumnsMap.get(SortColumns.FROMOS)).append(" ").append(sortTypesMap.get(SortTypes.ASC))
                    .append(", ").append(sortColumnsMap.get(SortColumns.TOOS)).append(" ").append(sortTypesMap.get(SortTypes.ASC))
                    .append(", ").append(sortColumnsMap.get(SortColumns.TRNTYPE)).append(" ").append(sortTypesMap.get(SortTypes.ASC))
                    .append(", ").append(sortColumnsMap.get(SortColumns.NUMTRAINS)).append(" ").append(sortTypesMap.get(SortTypes.ASC))
                    .toString();
        }
        else {
            orderBy = orderByInitial;
        }
        return orderBy;
    }
}
