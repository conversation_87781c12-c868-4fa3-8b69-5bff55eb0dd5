package com.nscorp.ccp.dao.impl.job;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.job.Job;
import com.nscorp.ccp.common.job.JobSummary;
import com.nscorp.ccp.dao.job.JobDAO;
import com.nscorp.ccp.utils.commonTypes.JobStatus;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Slf4j
@Repository
@RequiredArgsConstructor
class JobDAOImpl implements JobDAO {
	private final JobRepository jobRepository;
	private final JobSummaryRepository jobsummaryRepository;

	@Repository
	interface JobSummaryRepository extends CrudRepository<JobSummary,Long> {
		@Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job order by job_id")
		Iterable<JobSummary> getAllJobSummaries();

		@Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job where status=:status order by job_id")
		Iterable<JobSummary> getJobSummariesByStatus(@Param("status") JobStatus status);

		@Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job where scenario_id=:scenarioId order by job_id")
		Iterable<JobSummary> getJobSummariesByScenarioId(@Param("scenarioId") long scenarioId);

		@Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job where jobmgr_job_id=:jobmgrJobId and scenario_id=:scenarioId")
			// @Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job where jobmgr_job_id=:jobmgrJobId")
		Iterable<JobSummary> getJobSummaryByJobmgrJobId(@Param("jobmgrJobId") Long jobmgrJobId, @Param("scenarioId") Long scenarioId);

		@Query("select job_id, hub_id, scenario_id, status, jobmgr_job_status, jobmgr_job_id from job where job_id=:jobId")
		JobSummary getJobSummaryByJobId(@Param("jobId") Long jobId);
	}
	@Repository
	interface JobRepository extends CrudRepository<Job,Long> {
		Job findByJobId(long jobId);

		@Query("delete from job where scenario_id = :scenarioId")
		@Modifying
		Long deleteByScenarioId(@Param("scenarioId") long scenarioId);

		@Query("update job set status=:status where job_id=:jobId")
		@Modifying
		void setJobStatus(@Param("jobId") long jobId, @Param("status") JobStatus status);
	}

	@Override
	public void setJobStatus(long jobId, JobStatus status) {
		jobRepository.setJobStatus(jobId, status);
	}

	@Override public JobSummary getJobSummaryByJobId(long jobId) {
		return jobsummaryRepository.getJobSummaryByJobId(jobId);
	}

	@Override
	public void deleteJobById(long jobId) {
		jobRepository.deleteById(jobId);
	}

	@Override public Long deleteJobsByScenarioId(long scenarioId) {
		return jobRepository.deleteByScenarioId(scenarioId);
	}

	@Override public JobSummary getJobSummaryByJobmgrJobId(long jobmgrJobId, long scenarioId) {
		val jobSummaries = ImmutableList.copyOf(jobsummaryRepository.getJobSummaryByJobmgrJobId(jobmgrJobId, scenarioId));

		final JobSummary result;
		if (jobSummaries.isEmpty()) {
			log.warn(String.format("getJobSummaryByJobmgrJobId(): no match for jobmgrJobId=%s, scenarioId=%s.  Returning null", jobmgrJobId, scenarioId));
			result = null;
		}
		else if ( jobSummaries.size() > 1 ) {
			// This should never happen.  We just include a warning here in case we have multiple matches, since migrating from
			//      the Prod to DR system will result in Job Manager resetting the job ID's to 0 for newly-created jobs.
			//      Thus, the jobmgrJobId could match to more than one job.  However, the combination of (jobmgrJobId,scenarioId)
			//      should always be unique.  We log this case just in case it happens, so we can identify why we are getting more
			//      than one match.
			log.warn(String.format(
					"%d matches were found for jobMgrJobId=%s, scenarioId=%s.  Taking only job #%s.",
					jobSummaries.size(),
					jobmgrJobId,
					scenarioId,
					jobSummaries.get(0).getJobId()));
			result = jobSummaries.get(0);
		}
		else {
			result = jobSummaries.get(0);
		}
		return result;
	}

	@Override public Iterable<JobSummary> getJobSummariesByStatus(JobStatus status) {
		return jobsummaryRepository.getJobSummariesByStatus(status);
	}

	@Override public Iterable<JobSummary> getAllJobSummaries() {
		return jobsummaryRepository.getAllJobSummaries();
	}

	@Override public Job getJobById(long jobId) {
		return Optional.ofNullable(jobRepository.findByJobId(jobId)).
				orElse(null);
	}

	@Override public Job save(@NonNull Job job) {
		return Optional.of(job).
				map(jobRepository::save).
				orElse(null);
	}

	@Override public Iterable<JobSummary> getJobSummariesByScenarioId(long scenarioId) {
		return jobsummaryRepository.getJobSummariesByScenarioId(scenarioId);
	}
}
