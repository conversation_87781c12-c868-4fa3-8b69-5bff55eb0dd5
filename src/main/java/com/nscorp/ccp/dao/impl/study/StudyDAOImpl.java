package com.nscorp.ccp.dao.impl.study;
import com.nscorp.ccp.common.opd.OpdPlan;
import io.vavr.Function0;
import lombok.val;
import static com.google.common.collect.ImmutableSet.*;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.*;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.dao.study.StudyDAO;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.google.common.collect.Iterables.transform;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.dao.impl.study.StudyRepositories.*;
import static io.vavr.Function0.constant;

@Repository
@Slf4j
@RequiredArgsConstructor
class StudyDAOImpl implements StudyDAO {
	private final StudyRepository studyRepository;
	private final TrainRepository trainRepository;
	private final LineSegmentRepository lineSegmentRepository;

	@Override public Train getTrainByStudyIdAndTrainId(
			long studyId,
			long trainId) {
		return trainRepository.findById(trainId).
				filter(train ->train.getStudyId().equals(studyId)).
				orElse(null);
	}

	@Override public Iterable<Train> getTrainsByStudyIdAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(
			long studyId,
			String trnCrewDistr,
			String fromOs,
			String toOs) {
		return trainRepository.findByStudyIdAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(studyId, trnCrewDistr, fromOs, toOs);
	}

	@Override public Iterable<Train> getTrainsByStudyIdAndTrnSymbAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(
			long studyId,
			String trnSymb,
			String trnCrewDistr,
			String fromOs,
			String toOs) {
		return trainRepository.findByStudyIdAndTrnSymbAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(studyId, trnSymb, trnCrewDistr, fromOs, toOs);
	}

	@Override public Long deleteTrainsByStudyIdTrnSymbTrnCrewDistrFromOsAndToOs(
			long studyId,
			String trnSymb,
			String trnCrewDistr,
			String fromOs,
			String toOs) {

		val trains = ImmutableList.copyOf(getTrainsByStudyIdAndTrnSymbAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(studyId, trnSymb, trnCrewDistr, fromOs, toOs));
		trains.forEach(t -> deleteTrainById(t.getTrainId()));
		return (long) trains.size();
	}

	@Override public Long deleteTrainsByStudyIdTrnCrewDistrFromOsAndToOs(
			long studyId,
			String trnCrewDistr,
			String fromOs,
			String toOs) {
		val trains = ImmutableList.copyOf(getTrainsByStudyIdAndTrnCrewDistrAndFromOsAndToOsOrderByTrainId(studyId, trnCrewDistr, fromOs, toOs));
		trains.forEach(t -> deleteTrainById(t.getTrainId()));
		return (long) trains.size();
	}

	@Override public Long deleteTrainByStudyIdAndTrainId(long studyId, long trainId) {
		return Optional.ofNullable(getTrainByStudyIdAndTrainId(studyId, trainId)).
				map(train-> {
					deleteTrainById(train.getTrainId());
					return 1L;
				}).
				orElse(0L);
	}

	@Override public void deleteTrainById(long trainId) {
		trainRepository.deleteById(trainId);
	}

	@Override
	public Iterable<LineSegment> getLineSegmentsByStudyId(Long studyId) {
		return lineSegmentRepository.findByStudyId(studyId);
	}

	@Override
	public Study getStudyById(long studyId) {
		return studyRepository.findById(studyId).orElse(null);
	}

	@Override
	public Iterable<Study> getAllStudies() {
		return studyRepository.getAllByOrderByStudyId();
	}

	@Override
	public Iterable<Study> getStudiesByStatus(StudyStatus status) {
		return studyRepository.findByStatus(status);
	}

	@Override
	public Iterable<Study> getStudiesByType(StudyType studyType) {
		return studyRepository.findByStudyType(studyType);
	}

	@NonNull @Override public Study save(@NonNull Study study) {
		return constant(study).
				andThen(studyRepository::save).
				apply();
	}

	@Override public Train save(@NonNull Train train) {
		return constant(train).
				andThen(trainRepository::save).
				apply();
	}

	@Override public Long deleteChunkOfTrainsByStudyId(long studyId) {
		val result = trainRepository.deleteChunkByStudyId(studyId);
		if ( log.isInfoEnabled() ) {
			info(log, String.format("Deleting chunk of %d trains from study %d.", result, studyId));
		}
		return result;
	}

	@Override public Long deleteStudyById(long studyId) {
		return studyRepository.deleteByStudyId(studyId);
	}

	@Override public Iterable<Train> getTrainsByStudyId(long studyId) {
		return trainRepository.findByStudyIdOrderByTrainId(studyId);
	}

	@Override public CompleteStudy getCompleteStudyById(long studyId) {
		return Optional.ofNullable(getStudyById(studyId)).
				map(study-> {
					val trains = copyOf(getTrainsByStudyId(studyId));
					return CompleteStudy.builder().study(study).trains(trains).build();
				} ).
				orElse(null);
	}

	@Override public long deleteTrainsByStudyIdAndHubName(long studyId, String hubName) {
		if ( log.isWarnEnabled() ) {
			warn(log, String.format("Deleting hub %s of study #%d.", hubName, studyId));
		}
		return trainRepository.deleteByStudyIdAndHubName(studyId, hubName);
	}

	@Override
	public Iterable<Train> saveTrainsToStudy(long studyId, Iterable<Train> trains) {
		return stream(trains).
				map(e -> e.withStudyId(studyId)).
				map(this::save).
				collect(toImmutableSet());
	}
}
