package com.nscorp.ccp.dao.impl.monitor;

import com.nscorp.ccp.common.monitor.MonitorLog;
import com.nscorp.ccp.dao.monitor.MonitorDAO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
@Slf4j
class MonitorDAOImpl implements MonitorDAO {
	private final MonitorLogRepository repository;
	@Override public MonitorLog save(MonitorLog monitorLog) {
		return repository.save(monitorLog);
	}

	@Repository
	interface MonitorLogRepository extends CrudRepository<MonitorLog,Long> {
	}
}
