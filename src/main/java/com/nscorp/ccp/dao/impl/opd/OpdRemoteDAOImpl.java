package com.nscorp.ccp.dao.impl.opd;
import java.util.*;
import lombok.val;
import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.*;
import com.nscorp.ccp.common.opd.*;
import com.nscorp.ccp.dao.opd.OpdRemoteDAO;
import com.nscorp.ccp.integration.opd.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static com.google.common.collect.Iterables.transform;
import static org.apache.commons.lang3.StringUtils.*;

@Repository
@Slf4j
@RequiredArgsConstructor
class OpdRemoteDAOImpl implements OpdRemoteDAO {
	@Value("#{'${opdTrnTypes}'.split(',')}")
	private List<String> opdTrnTypes;
	private final OpdClient client;
	private final OpdJsonMapper mapper;
	@Override public Iterable<OpdScenario> getOpdScenarios() {
		return transform(client.getOpdScenarios(), mapper::jsonToDto);
	}
	@Override public Iterable<OpdPlan> getOpdPlans() {
		return transform(client.getOpdPlans(), mapper::jsonToDto);
	}
	private Iterable<OpdTrain> getOpdTrains(OpdScenOrPlanSa sa, LocalDate startDt, LocalDate endDt) {
		val opdTrains = client.getOpdTrains(sa, startDt, endDt);
		val jsonOpdTrains = stream(opdTrains).
				map(this::trimFields).
				collect(toImmutableList());
		return transform(jsonOpdTrains, mapper::jsonToDto);
	}
	private JsonOpdTrain trimFields(JsonOpdTrain jsonOpdTrain) {
		return jsonOpdTrain.toBuilder().
			fromOs(trim(jsonOpdTrain.getFromOs())).
			toOs(trim(jsonOpdTrain.getToOs())).
			fromRd(trim(jsonOpdTrain.getFromRd())).
			lnSeg(trim(jsonOpdTrain.getLnSeg())).
			trnType(trim(jsonOpdTrain.getTrnType())).
			trnRte(trim(jsonOpdTrain.getTrnRte())).
			trnSa(trim(jsonOpdTrain.getTrnSa())).
			toRd(trim(jsonOpdTrain.getToRd())).
			build();
	}

	static Iterable<LocalDate> getDates(LocalDate fromDt, LocalDate toDt) {
		return Stream.iterate(fromDt, dt->dt.compareTo(toDt) <= 0, dt->dt.plusDays(1)).collect(toImmutableList());
	}

	@Override public Iterable<OpdTrain> getTrains(OpdScenOrPlanSa sa, LocalDate fromDt, LocalDate toDt, Set<String> trnTypes) {
		return stream(getDates(fromDt, toDt)).flatMap(dt -> getTrainsForOneDate(sa, dt, trnTypes).stream()).collect(toImmutableList());
	}

	@Override public Set<String> getOpdTrnTypes() {
		return ImmutableSet.copyOf(opdTrnTypes);
	}

	private Set<String> adjustTrnTypes(Set<String> trnTypes) {
		return trnTypes.isEmpty() ? getOpdTrnTypes() : trnTypes;
	}
	private List<OpdTrain> getTrainsForOneDate(
			final OpdScenOrPlanSa sa,
			final LocalDate dt,
			final Set<String> trnTypes) {
		if ( log.isInfoEnabled() ) {
			info(log, String.format("Issuing request for date %s for SA %s.", dt, sa));
		}
		val opdTrains = copyOf(getOpdTrains(sa, dt, dt));

		// Filter by train type, convert to OpdTrain's, and return.
		val adjustedTrnTypes = adjustTrnTypes(trnTypes);
		return opdTrains.stream().
				filter(e->adjustedTrnTypes.contains(e.getTyp())).
				collect(toImmutableList());
	}
}
