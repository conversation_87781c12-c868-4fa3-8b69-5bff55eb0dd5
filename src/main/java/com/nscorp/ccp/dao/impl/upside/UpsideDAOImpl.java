package com.nscorp.ccp.dao.impl.upside;
import lombok.*;

import static com.google.common.collect.ImmutableList.*;
import static com.google.common.collect.Iterables.*;
import static com.google.common.collect.Streams.*;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;
import static com.nscorp.ccp.utils.logging.LogUtils.*;
import static io.vavr.Function0.constant;

import com.nscorp.ccp.common.upside.*;
import com.nscorp.ccp.dao.upside.UpsideDAO;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Repository
@Slf4j
@RequiredArgsConstructor
class UpsideDAOImpl implements UpsideDAO {
	private final UpsideTrainRepository trainRepository;
	private final UpsideRepository upsideRepository;

	@Override public Iterable<Upside> getAllUpsides() {
		return upsideRepository.findAll();
	}

	@Override public Iterable<UpsideTrain> save(long upsideId, Instant loadTs, Iterable<UpsideTrain> trains) {
		trainRepository.deleteByUpsideId(upsideId);
		val savedTrains = stream(trains).
				map(train->train.withUpsideId(upsideId)).
				map(train ->train.withLoadTs(loadTs)).
				map(this::save).
				collect(toImmutableList());
		if ( log.isWarnEnabled() ) {
			savedTrains.forEach(train -> warn(log, String.format("train=%s", train)));
		}
		return savedTrains;
	}

	@NonNull@Override public UpsideAndTrains save(@NonNull UpsideAndTrains upsideAndTrains) {
		val upside = save(upsideAndTrains.getUpside());
		val trains = save(upside.getUpsideId(), upside.getLoadTs(), upsideAndTrains.getTrains());
		return UpsideAndTrains.builder().
			upside(upside).
			trains(imlist(trains)).
			build();
	}

	@NonNull @Override public Upside save(@NonNull Upside upside) {
		return constant(upside).
				andThen(upsideRepository::save).
				apply();
	}

	@Override public void deleteUpsideById(long upsideId) {
		trainRepository.deleteByUpsideId(upsideId);
		upsideRepository.deleteById(upsideId);
	}

	@Override public Iterable<Upside> getUpsides() {
		return upsideRepository.findAll();
	}

	@NonNull @Override public UpsideTrain save(@NonNull UpsideTrain train) {
		return constant(train).
				andThen(trainRepository::save).
				apply();
	}

	@Override public Upside getUpsideById(long upsideId) {
		return upsideRepository.findById(upsideId).
				orElse(null);
	}

	@Override public Iterable<UpsideTrain> getTrains(Long upsideId) {
		return trainRepository.findAllByUpsideIdOrderByUpsideTrainId(upsideId);
	}

	interface UpsideRepository extends CrudRepository<Upside,Long> {
	}

	interface UpsideTrainRepository extends CrudRepository<UpsideTrain,Long> {
		Iterable<UpsideTrain> findAllByUpsideIdOrderByUpsideTrainId(Long upsideId);

		@Query("delete from upside_train where upside_id=:upsideId")
		@Modifying
		Long deleteByUpsideId(Long upsideId);
	}
}
