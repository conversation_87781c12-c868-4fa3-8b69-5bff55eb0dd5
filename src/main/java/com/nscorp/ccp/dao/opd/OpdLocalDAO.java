package com.nscorp.ccp.dao.opd;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.opd.*;
import com.nscorp.ccp.common.opd.OpdScenOrPlanSa;

import java.time.LocalDate;

public interface OpdLocalDAO {
	long deleteChunkByScenSa(Integer scenSa);
	long deleteChunkByPlanSa(Integer planSa);
	long deleteOpdScenariosByScenSa(Integer scenSa);
	long deleteOpdPlansByPlanSa(Integer planSa);
	OpdScenario save(OpdScenario scenario);
	OpdPlan save(OpdPlan plan);
	OpdTrain save(OpdTrain train);
	Iterable<OpdScenario> getOpdScenarios();
	Iterable<OpdPlan> getOpdPlans();
	Iterable<OpdTrain> getOpdTrains(OpdScenOrPlanSa sa, LocalDate startDt, LocalDate endDt);
}
