package com.nscorp.ccp.dao.batchRun;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.batchRun.BatchRun;
import com.nscorp.ccp.common.batchRun.BatchRunType;

import java.time.Instant;

public interface BatchRunDAO {
	long deleteByBatchRunId(long batchRunId);
	Iterable<BatchRun> getRecentBatchRuns(Instant cutoff);
	Iterable<BatchRun> getMostRecentRunsOfType(BatchRunType type);
	BatchRun getMostRecentRunOfType(BatchRunType type);
	BatchRun getLastSuccessfulRunOfType(BatchRunType type);
	BatchRun save(BatchRun batchRun);
}
