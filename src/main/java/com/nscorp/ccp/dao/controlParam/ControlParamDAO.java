package com.nscorp.ccp.dao.controlParam;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.controlParam.ControlParam;

public interface ControlParamDAO {
	Long deleteControlParamByKey(String key);
	ControlParam save(ControlParam controlParam);
	Iterable<ControlParam> getAllControlParams();
	ControlParam getControlParamByKey(String key);
}
