package com.nscorp.ccp.dao.historicalTrain;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.historicalTrain.HistoricalTrain;

import java.time.LocalDate;

public interface HistoricalTrainDAO {
	Iterable<Phase2QueryResult> executePhase2Query(Iterable<TrainCategoryKey> trainCategoryKeys);
	Iterable<HistoricalTrain> getPhase1Trains(LocalDate startDate, LocalDate endDate);

}
