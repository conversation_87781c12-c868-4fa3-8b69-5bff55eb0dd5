package com.nscorp.ccp.dao.crewpro;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.crewpro.CrewProGroup;

public interface CrewProGroupDAO {
	CrewProGroup getLatestGroup();
	CrewProGroup deleteGroupById(long id);
	CrewProGroup getGroupById(long id);
	CrewProGroup getGroupByName(String name);
	Iterable<CrewProGroup> getAllGroups();
	CrewProGroup save(CrewProGroup crewProGroup);
}
