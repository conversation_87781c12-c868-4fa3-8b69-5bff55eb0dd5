package com.nscorp.ccp.config;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.controlParam.ControlParam;
import com.nscorp.ccp.common.controlParam.ControlParams;
import com.nscorp.ccp.biz.controlParam.*;
import com.nscorp.ccp.utils.logging.LogLevelAdjuster;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@Profile("svc")
@Slf4j
@RequiredArgsConstructor
@EnableScheduling
public class ServiceConfiguration {
	private final ControlParamWriter controlParamWriter;
	private final ControlParamReader controlParamReader;
	private final LogLevelAdjuster logLevelAdjuster;
	@Scheduled(cron = "0,10,20,30,40,50 * * * * ?")
	public void adjustLogLevel() {
		final ControlParam param = controlParamReader.getControlParamByKey(ControlParams.SVC_LOG_LEVEL);
		if ( param != null ) {
			logLevelAdjuster.adjust("com.nscorp", param.getParamValue());
			logLevelAdjuster.adjust(Logger.ROOT_LOGGER_NAME, param.getParamValue());
		}
	}
	@Scheduled(cron = "0 * * * * ?")
	public void checkForExit() {
		final String paramName = ControlParams.SVC_EXIT_REQUESTED;
		boolean value = controlParamWriter.checkAndSetFalse(paramName);
		if ( value ) {
			error(log, String.format("Exiting due to '%s' property.", paramName));
			Runtime.getRuntime().halt(1);
		}
	}
}
