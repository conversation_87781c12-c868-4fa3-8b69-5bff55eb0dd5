package com.nscorp.ccp.logic.crewpro;

import com.nscorp.ccp.common.crewpro.CrewProJobFiles;
import com.nscorp.ccp.logic.modelJson.ModelJsonRequestParser;
import com.nscorp.ccp.logic.modelJson.ModelJsonResponseParser;
import com.nscorp.ccp.utils.lang.Pure;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Map;
import java.util.Optional;

import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;

@Slf4j
@Pure
public class CrewProJobFileCollectorLogic {
	private final ModelJsonRequestParser requestParser = new ModelJsonRequestParser(); 
	private final ModelJsonResponseParser responseParser = new ModelJsonResponseParser(); 

	@Value
	@Builder
	public static class NameAndContents {
		File name;
		String contents;
	}

	@Value
	static class ReqRespMaps {
		Map<String,File> requestMap;
		Map<String,File> responseMap;
	}

	private ReqRespMaps buildRequestResponseMaps(final Iterable<NameAndContents> files) {
		val filesList = imlist(files);

		// Attempt to parse each file as a request.  If successful, add the file's request ID as
		//      a key and the file's name as a value in the 'requestsByRequestId' map.
		val requestsByRequestId = filesList.stream().
				flatMap(file-> Try.of(()->requestParser.parseRequest(file.contents)).
						map(req-> Tuple.of(req.getRequestInfo().getRequestId(), file.name)).
						toJavaStream()).
				collect(toImmutableMap(Tuple2::_1, Tuple2::_2));

		// Attempt to parse each file as a response.  If successful, add the file's request ID as
		//      a key and the file's name as a value in the 'responsesByRequestId' map.
		val responsesByRequestId = filesList.stream().
				flatMap(file-> Try.of(()->responseParser.parseResponse(file.contents)).
						map(resp-> Tuple.of(resp.getResponseInfo().getRequestId(), file.name)).
						toJavaStream()).
				collect(toImmutableMap(Tuple2::_1, Tuple2::_2));
		return new ReqRespMaps( requestsByRequestId, responsesByRequestId);
	}

	Iterable<CrewProJobFiles> buildPairs(final ReqRespMaps maps) {
		// For each legal request/response pair, build a CrewProJobFiles object.
		return maps.getRequestMap().entrySet().stream().
				flatMap(ent-> {
					val requestId = ent.getKey();
					val requestFile = ent.getValue();
					return Optional.ofNullable(maps.getResponseMap()
							.get(requestId)).stream().
							map(responseFile->buildJobFiles(requestFile, responseFile));
				}).
				collect(toImmutableList());
	}

	private static CrewProJobFiles buildJobFiles(final File requestFile, final File responseFile) {
		return CrewProJobFiles.builder().
				requestFile(requestFile).
				responseFile(responseFile).
				build();
	}

	public Iterable<CrewProJobFiles> collectFiles(final Iterable<NameAndContents> files) {
		return buildPairs(buildRequestResponseMaps(files));
	}
}
