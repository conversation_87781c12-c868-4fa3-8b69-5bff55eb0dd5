package com.nscorp.ccp.logic.simulation;

import com.nscorp.ccp.common.job.JobSummary;
import com.nscorp.ccp.common.scenario.Scenario;
import com.nscorp.ccp.common.scenario.ScenarioStatus;
import com.nscorp.ccp.utils.commonTypes.JobStatus;
import com.nscorp.ccp.utils.lang.Pure;
import com.nscorp.ieorcommons.collection.GroupingUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Pure
public class ScenarioStatusUpdaterLogic {
	@Value
	@Builder
	public static class Result {
		boolean update;
		Scenario updatedScenario;
	}
	@NonNull public Result updateStatus(
			@NonNull final Scenario scenario,
			@NonNull final Iterable<JobSummary> jobs) {

		// Group the jobs by status.
		val jobsByStatus = GroupingUtils.groupBy(jobs, JobSummary::getStatus);

		// Compute the number of jobs in each of the statuses.
		val error = jobsByStatus.get(JobStatus.RUN_SUCCEEDED_ERROR_RESULT).size();
		val resultsLoaded = jobsByStatus.get(JobStatus.RESULTS_LOADED).size();
		val running = jobsByStatus.get(JobStatus.RUNNING).size();
		val submitted = jobsByStatus.get(JobStatus.SUBMITTED).size();
		val created = jobsByStatus.get(JobStatus.CREATED).size();
		val pending = jobsByStatus.get(JobStatus.PENDING).size();

		final Result result;
		if ( created + submitted + pending + running > 0 ) {
			// This scenario still has jobs that are either running or in a pre-running status.
			//      Therefore we can't complete the scenario yet.
			result = Result.builder().update(false).updatedScenario(null).build();
		}
		else {
			// We can complete this scenario.  Set its status to COMPLETE if successful, FAILED otherwise.
			val newStatus = resultsLoaded > 0 ? ScenarioStatus.COMPLETE : ScenarioStatus.FAILED;
			val updatedScenario = scenario.withStatus(newStatus);
			result = Result.builder().update(true).updatedScenario(updatedScenario).build();
		}
		return result;
	}
}
