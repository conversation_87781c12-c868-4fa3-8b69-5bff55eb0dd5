package com.nscorp.ccp.logic.simulation;

import lombok.val;

import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.common.hub.TrainHubAssignmentResult;
import com.nscorp.ccp.common.plan.CrewProfile;
import com.nscorp.ccp.common.plan.ExbPoolSetUp;
import com.nscorp.ccp.common.study.HubAssignmentInput;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.logic.hub.TrainHubAssignmentEngine;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import static com.google.common.collect.ImmutableList.copyOf;
import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.google.common.collect.Iterables.transform;
import com.nscorp.ieorcommons.collection.*;
import com.nscorp.ieorcommons.guava.*;

@RequiredArgsConstructor
@Slf4j
@Pure
public class TrainHubAssigner {
	private final TrainHubAssignmentEngine engine = new TrainHubAssignmentEngine(); 

	public Iterable<Train> assignHubs(
		final Iterable<Train> trainsIt,
		final Iterable<CrewProfile> crewProfiles,
		final Iterable<ExbPoolSetUp> exbPoolSetUps) {
		val trains = ImmutableCollectionUtils.imlist(trainsIt);

		// Map the trains by key.
		val trainsByKey = ImmutableCollectionUtils.iterableToImmutableMap(trains, Train::getFullTrainKey);

		// Assign the hubs and return.
		val inputs = transform(trains, e-> HubAssignmentInput.builder().
				fullTrainKey(e.getFullTrainKey()).
				assignFlag(e.getAssignFlag()).
				profileId(e.getProfileId()).
				crewOrgnOs(e.getCrewOrgnOs()).
				coExbKey(e.getCoExbKey()).
				enExbKey(e.getEnExbKey()).
				build());
		val hubAssignmentResults = engine.assignHubs(inputs, crewProfiles, exbPoolSetUps);

		if ( log.isWarnEnabled() ) {
			hubAssignmentResults.forEach(hubAssignmentResult ->
				warn(log, String.format("hubAssignmentResult: %s", hubAssignmentResult)));
		}

		// Apply the hub assignments and return.
		return ImmutableCollectionUtils.mapToImmutableList(
				hubAssignmentResults,
				hubAssignmentResult -> assignHub(trainsByKey.get(hubAssignmentResult.getTrain().getFullTrainKey()), hubAssignmentResult));
	}

	@NonNull private Train assignHub(@NonNull Train train, @NonNull TrainHubAssignmentResult hubAssignmentResult) {
		return train.
				withHubId(hubAssignmentResult.getHubId()).
				withHubAssignmentReasonCode(hubAssignmentResult.getHubAssignmentReasonCode());
	}
}
