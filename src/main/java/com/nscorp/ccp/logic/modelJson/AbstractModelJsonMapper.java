package com.nscorp.ccp.logic.modelJson;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.common.modelJson.*;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.common.scenario.ScenarioCfg;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.utils.lang.Pure;
import com.nscorp.ieorcommons.time.NSDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.*;

import java.time.LocalDate;
import java.time.LocalTime;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = AbstractModelJsonMapper.class)
@Slf4j
@Pure
public abstract class AbstractModelJsonMapper implements ModelJsonMapper {
	@Override public abstract Turn jsonToDto(ModelJsonPlanTurnsInPool json);
	@Override public abstract ModelJsonPlanTurnsInPool dtoToJson(Turn dto);

	@Override public abstract ExbTurn jsonToDto(ModelJsonPlanExbTurnsInPool json);
	@Override public abstract ModelJsonPlanExbTurnsInPool dtoToJson(ExbTurn dto);

	@Mapping(target = "optimizeSizeCo", expression = "java(integerToBoolean(json.getOptimizeSizeCo()))")
	@Mapping(target = "optimizeSizeEn", expression = "java(integerToBoolean(json.getOptimizeSizeEn()))")
	@Override public abstract Pool jsonToDto(ModelJsonPlanPool json);

	@Mapping(target = "optimizeSizeCo", expression = "java(booleanToInteger(dto.getOptimizeSizeCo()))")
	@Mapping(target = "optimizeSizeEn", expression = "java(booleanToInteger(dto.getOptimizeSizeEn()))")
	@Override public abstract ModelJsonPlanPool dtoToJson(Pool dto);

	@Override public abstract PoolSetUp jsonToDto(ModelJsonPlanPoolSetUp json);
	@Override public abstract ModelJsonPlanPoolSetUp dtoToJson(PoolSetUp dto);

	@Override public abstract Extraboard jsonToDto(ModelJsonPlanExtraboard json);

	@Override public abstract ModelJsonPlanExtraboard dtoToJson(Extraboard dto);

	@Mapping(target = "optimizeSize", expression = "java(integerToBoolean(json.getOptimizeSize()))")
	@Override public abstract ExbPoolSetUp jsonToDto(ModelJsonPlanExbPoolSetUp json);
	@Mapping(target = "optimizeSize", expression = "java(booleanToInteger(dto.getOptimizeSize()))")
	@Override public abstract ModelJsonPlanExbPoolSetUp dtoToJson(ExbPoolSetUp dto);

	@Mapping(target = "calDayBegn", expression = "java(jsonToLocalTime(json.getCalDayBegn()))")
	@Mapping(target = "calDayEnd", expression = "java(jsonToLocalTime(json.getCalDayEnd()))")
	@Mapping(target = "profStrtDate", expression = "java(jsonToLocalDate(json.getProfStrtDate()))")
	@Mapping(target = "profEndDate", expression = "java(jsonToLocalDate(json.getProfEndDate()))")

	@Override public abstract WorkRestProf doJsonToDto(ModelJsonPlanWorkRestProf json);
	@Override public WorkRestProf jsonToDto(ModelJsonPlanWorkRestProf json) {
		return doJsonToDto(json).withRotations(ImmutableSet.of()).withTurnGroups(ImmutableSet.of());
	}

	@Mapping(target = "calDayBegn", expression = "java(localTimeToJson(dto.getCalDayBegn()))")
	@Mapping(target = "calDayEnd", expression = "java(localTimeToJson(dto.getCalDayEnd()))")
	@Mapping(target = "profStrtDate", expression = "java(localDateToJson(dto.getProfStrtDate()))")
	@Mapping(target = "profEndDate", expression = "java(localDateToJson(dto.getProfEndDate()))")
	@Override public abstract ModelJsonPlanWorkRestProf dtoToJson(WorkRestProf dto);

	@Override public abstract ModelJsonPlanWorkRestProfTGrp dtoToJson(WorkRestProfTGrp dto);
	@Override public abstract WorkRestProfTGrp jsonToDto(ModelJsonPlanWorkRestProfTGrp json);

	@Override public abstract ModelJsonPlanWorkRestProfRotn dtoToJson(WorkRestProfRotn dto);
	@Override public abstract WorkRestProfRotn jsonToDto(ModelJsonPlanWorkRestProfRotn json);

	@Override public abstract ModelJsonPlanIdPools dtoToJson(IdPool dto);
	@Override public abstract IdPool jsonToDto(ModelJsonPlanIdPools json);

	@Override public abstract ModelJsonPlanSelfSustaining dtoToJson(SelfSustaining dto);

	@Override public abstract SelfSustaining jsonToDto(ModelJsonPlanSelfSustaining json);

	/*
	public abstract TieUpException doJsonToDto(ModelJsonPlanTieUpException json);

	public TieUpException jsonToDto(ModelJsonPlanTieUpException json) {
		final TieUpException dto = doJsonToDto(json);
		return TieUpExceptionAfterMapping.afterMapping(json, dto);
	}

	 */

	@Override public abstract ModelJsonBoardSummary dtoToJson(com.nscorp.ccp.common.scenario.BoardSummary dto);
	@Override public abstract com.nscorp.ccp.common.scenario.BoardSummary jsonToDto(ModelJsonBoardSummary json);

	@Override public abstract ModelJsonBoardTrainStarts dtoToJson(com.nscorp.ccp.common.scenario.BoardTrainStarts dto);
	@Override public abstract com.nscorp.ccp.common.scenario.BoardTrainStarts jsonToDto(ModelJsonBoardTrainStarts json);

	@Override public abstract ModelJsonTurnUtilization dtoToJson(com.nscorp.ccp.common.scenario.TurnUtilization dto);
	@Override public abstract com.nscorp.ccp.common.scenario.TurnUtilization jsonToDto(ModelJsonTurnUtilization json);

	@Override public abstract ModelJsonSimulationTrainOutput dtoToJson(com.nscorp.ccp.common.scenario.SimulationTrainOutput dto);
	@Override public abstract com.nscorp.ccp.common.scenario.SimulationTrainOutput jsonToDto(ModelJsonSimulationTrainOutput json);

	@Mapping(target = "rule0ReturnTime", expression = "java(jsonToLocalTime(json.getRule0ReturnTime()))")
	@Mapping(target = "rule1ReturnTime", expression = "java(jsonToLocalTime(json.getRule1ReturnTime()))")
	@Override public abstract ScenarioCfg jsonToDto(ModelJsonScenarioConfig json);

	@Mapping(target = "rule0ReturnTime", expression = "java(localTimeToJson(dto.getRule0ReturnTime()))")
	@Mapping(target = "rule1ReturnTime", expression = "java(localTimeToJson(dto.getRule1ReturnTime()))")
	@Override public abstract ModelJsonScenarioConfig dtoToJson(ScenarioCfg dto);

	@Override public abstract CrewProfile jsonToDto(ModelJsonPlanCrewProfile json);
	@Override public abstract ModelJsonPlanCrewProfile dtoToJson(CrewProfile dto);

	@Override public abstract PoolRotationH jsonToDto(ModelJsonPlanPoolRotationH json);
	@Override public abstract ModelJsonPlanPoolRotationH dtoToJson(PoolRotationH dto);

	@Override public ModelJsonStudyTrnInput dtoToJson(Train dto) {
		val json = generatedDtoToJson(dto);
		return TrainAfterMapping.afterMapping(dto, json);
	}

	@Override public Train jsonToDto(ModelJsonStudyTrnInput json) {
		final Train dto = generatedJsonToDto(json);
		return TrainAfterMapping.afterMapping(json, dto);
	}
	protected abstract ModelJsonStudyTrnInput generatedDtoToJson(Train dto);
	protected abstract Train generatedJsonToDto(ModelJsonStudyTrnInput json);
	protected static LocalTime jsonToLocalTime(String s) {
		LocalTime result = NSDateUtils.hhmmToLocalTime(s);
		if ( log.isInfoEnabled() ) {
			info(log, String.format("jsonToLocalTime() translated '%s' to %s.  reconverted=%s", s, result, localTimeToJson(result)));
		}
		return result;
	}
	protected static String localTimeToJson(LocalTime localTime) {
		return NSDateUtils.localTimeToHhmm(localTime);
	}
	protected static LocalDate jsonToLocalDate(String s) {
		return NSDateUtils.yyyymmddToLocalDate(s);
	}
	protected static String localDateToJson(LocalDate localDate) {
		return NSDateUtils.localDateToYymmdd(localDate);
	}
	protected static Boolean integerToBoolean(Integer n) {
		return n == null ? null : (n.intValue() == 1);
	}

	protected static Integer booleanToInteger(Boolean b) {
		return b == null ? null : (b.booleanValue() ? 1 : 0);
	}

}
