package com.nscorp.ccp.logic.modelJson;
import lombok.val;

import com.nscorp.ccp.common.modelJson.ModelJsonStudyTrnInput;
import com.nscorp.ccp.common.study.DepartureDistributionParams;
import com.nscorp.ccp.common.study.TransitDistributionParams;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.logic.study.TrnParser;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.experimental.UtilityClass;

@UtilityClass
@Pure
public class TrainAfterMapping {
	static ModelJsonStudyTrnInput afterMapping(
			final Train dto,
			final ModelJsonStudyTrnInput json) {
		val dept = dto.getDepartureDistributionParams();
		val hasDept = dept != null;
		val departureDistribution = hasDept ? dept.getDepartureDistribution() : null;
		val deptLower = hasDept ? dept.getDeptLower() : null;
		val deptLambda1 = hasDept ? dept.getDeptLambda1() : null;
		val deptMean1 = hasDept ? dept.getDeptMean1() : null;
		val deptRate = hasDept ? dept.getDeptRate() : null;
		val deptScale = hasDept ? dept.getDeptScale() : null;
		val deptUpper = hasDept ? dept.getDeptUpper() : null;
		val deptMedium = hasDept ? dept.getDeptMedium() : null;
		val deptShape = hasDept ? dept.getDeptShape() : null;
		val deptVariance1 = hasDept ? dept.getDeptVariance1() : null;
		val deptShiftConstant = hasDept ? dept.getDeptShiftConstant() : null;

		val transit = dto.getTransitDistributionParams();
		val hasTransit = transit != null;
		val transitTimeDistribution = hasTransit ? transit.getTransitTimeDistribution() : null;
		val transitLower = hasTransit ? transit.getTransitLower() : null;
		val transitLambda1 = hasTransit ? transit.getTransitLambda1() : null;
		val transitMean1 = hasTransit ? transit.getTransitMean1() : null;
		val transitRate = hasTransit ? transit.getTransitRate() : null;
		val transitScale = hasTransit ? transit.getTransitScale() : null;
		val transitUpper = hasTransit ? transit.getTransitUpper() : null;
		val transitMedium = hasTransit ? transit.getTransitMedium() : null;
		val transitShape = hasTransit ? transit.getTransitShape() : null;
		val transitVariance1 = hasTransit ? transit.getTransitVariance1() : null;
		val transitShiftConstant = hasTransit ? transit.getTransitShiftConstant() : null;

		return json.toBuilder().
            departureDistribution(departureDistribution).
            transitTimeDistribution(transitTimeDistribution).
            deptLower(deptLower).
            deptLambda1(deptLambda1).
            deptMean1(deptMean1).
            deptRate(deptRate).
            deptScale(deptScale).
            deptUpper(deptUpper).
            deptMedium(deptMedium).
            deptShape(deptShape).
            deptVariance1(deptVariance1).
            deptShiftConstant(deptShiftConstant).
            transitLower(transitLower).
            transitLambda1(transitLambda1).
            transitMean1(transitMean1).
            transitRate(transitRate).
            transitScale(transitScale).
            transitUpper(transitUpper).
            transitMedium(transitMedium).
            transitShape(transitShape).
            transitVariance1(transitVariance1).
            transitShiftConstant(transitShiftConstant).
            assignCoXbDs(dto.getAssignCoXbDistr()).
            assignCoXbSD(dto.getAssignCoXbSubDistr()).
            assignCoXbXB(dto.getAssignCoXbExb()).
            assignEnXbDs(dto.getAssignEnXbDistr()).
            assignEnXbSD(dto.getAssignEnXbSubDistr()).
            assignEnXbXB(dto.getAssignEnXbExb()).
            build();
	}
	static Train afterMapping(final ModelJsonStudyTrnInput json, final Train dto) {
		val parts = new TrnParser().parse(json.getTrn());
		val trnSymb = parts.getTrnSymb();
		val deptParams = DepartureDistributionParams.builder().
				departureDistribution(json.getDepartureDistribution()).
				deptLower(json.getDeptLower()).
				deptLambda1(json.getDeptLambda1()).
				deptMean1(json.getDeptMean1()).
				deptRate(json.getDeptRate()).
				deptScale(json.getDeptScale()).
				deptUpper(json.getDeptUpper()).
				deptMedium(json.getDeptMedium()).
				deptShape(json.getDeptShape()).
				deptVariance1(json.getDeptVariance1()).
				deptShiftConstant(json.getDeptShiftConstant()).build();
		val transitTimeParams = TransitDistributionParams.builder().
				transitTimeDistribution(json.getTransitTimeDistribution()).
				transitLower(json.getTransitLower()).
				transitLambda1(json.getTransitLambda1()).
				transitMean1(json.getTransitMean1()).
				transitRate(json.getTransitRate()).
				transitScale(json.getTransitScale()).
				transitUpper(json.getTransitUpper()).
				transitMedium(json.getTransitMedium()).
				transitShape(json.getTransitShape()).
				transitVariance1(json.getTransitVariance1()).
				transitShiftConstant(json.getTransitShiftConstant()).build();
		val result = dto.
				withDepartureDistributionParams(deptParams).
				withTransitDistributionParams(transitTimeParams).
				toBuilder().
				trnSymb(trnSymb).
				assignCoXbDistr(json.getAssignCoXbDs()).
				assignCoXbSubDistr(json.getAssignCoXbSD()).
				assignCoXbExb(json.getAssignCoXbXB()).
				assignEnXbDistr(json.getAssignEnXbDs()).
				assignEnXbSubDistr(json.getAssignEnXbSD()).
				assignEnXbExb(json.getAssignEnXbXB()).
				build();
		return result;
	}
}
