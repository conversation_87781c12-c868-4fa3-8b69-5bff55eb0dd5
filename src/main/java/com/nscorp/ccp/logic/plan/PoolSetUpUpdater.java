package com.nscorp.ccp.logic.plan;
import lombok.val;

import com.nscorp.ccp.common.plan.PoolSetUp;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ieorcommons.collection.NSCollectionUtils.*;

import io.vavr.collection.*;

@RequiredArgsConstructor
@Slf4j
@Pure
public class PoolSetUpUpdater {
	@Value
	private static class PoolSetUpKey {
		String os;
		int poolHomeAway;
	}

	private PoolSetUpKey psuKey(PoolSetUp psu) {
		return new PoolSetUpKey(psu.getOs(), psu.getPoolHomeAway());
	}

	public Iterable<PoolSetUp> update(
			final Iterable<PoolSetUp> fromRequest,
			final Iterable<PoolSetUp> existing) {
		// Map the 'existing' PoolSetUp's by PoolSetUpKey.
		val psuByKey = Stream.ofAll(existing).toLinkedMap(this::psuKey, Function.identity());

		// Go through all the PoolSetUp's in 'fromRequest'.  For each one, apply the leadTime, regularRestTime, and undistrubedRest fields to the
		//      corresponding PoolSetUp from 'existing'.  If no such PoolSetUp exists, create it.
		val newPsuByKey = foldLeft(fromRequest, psuByKey, (map, requestPsu)-> {
			val key = psuKey(requestPsu);
			return map.get(key).
					map(psu-> {
						val updatedPsu = psu.toBuilder().
								leadTime(requestPsu.getLeadTime()).
								regularRestTime(requestPsu.getRegularRestTime()).
								undisturbedRest(requestPsu.getUndisturbedRest()).
								build();
						return map.put(key, updatedPsu);
					}).
					getOrElse(()->map.put(key, requestPsu));
		});

		// Delete any mappings in newPsuByKey that are not present in requestPsusByKey.
		val requestPsusByKey = Stream.ofAll(fromRequest).toLinkedMap(this::psuKey, Function.identity());
		val psuToRemove = newPsuByKey.keySet().removeAll(requestPsusByKey.keySet());
		val finalMap = newPsuByKey.removeAll(psuToRemove);
		val result = finalMap.values();
		return result;
	}
}
