package com.nscorp.ccp.logic.plan;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.common.plan.ConsolidatedTieUpException;
import com.nscorp.ccp.utils.lang.Pure;
import org.assertj.core.util.Streams;

import java.util.List;

@Pure
public class TieUpExceptionConsolidator {
	public Iterable<ConsolidatedTieUpException> mapExpTieUpExceptions(final Iterable<TieUpException> tieUpExceptions) {
		return Streams.stream(tieUpExceptions).
			flatMap(tue-> tue.getTueSubrules().stream().map(tueSub-> getConsolidatedTieUpException(tue, tueSub))).
			collect(ImmutableList.toImmutableList());
	}

	private static ConsolidatedTieUpException getConsolidatedTieUpException(final TieUpException tue, final TueSubrule tueSub) {
		List<SearchPool> searchPools = tueSub.getSearchPools();
		int searchPoolSize = searchPools.size();
		String sp1Distr = null;
		String sp1SubDistr = null;
		String sp1PoolName = null;
		String sp2Distr = null;
		String sp2SubDistr = null;
		String sp2PoolName = null;
		String sp3Distr = null;
		String sp3SubDistr = null;
		String sp3PoolName = null;
		String sp4Distr = null;
		String sp4SubDistr = null;
		String sp4PoolName = null;
		if (searchPoolSize >= 1) {
			SearchPool sp1 = searchPools.get(0);
			sp1Distr = sp1.getDistr();
			sp1SubDistr = sp1.getSubDistr();
			sp1PoolName = sp1.getPoolName();
		}
		if (searchPoolSize >= 2) {
			SearchPool sp2 = searchPools.get(1);
			sp2Distr = sp2.getDistr();
			sp2SubDistr = sp2.getSubDistr();
			sp2PoolName = sp2.getPoolName();
		}
		if (searchPoolSize >= 3) {
			SearchPool sp3 = searchPools.get(2);
			sp3Distr = sp3.getDistr();
			sp3SubDistr = sp3.getSubDistr();
			sp3PoolName = sp3.getPoolName();
		}
		if (searchPoolSize >= 4) {
			SearchPool sp4 = searchPools.get(3);
			sp4Distr = sp4.getDistr();
			sp4SubDistr = sp4.getSubDistr();
			sp4PoolName = sp4.getPoolName();
		}
		ConsolidatedTieUpException result = ConsolidatedTieUpException.builder()
				.distr(tue.getDistr())
				.subDistr(tue.getSubDistr())
				.craft(tue.getCraft())
				.tueSeqNbr(tue.getTueSeqNbr())
				.fromDistr(tueSub.getFromDistr())
				.fromSubDistr(tueSub.getFromSubDistr())
				.fromPoolName(tueSub.getFromPoolName())
				.toDistr(tueSub.getToDistr())
				.toSubDistr(tueSub.getToSubDistr())
				.toPoolName(tueSub.getToPoolName())
				.tueSubSeqNbr(tueSub.getTueSubSeqNbr())
				.toPoolHomeAway(tueSub.getToPoolHomeAway())
				.sp1Distr(sp1Distr)
				.sp1SubDistr(sp1SubDistr)
				.sp1PoolName(sp1PoolName)
				.sp2Distr(sp2Distr)
				.sp2SubDistr(sp2SubDistr)
				.sp2PoolName(sp2PoolName)
				.sp3Distr(sp3Distr)
				.sp3SubDistr(sp3SubDistr)
				.sp3PoolName(sp3PoolName)
				.sp4Distr(sp4Distr)
				.sp4SubDistr(sp4SubDistr)
				.sp4PoolName(sp4PoolName)
				.build();
		return result;
	}
}
