package com.nscorp.ccp.logic.plan;
import com.google.common.collect.ImmutableSet;
import lombok.*;

import com.nscorp.ccp.common.plan.ExbPoolSetUp;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;

// @RequiredArgsConstructor
@Slf4j
@Pure
public class ExbPoolSetUpUpdaterLogic {
	private final ExtraboardUpdater extraboardUpdater = new ExtraboardUpdater(); 
	private final ExbTurnUpdater exbTurnUpdater = new ExbTurnUpdater(); 
	@NonNull public ExbPoolSetUp update(
			@NonNull final ExbPoolSetUp fromRequest,
			@NonNull final ExbPoolSetUp existing) {
		// FileUtils.writeStringToFile(new File("exbPoolSetUp_fromRequest.txt"), JsonUtils.prettyPrint(fromRequest), "UTF-8");
		// FileUtils.writeStringToFile(new File("exbPoolSetUp_existing.txt"), JsonUtils.prettyPrint(existing), "UTF-8");
		val exbPoolSetUpId = existing.getExbPoolSetUpId();
		val planId = existing.getPlanId();

		// Update the ExbTurn's
		val exbTurns = exbTurnUpdater.update(fromRequest.getExbTurns(), existing.getExbTurns());

		// Update the Extraboard's
		val extraboards = extraboardUpdater.update(fromRequest.getExtraboards(), existing.getExtraboards());

		val modified = fromRequest.toBuilder().
				planId(planId).
				exbPoolSetUpId(exbPoolSetUpId).
				exbTurns(ImmutableSet.copyOf(exbTurns)).
				extraboards(ImmutableSet.copyOf(extraboards)).
				hubId(fromRequest.getHubId()).
				suMarkOffRate(fromRequest.getSuMarkOffRate()).
				moMarkOffRate(fromRequest.getMoMarkOffRate()).
				tuMarkOffRate(fromRequest.getTuMarkOffRate()).
				weMarkOffRate(fromRequest.getWeMarkOffRate()).
				thMarkOffRate(fromRequest.getThMarkOffRate()).
				frMarkOffRate(fromRequest.getFrMarkOffRate()).
				saMarkOffRate(fromRequest.getSaMarkOffRate()).
				markOffRate(fromRequest.getMarkOffRate()).
				breakEvenPoint(fromRequest.getBreakEvenPoint()).
				description(fromRequest.getDescription()).
				exbType(fromRequest.getExbType()).
				exbPlacementCode(fromRequest.getExbPlacementCode()).
				optimizeSize(fromRequest.getOptimizeSize()).
				craft(fromRequest.getCraft()).
				homeOs(fromRequest.getHomeOs()).
				build();
		// FileUtils.writeStringToFile(new File("exbPoolSetUp_modified.txt"), JsonUtils.prettyPrint(modified), "UTF-8");
		return modified;
	}
}
