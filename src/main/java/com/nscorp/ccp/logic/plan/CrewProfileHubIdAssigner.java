package com.nscorp.ccp.logic.plan;
import java.util.*;
import com.nscorp.ieorcommons.collection.*;
import com.nscorp.ieorcommons.guava.*;

import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.utils.commonTypes.ProfileKey;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import com.nscorp.ieorcommons.collection.*;
import com.nscorp.ieorcommons.guava.*;

import static com.google.common.collect.ImmutableList.toImmutableList;

@Slf4j
@Pure
public class CrewProfileHubIdAssigner {

	public Result buildCrewProfiles(
			@NonNull final CompletePlan cpWithHubIds,
			@NonNull final BoardInfo boardInfo) {
		// Assign hub ID's to the crew profiles.  Create a warning for each crew profile that couldn't be assigned a hub.
		val list = ImmutableCollectionUtils.mapToImmutableList(cpWithHubIds.getCrewProfiles(),
				crewProfile-> assignHubId(boardInfo, crewProfile));

		// Build a map whose key is ProfileKey and whose value is the hub ID to which the profile was assigned.
		val map = list.stream().
				map(CrewProfileAndWarnings::getCrewProfile).
				filter(crewProfile->crewProfile.getHubId() != null).
				collect(ImmutableCollectionUtils.toImmutableMapLastOnly(CrewProfile::getProfileKey, CrewProfile::getHubId));

		// Extract just the crew profiles and warnings.
		val crewProfiles = ImmutableCollectionUtils.mapToImmutableSet(list, CrewProfileAndWarnings::getCrewProfile);
		val warnings = list.stream().flatMap(e->e.getWarnings().stream()).collect(toImmutableList());

		// Return the crew profiles, profile-to-hub assignment map, and warnings.
		return new Result(crewProfiles, map, warnings);
	}

	@Value
	private static class CrewProfileAndWarnings {
		CrewProfile crewProfile;
		List<PlanHubAssignmentWarning> warnings;
	}

	@NonNull private static CrewProfileAndWarnings assignHubId(
			@NonNull final BoardInfo boardInfo,
			@NonNull final CrewProfile crewProfile) {
		@Nullable val pool = boardInfo.getPoolsByKey().get(crewProfile.getPoolKey());

		return Optional.ofNullable(pool).
				filter(p->p.getHubId() != null).

				// Copy the pool's hub ID to the crew profile.
				map(p->new CrewProfileAndWarnings(crewProfile.withHubId(pool.getHubId()), ImmutableCollectionUtils.imlist())).

				// If no pool is available, we can't assign a hub ID.
				orElseGet(()-> {
					val warnings = ImmutableCollectionUtils.imlist(PlanHubAssignmentWarning.builder().
							warning(String.format("CrewProfile %s refers to pool %s which either does not exist or has no defined hub.  The CrewProfile will be ignored.", crewProfile.getProfileKeyWithCraft(), crewProfile.getPoolKey())).
							code(PlanHubAssignmentWarning.WarningCode.POOL_CANNOT_SUPPLY_HUB).
							build());
					return new CrewProfileAndWarnings(crewProfile, warnings);
				});
	}

	@Value
	public static class Result {
		Set<CrewProfile> crewProfiles;
		Map<ProfileKey, Long> map;
		List<PlanHubAssignmentWarning> warnings;
	}
}
