package com.nscorp.ccp.logic.plan;

import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.utils.commonTypes.PoolOrExbPoolSetUpKey;
import com.nscorp.ccp.utils.commonTypes.SelfSustainingKey;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.util.*;

import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;

@Slf4j
@Pure
public class SelfSustainingHubIdAssigner {
	@Value
	public static class Result {
		Set<SelfSustaining> selfSustainings;
		Map<SelfSustainingKey, Long> map;
		List<PlanHubAssignmentWarning> warnings;
	}
	@NonNull public Result buildSelfSustainings(
			@NonNull final CompletePlan cpWithHubIds,
			final BoardInfo boardInfo ) {

		val list = cpWithHubIds.getSelfSustainings().stream().
				map(selfSustaining -> getSelfSustainings(boardInfo, selfSustaining)).
				collect(toImmutableList());
		val selfSustainings = list.stream().map(SelfSustainingAndWarnings::getSelfSustaining).collect(toImmutableSet());
		val warnings = list.stream().flatMap(e -> e.getWarnings().stream()).collect(toImmutableList());
		val map = selfSustainings.stream().
				filter(e -> e.getHubId() != null).
				collect(toImmutableMapLastOnly(SelfSustaining::getKey, SelfSustaining::getHubId));
		return new Result(selfSustainings, map, warnings);
	}

		/**
		val selfSustainings = ImmutableSet.<SelfSustaining>builder();
		val selfSustainingHubs = Maps.<SelfSustainingKey, Long>newLinkedHashMap();
		for (final SelfSustaining selfSustaining : cpWithHubIds.getSelfSustainings()) {
			val key = selfSustaining.getPoolOrExbPoolSetUpKey();
			val poolOrExbPoolSetUp = boardInfo.getPoolOrExbPoolSetUp(key);
			final SelfSustaining toAdd;
			if (poolOrExbPoolSetUp != null) {
				final Long hubId;
				if (poolOrExbPoolSetUp instanceof Pool) {
					hubId = ((Pool) poolOrExbPoolSetUp).getHubId();
				}
				else if (poolOrExbPoolSetUp instanceof ExbPoolSetUp) {
					hubId = ((ExbPoolSetUp) poolOrExbPoolSetUp).getHubId();
				}
				else {
					hubId = null;
				}
				if (hubId != null) {
					toAdd = selfSustaining.withHubId(hubId);
					selfSustainingHubs.put(selfSustaining.getKey(), hubId);
				}
				else {
					warnings.accept(PlanHubAssignmentWarning.builder().
							warning(String.format("SelfSustaining %s refers to pool/exb %s which either does not exist or has no defined hub.  The SelfSustaining will be ignored.", key, selfSustaining.getPoolOrExbPoolSetUpKey())).
							code(PlanHubAssignmentWarning.WarningCode.POOL_CANNOT_SUPPLY_HUB).
							build());
					toAdd = selfSustaining;
				}
			}
			else {
				toAdd = selfSustaining;
			}
			selfSustainings.add(toAdd);
		}
		return new Result(selfSustainings.build(), immap(selfSustainingHubs));
	}
	*/

	@NonNull private static SelfSustainingAndWarnings getSelfSustainings(
			@NonNull final BoardInfo boardInfo,
			@NonNull final SelfSustaining selfSustaining) {
		val key = selfSustaining.getPoolOrExbPoolSetUpKey();

		return Optional.ofNullable(boardInfo.getPoolOrExbPoolSetUp(key)).
				// Assign a hub ID to the SelfSustaining.
				map(poolOrExbPoolSetUp-> assignHubId(selfSustaining, key, poolOrExbPoolSetUp)).

				// If no PoolOrExbPoolSetUpKey is present, we can't assign a hub ID.
				orElseGet(()->new SelfSustainingAndWarnings(selfSustaining, imlist()));
	}
	@Value
	private static class SelfSustainingAndWarnings {
		SelfSustaining selfSustaining;
		List<PlanHubAssignmentWarning> warnings;
	}

	@NonNull private static SelfSustainingAndWarnings assignHubId(
			final SelfSustaining selfSustaining,
			final PoolOrExbPoolSetUpKey key,
			final Object poolOrExbPoolSetUp) {
		return Optional.ofNullable(getHubId(poolOrExbPoolSetUp)).
				map(hubId -> new SelfSustainingAndWarnings(selfSustaining.withHubId(hubId), imlist())).
				orElseGet(() -> {
					val warning = PlanHubAssignmentWarning.builder().
							warning(String.format("SelfSustaining %s refers to pool/exb %s which either does not exist or has no defined hub.  The SelfSustaining will be ignored.", key, selfSustaining.getPoolOrExbPoolSetUpKey())).
							code(PlanHubAssignmentWarning.WarningCode.POOL_CANNOT_SUPPLY_HUB).
							build();
					return new SelfSustainingAndWarnings(selfSustaining, imlist(warning));
				});
	}

	@Nullable private static Long getHubId(final Object poolOrExbPoolSetUp) {
		final Long hubId;
		if (poolOrExbPoolSetUp instanceof Pool) {
			hubId = ((Pool) poolOrExbPoolSetUp).getHubId();
		}
		else if (poolOrExbPoolSetUp instanceof ExbPoolSetUp) {
			hubId = ((ExbPoolSetUp) poolOrExbPoolSetUp).getHubId();
		}
		else {
			hubId = null;
		}
		return hubId;
	}

}
