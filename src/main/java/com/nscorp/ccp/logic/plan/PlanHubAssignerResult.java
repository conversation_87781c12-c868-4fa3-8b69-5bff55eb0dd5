package com.nscorp.ccp.logic.plan;
import java.util.*;

import com.nscorp.ccp.common.plan.CompletePlan;
import com.nscorp.ccp.utils.commonTypes.*;
import lombok.Builder;
import lombok.Value;

@Value
@Builder(toBuilder = true)
public class PlanHubAssignerResult {
	List<PlanHubAssignmentWarning> warnings;
	CompletePlan completePlan;

	Map<ProfileKey,Long> crewProfileHubs;
	Map<CardedPoolKey,Long> cardedPoolHubs;
	Map<IdPoolKey,Long> idPoolHubs;
	Map<WorkRestProfKey,Long> workRestProfHubs;
	Map<TueKey,Long> tueHubs;
	Map<SelfSustainingKey,Long> selfSustainingHubs;
}
