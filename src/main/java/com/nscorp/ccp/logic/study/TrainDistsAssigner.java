package com.nscorp.ccp.logic.study;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.common.trainDists.*;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.time.DayOfWeek;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import com.nscorp.ieorcommons.guava.*;

@RequiredArgsConstructor
@Slf4j
@Pure
public class TrainDistsAssigner {
	static final DayOfWeek WILDCARD_DOW = null;
	static final String WILDCARD_OS = null;

	@Value
	static class DepartureKey {
		String trnNr;
		@With DayOfWeek dow;
		String trnCrewDistr;
		String fromOs;
	}

	@Value
	static class TransitTimeKey {
		String trnNr;
		@With DayOfWeek dow;
		String trnCrewDistr;
		String fromOs;
		String toOs;
	}

	@Value
	static class TtDistByTrainTypeKey {
		String trnType;
		@With DayOfWeek dow;
		String trnCrewDistr;
		@With String fromOs;
		@With String toOs;
	}

	@NonNull private DepartureKey key(@NonNull DepartureDistribution distribution) {
		val dayOfWeek = (distribution.getDow() == 0) ? WILDCARD_DOW : DayOfWeek.of(distribution.getDow() );
		return new DepartureKey(distribution.getTrnSymbol(), dayOfWeek, distribution.getLineSegment(), distribution.getFromOs());
	}

	@NonNull private DepartureKey departureKey(@NonNull TrainDistAssignmentInput train) {
		val dayOfWeek = train.getTrnOrgnDt().getDayOfWeek();
		return new DepartureKey(train.getTrnSymb(), dayOfWeek, train.getTrnCrewDistr(), train.getFromOs());
	}

	@NonNull private TransitTimeKey transitTimeKey(@NonNull TrainDistAssignmentInput train) {
		val dayOfWeek = train.getTrnOrgnDt().getDayOfWeek();
		return new TransitTimeKey(train.getTrnSymb(), dayOfWeek, train.getTrnCrewDistr(), train.getFromOs(), train.getToOs());
	}

	@NonNull private TtDistByTrainTypeKey ttDistByTrainTypeKey(@NonNull TrainDistAssignmentInput train) {
		val dayOfWeek = train.getTrnOrgnDt().getDayOfWeek();
		return new TtDistByTrainTypeKey(train.getTrnType(),
				dayOfWeek,
				train.getTrnCrewDistr(),
				train.getFromOs(),
				train.getToOs());
	}

	@NonNull private TransitTimeKey key(@NonNull TransitTimeDistribution transitTimeDistribution) {
		val dayOfWeek = (transitTimeDistribution.getDow() == 0) ? WILDCARD_DOW : DayOfWeek.of(transitTimeDistribution.getDow() );
		return new TransitTimeKey(transitTimeDistribution.getTrnSymbol(), dayOfWeek, transitTimeDistribution.getLineSegment(), transitTimeDistribution.getFromOs(), transitTimeDistribution.getToOs());
	}

	@NonNull private TtDistByTrainTypeKey key(@NonNull TtDistByTrainType ttDistByTrainType) {
		val dayOfWeek = (ttDistByTrainType.getDow() == 0) ? WILDCARD_DOW : DayOfWeek.of(ttDistByTrainType.getDow() );
		return new TtDistByTrainTypeKey(ttDistByTrainType.getTrnType(),
				dayOfWeek,
				ttDistByTrainType.getLineSegment(),
				ttDistByTrainType.getFromOs(),
				ttDistByTrainType.getToOs());
	}

	private DepartureDistributionParams deptParams(DistributionSource source, DepartureDistribution departureDistribution) {
		return DepartureDistributionParams.builder().
				deptDistSource(source).
				departureDistribution(departureDistribution.getDepartureDistribution()).
				deptLower(departureDistribution.getDepartureLower()).
				deptUpper(departureDistribution.getDepartureUpper()).
				deptMedium(departureDistribution.getDepartureMedium()).
				deptMean1(departureDistribution.getDepartureMean1()).
				deptLambda1(departureDistribution.getDepartureLamda1()).
				deptScale(departureDistribution.getDepartureScale()).
				deptVariance1(departureDistribution.getDepartureVariance1()).
				deptShiftConstant(departureDistribution.getDepartureShiftConstant()).
				deptShape(departureDistribution.getDepartureShape()).
				deptRate(departureDistribution.getDepartureRate()).
				build();
	}

	private TransitDistributionParams ttParams(DistributionSource source, TransitTimeDistribution transitTimeDistribution) {
		return TransitDistributionParams.builder().
				transitDistSource(source).
				transitTimeDistribution(transitTimeDistribution.getTransitDistribution()).
				transitLower(transitTimeDistribution.getTransitLower()).
				transitUpper(transitTimeDistribution.getTransitUpper()).
				transitMedium(transitTimeDistribution.getTransitMedium()).
				transitMean1(transitTimeDistribution.getTransitMean1()).
				transitLambda1(transitTimeDistribution.getTransitLamda1()).
				transitScale(transitTimeDistribution.getTransitScale()).
				transitVariance1(transitTimeDistribution.getTransitVariance1()).
				transitShiftConstant(transitTimeDistribution.getTransitShiftConstant()).
				transitShape(transitTimeDistribution.getTransitShape()).
				transitRate(transitTimeDistribution.getTransitRate()).
				build();
	}

	private TransitDistributionParams ttParams(
			DistributionSource source,
			TtDistByTrainType ttDistByTrainType) {
		return TransitDistributionParams.builder().
				transitDistSource(source).
				transitTimeDistribution(ttDistByTrainType.getTransitDistribution()).
				transitLower(ttDistByTrainType.getTransitLower()).
				transitUpper(ttDistByTrainType.getTransitUpper()).
				transitMedium(ttDistByTrainType.getTransitMedium()).
				transitMean1(ttDistByTrainType.getTransitMean1()).
				transitLambda1(ttDistByTrainType.getTransitLamda1()).
				transitScale(ttDistByTrainType.getTransitScale()).
				transitVariance1(ttDistByTrainType.getTransitVariance1()).
				transitShiftConstant(ttDistByTrainType.getTransitShiftConstant()).
				transitShape(ttDistByTrainType.getTransitShape()).
				transitRate(ttDistByTrainType.getTransitRate()).
				build();
	}

	@Value
	@Builder
	static class DistributionsByKey {
		Map<DepartureKey, DepartureDistribution> deptByKey;
		Map<TransitTimeKey, TransitTimeDistribution> ttByKey;
		Map<TtDistByTrainTypeKey, TtDistByTrainType> ttDistByTrainTypeByKey;
	}

	private AllTrainDists adjustAllTrainDists(
			final @NonNull AllTrainDists allTrainDists,
			final boolean assignDeptParams,
			final boolean assignTransitTimeParams,
			final boolean useTtDist) {
		val depts = assignDeptParams ?
		            allTrainDists.getDepartureDistributions() :
		            ImmutableList.<DepartureDistribution>of();
		val trans = assignTransitTimeParams ?
		            allTrainDists.getTransitTimeDistributions() :
		            ImmutableList.<TransitTimeDistribution>of();
		val ttd = useTtDist ?
		          allTrainDists.getTtDistByTrainTypes() :
		          ImmutableList.<TtDistByTrainType>of();
		val newAllTrainDists = AllTrainDists.builder().
				departureDistributions(imlist(depts)).
				transitTimeDistributions(imlist(trans)).
				ttDistByTrainTypes(imlist(ttd)).
				build();
		return newAllTrainDists;
	}

	@NonNull public Iterable<TrainDistAssignmentResult> assignTrainDists(
		@NonNull final Iterable<TrainDistAssignmentInput> trains,
		@NonNull final AllTrainDists allTrainDists,
		final boolean assignDeptParams,
		final boolean assignTransitTimeParams,
		final boolean useTtDist) {

		// Remove any train dists that aren't requested via the three boolean parameters.
		val newAllTrainDists = adjustAllTrainDists(allTrainDists, assignDeptParams, assignTransitTimeParams, useTtDist);

		// Fetch all the departure distributions and key each one by train symbol,
		//  day of week, line segment, and fromOs.
		val deptByKey = newAllTrainDists.getDepartureDistributions().stream().
				collect(toImmutableMap(this::key, Function.identity()));

		// Fetch all the transit time distributions and key each one by train symbol,
		//  day of week, line segment, and fromOs.
		val ttByKey = newAllTrainDists.getTransitTimeDistributions().stream().
				collect(toImmutableMap(this::key, Function.identity()));

		// Fetch all the transit time distributions and key each one by train symbol,
		//  day of week, line segment, and fromOs.
		val ttDistByTrainTypeByKey = newAllTrainDists.getTtDistByTrainTypes().stream().
				collect(toImmutableMap(this::key, Function.identity()));

		val dists = DistributionsByKey.builder().
				deptByKey(deptByKey).
				ttByKey(ttByKey).
				ttDistByTrainTypeByKey(ttDistByTrainTypeByKey).
				build();

		// Attach departure and transit time params to each train (if available)
		//  and return a list of new trains.
		return stream(trains).
				map(input -> buildResult(dists, input)).
				collect(toImmutableList());
	}

	@NonNull private TrainDistAssignmentResult buildResult(
			@NonNull final DistributionsByKey dists,
			@NonNull final TrainDistAssignmentInput train) {
		@Nullable val departureDistributionParams = getDepartureDistributionParams(dists, train);
		@Nullable val transitDistributionParams = getTransitDistributionParams(dists, train);

		// Attach the params.
		final TrainDistAssignmentResult result;
		if ( departureDistributionParams != null && transitDistributionParams != null ) {
			result = TrainDistAssignmentResult.builder().
					fullTrainKey(train.getFullTrainKey()).
					departureDistributionParams(departureDistributionParams).
					transitDistributionParams(transitDistributionParams).
					build();
		}
		else if ( departureDistributionParams != null ) {
			result = TrainDistAssignmentResult.builder().
					fullTrainKey(train.getFullTrainKey()).
					departureDistributionParams(departureDistributionParams).
					build();
		}
		else if ( transitDistributionParams  != null ) {
			result = TrainDistAssignmentResult.builder().
					fullTrainKey(train.getFullTrainKey()).
					transitDistributionParams(transitDistributionParams).
					build();
		}
		else {
			result = TrainDistAssignmentResult.builder().
					fullTrainKey(train.getFullTrainKey()).
					build();
		}
		return result;
	}

	@Nullable DepartureDistributionParams getDepartureDistributionParams(
			@NonNull final DistributionsByKey dists,
			@NonNull final TrainDistAssignmentInput train) {
		val departureKey = departureKey(train);

		// Try train type with day of week.
		return Optional.ofNullable(dists.getDeptByKey().get(departureKey)).
				// Map to params.
				map(dept ->deptParams(DistributionSource.TRN_SYMBOL_WITH_DOW, dept)).

				// If no match found with train type and day of week, try train type with wildcard day of week.
				or(()-> Optional.ofNullable(getDeptDistParamsWithWildcardDow(dists, departureKey))).
				orElse(null);
	}

	@Nullable private DepartureDistributionParams getDeptDistParamsWithWildcardDow(
			@NonNull final DistributionsByKey dists,
			@NonNull final DepartureKey departureKey) {
		return Optional.ofNullable(dists.deptByKey.get(departureKey.withDow(WILDCARD_DOW))).

				// If a departure distribution was found, map to params.
				map(deptfdow -> deptParams(DistributionSource.TRN_SYMBOL_NO_DOW, deptfdow)).
				orElse(null);
	}
	private Optional<TransitDistributionParams> getTransitTimeWithSymbolAndDow(DistributionsByKey dists, TransitTimeKey transitTimeKey) {
		return Optional.ofNullable(dists.ttByKey.get(transitTimeKey)).
				// Map to params.
				map(tt-> ttParams(DistributionSource.TRN_SYMBOL_WITH_DOW, tt));
	}

	private Optional<TransitDistributionParams> getTransitTimeWithSymbolNoDow(
			DistributionsByKey dists,
			TransitTimeKey transitTimeKey) {
		val newKey = transitTimeKey.withDow(WILDCARD_DOW);
		return Optional.ofNullable(dists.ttByKey.get(newKey)).
				// Map to params.
				map(tt-> ttParams(DistributionSource.TRN_SYMBOL_NO_DOW, tt));
	}

	@Value
	@Builder
	private static class TtRule {
		TtDistByTrainTypeKey key;
		DistributionSource source;

		@Override
		public String toString() {
			return String.format("TtRule(key=%s, src=%s)", key, source);
		}
	}

	private Iterable<TtRule> listTtRules(TtDistByTrainTypeKey key) {
		return ImmutableList.of(
				// All fields.
				new TtRule(key, DistributionSource.YLFTD),

				// Subtract fromOs and toOs.
				new TtRule(key.withFromOs(WILDCARD_OS).withToOs(WILDCARD_OS), DistributionSource.YLD),

				// Subtract DOW.
				new TtRule(key.withDow(WILDCARD_DOW), DistributionSource.YLFT),

				// Subtract DOW, fromOs, and toOs.
				new TtRule(key.withDow(WILDCARD_DOW).withFromOs(WILDCARD_OS).withToOs(WILDCARD_OS), DistributionSource.YL)
		);
	}

	@Nullable TransitDistributionParams getTransitDistributionParams(
			@NonNull final DistributionsByKey dists,
			@NonNull final TrainDistAssignmentInput train) {

		// Look for a train symbol-based match with DOW.
		return getTransitTimeWithSymbolAndDow(dists, transitTimeKey(train)).

				// Look for a train symbol-based match with no DOW.
				or( ()->getTransitTimeWithSymbolNoDow(dists, transitTimeKey(train))).

				// Look for a train type-based match.
				or(()-> getTransitTimeWithMatchingTrnType(dists, ttDistByTrainTypeKey(train))).

				// Nothing found.  Return null.
				orElse(null);
	}

	@NonNull private Optional<TransitDistributionParams> getTransitTimeWithMatchingTrnType(
			@NonNull final DistributionsByKey dists,
			@NonNull final TtDistByTrainTypeKey ttDistByTrainTypeKey) {
		// Try a train-type-based match. Matching is based on the list of key variants returned by listTtRules().
		return stream(listTtRules(ttDistByTrainTypeKey)).
				flatMap(rule ->
					// Look for a match.
					Optional.ofNullable(dists.ttDistByTrainTypeByKey.get(rule.getKey())).
							// Map the match to a TransitDistributionParams.
							map(e -> ttParams(rule.getSource(), e)).
							stream()
				).
				findFirst();
	}
}
