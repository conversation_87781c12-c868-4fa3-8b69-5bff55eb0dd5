package com.nscorp.ccp.logic.study;
import java.util.*;
import lombok.val;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Streams;
import com.nscorp.ccp.common.study.*;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;

import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.trim;

@RequiredArgsConstructor
@Slf4j
@Pure
public class StudyCopierLogic {
	@NonNull public Study beginCopy(
			final @NonNull Study origStudy,
			final @NonNull Instant now,
			final @NonNull String userId,
			final @NonNull String name,
			final @NonNull String desc) {
		val studyId = origStudy.getStudyId();
		val theName = trim(name);
		val theDesc = trim(desc);
		if (isEmpty(theName) ) {
			throw new StudyCopierException(String.format("Illegal name '%s'.", theName));
		}
		if (isEmpty(theDesc) ) {
			throw new StudyCopierException(String.format("Illegal description '%s'.", theDesc));
		}
		// Preconditions.checkArgument(study != null, String.format("Source study #%d not found.", studyId));
		val newStudy = origStudy.toBuilder().
				studyId(null).
				createTs(now).
				updateTs(null).
				userId(userId).
				updateUserId(null).
				errorMessage(null).
				studyName(theName).
				description(theDesc).
				status(StudyStatus.IN_PROGRESS).
				copiedFrom(studyId).
				build();
		return newStudy;
	}

	@Value
	@Builder
	public static class Result {
		Study newStudy;
		List<Train> newTrains;
	}

	@NonNull public Result finishCopy(final @NonNull Study study, final @NonNull Iterable<Train> originalTrains) {
		val studyId = study.getStudyId();

		// Clear the train ID's and set new study ID.
		val newTrains = Streams.stream(originalTrains).
				map(train -> train.withTrainId(null)).
				map(train->train.withStudyId(studyId)).
				collect(ImmutableList.toImmutableList());

		val newStudy = study.withStatus(StudyStatus.COMPLETE);

		return Result.builder().newStudy(newStudy).newTrains(newTrains).build();
	}
}
