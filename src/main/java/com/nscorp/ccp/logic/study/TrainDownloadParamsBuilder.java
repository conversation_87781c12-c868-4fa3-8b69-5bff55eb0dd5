package com.nscorp.ccp.logic.study;
import lombok.*;

import com.nscorp.ccp.common.study.Study;
import com.nscorp.ccp.common.study.TrainDownloadParams;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Pure
@Slf4j
public class TrainDownloadParamsBuilder {
	@NonNull public TrainDownloadParams getTrainDownloadParams(@NonNull final Study savedInProgressStudy) {
		val result = TrainDownloadParams.builder().
				currentDate(savedInProgressStudy.getCurrentDate()).
				deptDist(savedInProgressStudy.getDeptDist()).
				transitDist(savedInProgressStudy.getTransitDist()).
				opdScenario(savedInProgressStudy.getOpdScenario()).
				opdPlan(savedInProgressStudy.getOpdPlan()).
				algorithm(savedInProgressStudy.getAlgorithm()).
				opdTrnTypes(savedInProgressStudy.getOpdTrnTypesAsSet()).
				historyTrnTypes(savedInProgressStudy.getHistoricalTrnTypesAsSet()).
				startDate(savedInProgressStudy.getStartDate()).
				endDate(savedInProgressStudy.getEndDate()).
				localYardJobStartDate(savedInProgressStudy.getLocalYardJobStartDate()).
				localYardJobEndDate(savedInProgressStudy.getLocalYardJobEndDate()).
				historyStartDate(savedInProgressStudy.getHistoryStartDate()).
				historyEndDate(savedInProgressStudy.getHistoryEndDate()).
				upsideId(savedInProgressStudy.getUpsideId()).
				build();
		return result;
	}
}
