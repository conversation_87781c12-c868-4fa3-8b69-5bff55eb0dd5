package com.nscorp.ccp.utils.commonTypes;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class ExbPoolSetUpKey {
	@NonNull String distr;
	@NonNull String subDistr;
	@NonNull String poolName;

	@Override
	public String toString() {
		return String.format("xb-psu-k[d=%s,sd=%s,p=%s]", distr, subDistr, poolName);
	}
}
