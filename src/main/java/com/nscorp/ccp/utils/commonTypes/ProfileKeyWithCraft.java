package com.nscorp.ccp.utils.commonTypes;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;

/**
 * @see ProfileKey
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class ProfileKeyWithCraft {
	@NonNull String profileId;
	@NonNull String crewOrgnOs;
	@NonNull @With Craft craft;

	@Override
	public String toString() {
		return String.format("pkwc[pid=%s, coos=%s, craft=%s]", profileId, crewOrgnOs, craft);
	}

}
