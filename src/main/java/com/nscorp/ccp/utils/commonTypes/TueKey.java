package com.nscorp.ccp.utils.commonTypes;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class TueKey {
	String distr;
	String subDistr;
	Craft craft;
	int seqNbr;

	public TueSubruleKey toSubruleKey(int subseqnr) {
		return TueSubruleKey.builder().distr(distr).subDistr(subDistr).craft(craft).seqNbr(seqNbr).subSeqNbr(subseqnr).build();
	}

	@Override
	public String toString() {
		return String.format("tueKey[d=%s,s=%s,c=%s,s=%s]",
				distr, subDistr, craft, seqNbr);
	}
}
