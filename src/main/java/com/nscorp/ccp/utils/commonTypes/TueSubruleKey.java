package com.nscorp.ccp.utils.commonTypes;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class TueSubruleKey {
	String distr;
	String subDistr;
	Craft craft;
	int seqNbr;
	int subSeqNbr;

	@Override
	public String toString() {
		return String.format("tueSubruleKey[d=%s,s=%s,c=%s,s=%s, subseq=%s]",
				distr, subDistr, craft, seqNbr, subSeqNbr);
	}
}
