package com.nscorp.ccp.utils.cli;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.commonTypes.*;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class PoolKeyConverter implements Converter <String, PoolKey>{
	@Override public PoolKey convert(final String source) {
		final String[] parts = source.split("-");
		if ( parts.length != 3 ) {
			throw new IllegalArgumentException(String.format("'%s': expected format is distr-subDistr-pool", source));
		}
		return new PoolKey(parts[0], parts[1], parts[2]);
	}
}
