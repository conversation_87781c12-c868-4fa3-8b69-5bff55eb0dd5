import React, {useEffect, useState} from "react";
import {Modal, Alert} from "react-bootstrap";

/**
 * App notifier with header and message on as alert on top right corner of screen
 * it takes three parameter in props
 * @param props
 *          header: header of message
 *          desc: description of message
 *          variant: the type of message ( danger , warning, success)
 * @returns {JSX.Element}
 * @constructor
 */
const AppNotifier = (props) => {
    let [show, setShow] = useState(props.alert ?props.alert.show:false);
    let {header, desc, variant} = props.alert || {};

    useEffect(() => {
        setShow(props.alert ?props.alert.show:false);
    }, [props.alert])

    return (
        <Modal show={show} size={"alert"} className="modal-alert" onHide={() => {
            setShow(false)
        }}>
            <Alert key={1} variant={variant} onClose={() => {
                setShow(false)
            }} dismissible
                   style={{
                       marginBottom: "0rem"
                   }}>
                <Alert.Heading className="h5">{header}</Alert.Heading>
                <p style={{marginBottom:"0rem"}}>
                    {desc}
                </p>
            </Alert>
        </Modal>
    );
}

export default AppNotifier;
