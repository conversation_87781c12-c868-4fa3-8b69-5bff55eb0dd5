import {Form} from "react-bootstrap";
import React, {useState} from "react";
import {Hint, Typeahead} from "react-bootstrap-typeahead";

/**
 * This text input component will let the user to select the option from suggestion by typing and optionally add the new value
 * @param props consists of below attributes
 *          options - array of suggestions for field
 *          allowNew - flag to allow the user to add new value, By default this is disabled
 *          maxLength - text field length
 *          newSelectionPrefix - Prefix for add a new value
 *          placeHolder - hint label for field
 *          ref - callback reference to give full control of component to caller
 *          onChange - callback function on value change
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
const TextInputWithOptions = (props) => {
    const {
        options = [],
        allowNew = false,
        maxLength = 2,
        newSelectionPrefix = `Add`,
        placeHolder = ``,
        ref,
        onChange
    } = props || {};

    const [filter, setFilter] = useState(``)

    return (
        <Typeahead ref={ref}
                   allowNew={allowNew}
                   id="options-text-input"
                   newSelectionPrefix={`${newSelectionPrefix} : `}
                   options={options}
                   onChange={(option) => {
                       if (option.length > 0) {
                           setFilter(`${option[0].label}`)
                           onChange(`${option[0].label}`)
                       } else {
                           //  this.testRef.current.clear();
                           setFilter( ``)
                       }
                   }}
                   className="options-text-input"
                   clearButton
                   renderInput={({inputRef, referenceElementRef, ...inputProps}) => (
                       <Hint>
                           <Form.Control
                               type="text"
                               maxLength={maxLength}
                               // onKeyDown={(option) => {
                               //     }}
                               {...inputProps}
                               ref={(node) => {
                                   if (node) {
                                         node.value = filter.toUpperCase();
                                   }
                                   inputRef(node);
                                   referenceElementRef(node);
                               }}
                           />
                       </Hint>
                   )}
                   labelKey={option => `${option.label? option.label.toUpperCase(): ``}`}
                   placeholder={placeHolder}
                    value={filter}
                   onInputChange={(text, e) => {
                       setFilter(text)
                   }}
        />
    );
}
export default TextInputWithOptions;