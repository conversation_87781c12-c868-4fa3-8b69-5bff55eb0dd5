import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>} from "react-bootstrap";
import React from "react";

//Popup menu with action buttons, this component will render center of the screen.
/**
 * @param props consists of below attributes
 *          hide - boolean to show or hide the PopupAlert
 *          message - alert message
 *          handleClose - callback function on click of close button on alert
 *          handleSuccess - callback function on click of success button on alert
 *          variant - the type of alert (success , error, warning)
 *          successLabel - label for success button
 *          closeLabel - label for cancel/close button
 * @returns {JSX.Element}
 * @constructor
 */
const PopupActionAlert = (props) => {
    const {hide, message,handleClose, variant, handleSuccess, successLabel, closeLabel, showSaveButton = true, showCloseButton= true} = props || {};

   // const{show,setShow} = useState(true)
    return (
        <Modal show={hide} onHide={handleClose} size="md" centered>
            <Modal.Body style={{textAlign: "center"}}>
             <Row>
                 {variant && <Col style={{maxWidth: "2rem", alignSelf: "center"}}>
                     <img src={variant ==="success"? `../assets/images/check2-success.svg`:variant ==="warning" ? `../assets/images/exclamation-triangle-warning.svg`:variant ==="danger" ? `../assets/images/exclamation-octagon.svg`:''} alt="" width="20" height="20" title="Feedback icon"/>
                 </Col>
                 }
                 <Col style={{textAlign: "left"}}>
                     {message}
                 </Col>
             </Row>
            </Modal.Body>
            <Modal.Footer style={{justifyContent: "center"}} className="ccp-form">
               {showSaveButton &&  <Button variant={`${variant}`} onClick={handleSuccess}>
                    {`${successLabel}`}
                </Button>
                }
                {showCloseButton &&  <Button variant="warning" onClick={handleClose}>
                    {`${closeLabel}`}
                </Button>
                }
            </Modal.Footer>
        </Modal>
    );
}
export default PopupActionAlert;