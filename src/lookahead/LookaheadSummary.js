import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Modal, Row, Tab, Tabs, ButtonGroup, ButtonToolbar } from "react-bootstrap";
import { TE_HEADCOUNT_COLUMN_DEFS, CT_TRAINEE_COLUMN_DEFS, CT_TRAINEE_APPROVED_COLUMN_DEFS, GO_TEAM_COLUMN_DEFS, TEMP_TRANSFER_COLUMN_DEFS, HIRE_GROUP_DETAIL_COLUMN_DEFS, HIRE_GROUP_MAPPING_COLUMN_DEFS } from "../config/ccpconstants";
import { AgGridReact } from "ag-grid-react";
import { connect } from "react-redux";
import { clearFeedback, changeLookaheadCurrTab, getLookaheadFile } from "./lookaheadActions";
import { getDefaultColDef, getGenericRowHeight, getUSTimeFormatETFromUTC, getExcelFileName, getExcelSheetName } from "../util/Utils";

class LookaheadSummary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            validated: false,
            showSuccess: false,
            name: this.props.currLookahead.name,
            desc: this.props.currLookahead.desc,
            updated: false,
            saved: false
        }
    }
    formRef = React.createRef();
    handleTabChange = (e) => {
        this.setState({ currTab: e })
        this.props.changeLookaheadCurrTab(e);
    }
    onTeHeadcountExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`TeHeadcount_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiTeHeadcount.exportDataAsExcel(params);
    }
    onCtTraineeExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`CtTrainee_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiCtTrainee.exportDataAsExcel(params);
    }
    onCtTraineeApprovedExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`CtApproved_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiCtTraineeApproved.exportDataAsExcel(params);
    }
    onGoTeamExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`GoTeam_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiGoTeam.exportDataAsExcel(params);
    }
    onTempTeamExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`TempTeam_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiTempTeam.exportDataAsExcel(params);
    }
    onHireGroupDetailExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`HireGroupDetail_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiHireGroupDetail.exportDataAsExcel(params);
    }
    onHiregroupMapExport = () => {
        const { name } = this.props.currLookahead || {}
        let params = {
            fileName: getExcelFileName(`HireGroupMapping_${name}`),
            sheetName: getExcelSheetName(`Sheet1`)
        }
        this.gridApiHireGroupMap.exportDataAsExcel(params);
    }
    onGridReadyTeHeadcount = params => {
        this.gridApiTeHeadcount = params.api;
        this.gridColumnApi = params.columnApi;
        this.gridColumnApi.autoSizeAllColumns();
    }
    onGridReadyCtTrainee = params => {
        this.gridApiCtTrainee = params.api;
        this.gridColumnApiCtTrainee = params.columnApi;
        this.gridColumnApiCtTrainee.autoSizeAllColumns();
    }
    onGridReadyCtTraineeApproved = params => {
        this.gridApiCtTraineeApproved = params.api;
        this.gridColumnApiCtTraineeApproved = params.columnApi;
        this.gridColumnApiCtTraineeApproved.autoSizeAllColumns();
    }
    onGridReadyGoTeam = params => {
        this.gridApiGoTeam = params.api;
        this.gridColumnApiGoTeam = params.columnApi;
        this.gridColumnApiGoTeam.autoSizeAllColumns();
    }
    onGridReadyTempTeam = params => {
        this.gridApiTempTeam = params.api;
        this.gridColumnApiTempTeam = params.columnApi;
        this.gridColumnApiTempTeam.autoSizeAllColumns();
    }
    onGridReadyHireGroupDetail = params => {
        this.gridApiHireGroupDetail = params.api;
        this.gridColumnApiHireGroupDetail = params.columnApi;
        this.gridColumnApiHireGroupDetail.autoSizeAllColumns();
    }
    onGridReadyHireGroupMap = params => {
        this.gridApiHireGroupMap = params.api;
        this.gridColumnApiHireGroupMap = params.columnApi;
        this.gridColumnApiHireGroupMap.autoSizeAllColumns();
    }
    downloadFile = (fileId, fname) => {
        this.props.getLookaheadFile({ lamFileId: fileId, fileName: fname })
    }
    render() {
        const { currLookahead, tab, lookaheadDetails } = this.props;
        const {
            datasetDesc,
            datasetName,
            downloadDt,
            ctTraineeFileName,
            ctTraineeRowUploaded,
            ctTraineeStatus,
            ctTraineeApprovedFileName,
            ctTraineeApprovedRowUploaded,
            ctTraineeApprovedStatus,
            status,
            creatorRacfId,
            lastUpdTs,
            lamDatasetId,
            createTs,
            teHeadcountRowDownloaded,
            teHeadcountStatus,
            goTeamFileName,
            goTeamRowUploaded,
            goTeamStatus,
            tempTransferFileName,
            tempTransferRowUploaded,
            tempTransferStatus,
        } = currLookahead.item;
        return (
            <>
                <Form noValidate validated={this.state.validated}
                    className="font-weight-600 font-size-small" ref={this.formRef}>
                    <Form.Row sm={12}>
                        <Form.Group as={Col} controlId="validationCustom01">
                            <Modal.Dialog style={{ maxWidth: "100%", margin: "0rem" }}>
                                <Modal.Header className="settings-model-header">
                                    <Modal.Title style={{ color: 'darkblue' }}> <b>
                                        {datasetName + ' ('}{lamDatasetId + ')'}</b></Modal.Title>
                                </Modal.Header>
                                <Modal.Body className="ccp-form">
                                    <Tabs defaultActiveKey="info" id="lookahead-tabs"
                                        activeKey={tab}
                                        onSelect={this.handleTabChange} className="position-sticky"
                                        style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                        <Tab eventKey="info" title="Info"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <Modal.Dialog style={{ maxWidth: "100%", margin: "0rem" }} className="model-no-border">
                                                <Modal.Body>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="studyName">
                                                            <Form.Label column sm={2}  >
                                                                <b>Name :</b>
                                                            </Form.Label>
                                                            <Form.Label column sm={10}> {datasetName + ' (' + lamDatasetId + ')'}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="description">
                                                            <Form.Label column sm={2}  ><b>Description :</b></Form.Label>
                                                            <Form.Label column sm={10} >{datasetDesc}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="status">
                                                            <Form.Label column sm={2}  ><b>Status :</b></Form.Label>
                                                            <Form.Label column sm={10}>{status}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="creator">
                                                            <Form.Label column sm={2}  ><b>Creator :</b></Form.Label>
                                                            <Form.Label column sm={10}>{creatorRacfId}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="createTs">
                                                            <Form.Label column sm={2}  ><b>Create Ts :</b></Form.Label>
                                                            <Form.Label column sm={10}>{getUSTimeFormatETFromUTC(createTs)}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} controlId="lastUpdTs">
                                                            <Form.Label column sm={2}  ><b>Last update :</b></Form.Label>
                                                            <Form.Label column sm={10}>{getUSTimeFormatETFromUTC(lastUpdTs)}</Form.Label>
                                                        </Form.Group>
                                                    </Form.Row>
                                                    <Form.Row>
                                                        <Form.Group as={Col} md="6" controlId="datasetSettings">
                                                            <Modal.Dialog
                                                                style={{ maxWidth: "100%", margin: ".25rem .25rem .25rem .25rem" }}>
                                                                <Modal.Header className="settings-model-header">
                                                                    <Modal.Title
                                                                        className="settings-model-title"></Modal.Title>
                                                                </Modal.Header>
                                                                <Modal.Body>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>TE Headcount Rows :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{teHeadcountRowDownloaded}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>TE Headcount Date :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{downloadDt}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>TE Headcount Status :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{teHeadcountStatus}</Form.Label>
                                                                    </Row>
                                                                </Modal.Body></Modal.Dialog>
                                                        </Form.Group>
                                                        <Form.Group as={Col} md="6" controlId="validationCustom01">
                                                            <Modal.Dialog
                                                                style={{ maxWidth: "100%", margin: ".25rem .25rem .25rem .25rem" }}>
                                                                <Modal.Header className="settings-model-header">
                                                                    <Modal.Title
                                                                        className="settings-model-title"></Modal.Title>
                                                                </Modal.Header>
                                                                <Modal.Body>

                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={4}  ><b>Go Team File :</b></Form.Label>
                                                                        <Form.Label column sm={8} >
                                                                            <Button variant="link" onClick={(e) => {
                                                                                e.preventDefault();
                                                                                goTeamFileName ? this.downloadFile(currLookahead.item.goTeamFileId, goTeamFileName)
                                                                                    : this.downloadFile(currLookahead.item.goTeamOriginalFileId,
                                                                                        currLookahead.item.goTeamOriginalFileName)
                                                                            }}
                                                                            >{goTeamFileName || !goTeamFileName && currLookahead.item.goTeamOriginalFileName}</Button>
                                                                        </Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>Go Team Rows :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{goTeamRowUploaded}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>Go Team Status :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{goTeamStatus}</Form.Label>
                                                                    </Row>
                                                                </Modal.Body></Modal.Dialog>
                                                        </Form.Group></Form.Row>
                                                        <Form.Row>
                                                        <Form.Group as={Col} md="6" controlId="datasetSettings">
                                                            <Modal.Dialog
                                                                style={{ maxWidth: "100%", margin: ".25rem .25rem .25rem .25rem" }}>
                                                                <Modal.Header className="settings-model-header">
                                                                    <Modal.Title
                                                                        className="settings-model-title"></Modal.Title>
                                                                </Modal.Header>
                                                                <Modal.Body>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={4}  ><b>CT Trainee File :</b></Form.Label>
                                                                        <Form.Label column sm={8} >
                                                                            <Button variant="link" onClick={(e) => {
                                                                                e.preventDefault();
                                                                                ctTraineeFileName ? this.downloadFile(currLookahead.item.ctTraineeFileId, ctTraineeFileName)
                                                                                    : this.downloadFile(currLookahead.item.ctTraineeOriginalFileId,
                                                                                        currLookahead.item.ctTraineeOriginalFileName)
                                                                            }}
                                                                            >{ctTraineeFileName || !ctTraineeFileName && currLookahead.item.ctTraineeOriginalFileName}</Button>
                                                                        </Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>CT Trainee Rows :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{ctTraineeRowUploaded}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>CT Trainee Status :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{ctTraineeStatus}</Form.Label>
                                                                    </Row>
                                                                </Modal.Body></Modal.Dialog>
                                                        </Form.Group>
                                                        <Form.Group as={Col} md="6" controlId="validationCustom01">
                                                            <Modal.Dialog
                                                                style={{ maxWidth: "100%", margin: ".25rem .25rem .25rem .25rem" }}>
                                                                <Modal.Header className="settings-model-header">
                                                                    <Modal.Title
                                                                        className="settings-model-title"></Modal.Title>
                                                                </Modal.Header>
                                                                <Modal.Body>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}  >
                                                                        <Form.Label column sm={4} >
                                                                            <b>Temp Transfer File :</b></Form.Label>
                                                                        <Form.Label column sm={8} >
                                                                            <Button variant="link" onClick={(e) => {
                                                                                e.preventDefault();
                                                                                tempTransferFileName ? this.downloadFile(currLookahead.item.tempTransferFileId, tempTransferFileName)
                                                                                    : this.downloadFile(currLookahead.item.tempTransferOriginalFileId,
                                                                                        currLookahead.item.tempTransferOriginalFileName)
                                                                            }}
                                                                            >{tempTransferFileName || !tempTransferFileName && currLookahead.item.tempTransferOriginalFileName}</Button>
                                                                        </Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>Temp Transfer Rows :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{tempTransferRowUploaded}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>Temp Transfer Status:</b></Form.Label>
                                                                        <Form.Label column sm={4}>{tempTransferStatus}</Form.Label>
                                                                    </Row>
                                                                </Modal.Body></Modal.Dialog>
                                                        </Form.Group></Form.Row>
                                                        <Form.Row>
                                                        <Form.Group as={Col} md="6" controlId="datasetSettings">
                                                            <Modal.Dialog
                                                                style={{ maxWidth: "100%", margin: ".25rem .25rem .25rem .25rem" }}>
                                                                <Modal.Header className="settings-model-header">
                                                                    <Modal.Title
                                                                        className="settings-model-title"></Modal.Title>
                                                                </Modal.Header>
                                                                <Modal.Body>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={4}  ><b>CT Approved File :</b></Form.Label>
                                                                        <Form.Label column sm={8} >
                                                                            <Button variant="link" onClick={(e) => {
                                                                                e.preventDefault();
                                                                                ctTraineeFileName ? this.downloadFile(currLookahead.item.ctTraineeApprovedFileId, ctTraineeApprovedFileName)
                                                                                    : this.downloadFile(currLookahead.item.ctTraineeApprovedOriginalFileId,
                                                                                        currLookahead.item.ctTraineeApprovedOriginalFileName)
                                                                            }}
                                                                            >{ctTraineeApprovedFileName || !ctTraineeApprovedFileName && currLookahead.item.ctTraineeApprovedOriginalFileName}</Button>
                                                                        </Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>CT Approved Rows :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{ctTraineeApprovedRowUploaded}</Form.Label>
                                                                    </Row>
                                                                    <Row column="true" sm={6} className={`margin-top margin-bottom padding-left-1rem`}>
                                                                        <Form.Label column sm={5}  ><b>CT Approved Status :</b></Form.Label>
                                                                        <Form.Label column sm={4}>{ctTraineeApprovedStatus}</Form.Label>
                                                                    </Row>
                                                                </Modal.Body></Modal.Dialog>
                                                        </Form.Group>
                                                        </Form.Row>
                                                </Modal.Body>
                                            </Modal.Dialog>
                                        </Tab>
                                        <Tab eventKey="teHeadCount" title="TE Headcount"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine" style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.teHeadCounts && lookaheadDetails.teHeadCounts.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={TE_HEADCOUNT_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.teHeadCounts}
                                                            onGridReady={this.onGridReadyTeHeadcount}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.teHeadCounts && lookaheadDetails.teHeadCounts.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onTeHeadcountExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="ctTrainee" title="CT Trainee"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.ctTrainees && lookaheadDetails.ctTrainees.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={CT_TRAINEE_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.ctTrainees}
                                                            onGridReady={this.onGridReadyCtTrainee}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.ctTrainees && lookaheadDetails.ctTrainees.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onCtTraineeExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="ctTraineeApproved" title="CT Approved"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.ctTraineesApproved && lookaheadDetails.ctTraineesApproved.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={CT_TRAINEE_APPROVED_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.ctTraineesApproved}
                                                            onGridReady={this.onGridReadyCtTraineeApproved}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.ctTraineesApproved && lookaheadDetails.ctTraineesApproved.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onCtTraineeApprovedExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="goTeam" title="Go Team"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.goTeams && lookaheadDetails.goTeams.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={GO_TEAM_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.goTeams}
                                                            onGridReady={this.onGridReadyGoTeam}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.goTeams && lookaheadDetails.goTeams.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onGoTeamExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="tempTeam" title="Temp Team"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.tempTransfers && lookaheadDetails.tempTransfers.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={TEMP_TRANSFER_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.tempTransfers}
                                                            onGridReady={this.onGridReadyTempTeam}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.tempTransfers && lookaheadDetails.tempTransfers.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onTempTeamExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="hireGrpDet" title="Hire Group Detail"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.hireGroupDetails && lookaheadDetails.hireGroupDetails.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={HIRE_GROUP_DETAIL_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.hireGroupDetails}
                                                            onGridReady={this.onGridReadyHireGroupDetail}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.hireGroupDetails && lookaheadDetails.hireGroupDetails.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onHireGroupDetailExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                        <Tab eventKey="hireGrpMap" title="Hire Group Mapping"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            <div className="ag-theme-alpine " style={{ width: "100%" }}>
                                                {lookaheadDetails && lookaheadDetails.hireGroupMaps && lookaheadDetails.hireGroupMaps.length > 0 &&
                                                    <div className="ag-theme-alpine div-45vh mt-4" style={{ width: "100%" }}>
                                                        <AgGridReact
                                                            rowSelection='none'
                                                            defaultColDef={getDefaultColDef}
                                                            columnDefs={HIRE_GROUP_MAPPING_COLUMN_DEFS}
                                                            rowData={lookaheadDetails.hireGroupMaps}
                                                            onGridReady={this.onGridReadyHireGroupMap}
                                                            getRowHeight={getGenericRowHeight}
                                                            suppressContextMenu={true}
                                                            onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                        >
                                                        </AgGridReact>
                                                    </div>
                                                }
                                            </div>
                                            {lookaheadDetails && lookaheadDetails.hireGroupMaps && lookaheadDetails.hireGroupMaps.length > 0 &&
                                                <Row className="action-bar" style={{ marginBottom: "1rem" }}>
                                                    <ButtonToolbar>
                                                        <ButtonGroup className="mr-2">
                                                            <Button variant="success"
                                                                onClick={this.onHiregroupMapExport}
                                                            >Export</Button>
                                                        </ButtonGroup>
                                                    </ButtonToolbar>
                                                </Row>
                                            }
                                        </Tab>
                                    </Tabs>
                                </Modal.Body>
                            </Modal.Dialog>
                        </Form.Group>
                    </Form.Row>
                </Form>
            </>
        );
    }
}
function mapStateToProps(state) {
    const { currLookahead, feedback, lookaheadDetails } = state.lookahead
    const { id, role } = state.app
    return {
        currLookahead,
        feedback,
        id,
        role, lookaheadDetails
    }
}
export const mapDispatchToProps = {
    changeLookaheadCurrTab,
    clearFeedback, getLookaheadFile
}
export default connect(mapStateToProps, mapDispatchToProps)(LookaheadSummary);
