import { But<PERSON>, Form, Modal } from "react-bootstrap";
import React from "react";

class UploadModal extends React.Component {
    constructor(props) {
        super(props);
        const { show = false } = props;
        this.state = {
            show,
            selectedHireGrpFile: undefined
        }
    }
    onApply = () => {
        const { handleSuccess, handleClose } = this.props;
        const { selectedHireGrpFile } = this.state;
        handleClose();
        handleSuccess(selectedHireGrpFile);
    }
    handleHireGrpFileSelect = (event) => {
        const selectedFileObj = event.target.files[0];
        if (selectedFileObj) {
            this.setState({ selectedHireGrpFile: selectedFileObj })
        }
    }
    render() {
        const { selectedHireGrpFile } = this.state;
        const { title, handleClose, successLabel, show } = this.props
        return (<>
            <Modal style={{ opacity: 2 }}
                tabIndex="2"
                role="dialog"
                show={show}
                onHide={handleClose}
                aria-labelledby="contained-modal-title-vcenter"
                centered>
                <Modal.Header closeButton className="settings-model-header"
                    style={{ marginRight: "1rem", alignItems: "center" }}>
                    <Modal.Title style={{ color: 'darkblue' }}> <b>
                        {title}
                    </b>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Row>
                            <Form.Group controlId="formFiles" className="mb-3 custom-file">
                                <Form.Text className="ccp-label">Hire Group Details *</Form.Text>
                                <Form.Label className="custom-file-label mt-4 mb-3">
                                    {selectedHireGrpFile && selectedHireGrpFile.name && <span>Selected File:<b>{selectedHireGrpFile.name}</b>
                                    </span>}
                                    {(!selectedHireGrpFile || !selectedHireGrpFile.name) && <span>Choose Hire Group Details Report*
                                    </span>}
                                </Form.Label>
                                <Form.Control accept=".xls,.xlsx" name="file" required
                                    style={{ backgroundColor: 'green' }} title="Choose Report"
                                    className="custom-file-input" type="file"
                                    onChange={this.handleHireGrpFileSelect} />
                            </Form.Group>
                        </Form.Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer style={{ justifyContent: "center" }}>
                    <Button variant="success" onClick={this.onApply} disabled={(!selectedHireGrpFile || !selectedHireGrpFile.name)}>{successLabel}</Button>
                    <Button variant="warning" onClick={handleClose}>Cancel</Button>
                </Modal.Footer>
            </Modal>
        </>)
    }
}
export default UploadModal;