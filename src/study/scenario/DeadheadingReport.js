import React from "react";
import {<PERSON><PERSON>, <PERSON>, <PERSON>, Modal, Row} from "react-bootstrap";
import {
    CRAFTS,
    DEADHEAD_REPORT_COLUMN_DEFS, MULTI_SORT_KEY, DURATION_TYPES,
} from "../../config/ccpconstants";
import {connect} from "react-redux";
import {AgGridReact} from "ag-grid-react";
import {
    changeDeadheadBoardName, changeDeadheadCraft,
    changeDeadheadDistrict,
    changeDeadheadSubDistrict, changeDeadheadTrainStartsType,
    getDeadheadReport
} from "../studyActions";
import {getDefaultColDef, getExcelFileName, getExcelSheetName, getGenericRowHeight} from "../../util/Utils";

class DeadheadingReport extends React.Component {

    constructor(props) {
        super(props);
       this.state = {
           currRow:undefined
       }

    }

    getReport = () => {
        const{trainStartsType, distr, subDistr, boardName, craft} = this.props.deadhead ||{};
        const{scenarioId} = this.props.currStudy ||{};
        this.props.getDeadheadReport({scenarioId,distr, subDistr, boardName, craft,trainStartsType });
    }
    onBtnExport = () => {
        const {scenarioId, scenarioName} = this.props.currStudy || {}
        const {trainStartsType, distr, subDistr, boardName, craft} = this.props.deadhead || {}
        let params = {
            fileName: getExcelFileName(`Deadhead ${scenarioId} ${scenarioName}`),
            sheetName: getExcelSheetName(`Deadhead ${distr ? distr : ''} ${subDistr ? subDistr : ''} ${boardName ? boardName : ''} ${craft ? craft : ''} ${trainStartsType ? trainStartsType : ''}`)
        }
        this.gridApi.exportDataAsExcel(params);
    };

    onGridReady = params => {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        params.columnApi.autoSizeAllColumns();
    };
    handleRowSelection = (event) =>{
        let selectedRows = event.api.getSelectedRows()
        let currRow = selectedRows.length === 1 ? selectedRows[0]: '';
        this.setState({currRow});
    }
    changeTrainStartsType = (e) => {
        const value = e.target.selectedOptions[0].id;
        this.props.changeDeadheadTrainStartsType(value);
    }
    changeDistrict = (e) => {
        const value = e.target.selectedOptions[0].id;
        this.props.changeDeadheadDistrict(value);
    }
    changeSubDistrict = (e) => {
        const value = e.target.selectedOptions[0].id;
        this.props.changeDeadheadSubDistrict(value);
    }
    changeBoardName = (e) => {
        const value = e.target.selectedOptions[0].id;
        this.props.changeDeadheadBoardName(value);
    }
    changeCraft = (e) => {
        const value = e.target.selectedOptions[0].id;
        this.props.changeDeadheadCraft(value);
    }

    render(){
        const {data, trainStartsType, distr, subDistr, boardName, distrs, subDistrs, boardNames, craft, reportExpired} = this.props.deadhead || {};
        return(
            <>
                <div className="modal-container">
                    <Modal.Dialog style={{maxWidth: "100%", margin: "0rem"}}>
                        <Modal.Header className="settings-model-header">
                            <Modal.Title style={{color: 'darkblue'}}> <b>Deadheading Report</b></Modal.Title>
                        </Modal.Header>

                        <Modal.Body className="ccp-form">
                            <Form>
                                <Form.Group as={Row} controlId="deadhead">

                                    <Form.Label column sm={2} style={{textAlign: "end", paddingRight: "0rem"}}>
                                        <b>Aggregation Type:</b>
                                    </Form.Label>
                                    <Col sm={2}>
                                        <Form.Control
                                            onChange={this.changeTrainStartsType}
                                            as="select">
                                            {DURATION_TYPES.map(item =>
                                                <option key={item.key} id={item.key}
                                                        selected={item.key === trainStartsType}
                                                >{item.desc}</option>
                                            )}
                                        </Form.Control>
                                    </Col>

                                    <Form.Label column sm={1} style={{textAlign: "end", paddingRight: "0rem"}}>
                                        <b>District:</b>
                                    </Form.Label>
                                    <Col sm={2}>
                                        <Form.Control
                                            onChange={this.changeDistrict}
                                            as="select">
                                            <option key="ALL" id="ALL">All</option>
                                            {distrs && distrs.map(item =>
                                                <option key={item} id={item}
                                                        selected={distr === item}
                                                >{item}</option>
                                            )}
                                        </Form.Control>
                                    </Col>
                                    <Form.Label column sm={1} style={{textAlign: "end", paddingRight: "0rem"}}>
                                        <b> Sub district:</b>
                                    </Form.Label>
                                    <Col sm={2}>
                                        <Form.Control
                                            onChange={this.changeSubDistrict}
                                            as="select">
                                            <option key="ALL" id="ALL">All</option>
                                            {subDistrs && subDistrs.map(item =>
                                                <option key={item} id={item}
                                                        selected={subDistr === item}
                                                >{item}</option>
                                            )}
                                        </Form.Control>
                                    </Col>

                                </Form.Group>
                                <Form.Group as={Row} controlId="deadheadl2">
                                    <Form.Label column sm={2} style={{textAlign: "end", paddingRight: "0rem"}}>
                                        <b>Board name:</b>
                                    </Form.Label>
                                    <Col sm={2}>
                                        <Form.Control
                                            onChange={this.changeBoardName}
                                            as="select">
                                            <option key="ALL" id="ALL">All</option>
                                            {boardNames && boardNames.map(item =>
                                                <option key={item} id={item}
                                                        selected={boardName === item}
                                                >{item}</option>
                                            )}
                                        </Form.Control>
                                    </Col>

                                    <Form.Label column sm={1} style={{textAlign: "end", paddingRight: "0rem"}}>
                                        <b>Craft:</b>
                                    </Form.Label>
                                    <Col sm={2} md={2} lg={2}>
                                        <Form.Control
                                            onChange={this.changeCraft}
                                            as="select">
                                            {CRAFTS.map(item =>
                                                <option id={item.key}
                                                        selected={craft === item.key}
                                                >{item.desc}</option>
                                            )}
                                        </Form.Control>
                                    </Col>
                                </Form.Group>
                                <Row className="action-bar" style={{marginBottom: "1rem"}}>
                                    <Button variant="primary"
                                            onClick={this.getReport}
                                    >Get Report</Button>

                                   

                                </Row>

                                    {data &&
                                    <Form.Group as={Row} controlId="scenarioName" style={{marginBottom: "unset"}}
                                                className="tile-container">
                                        <div className="ag-theme-alpine div-50vh" style={{width: "100%"}}>
                                            <AgGridReact
                                                rowSelection='single'
                                                multiSortKey={MULTI_SORT_KEY}
                                                columnDefs={DEADHEAD_REPORT_COLUMN_DEFS}
                                                rowData={data}
                                                suppressContextMenu={true}
                                                onGridReady={this.onGridReady}
                                                defaultColDef={getDefaultColDef}
                                                getRowHeight={getGenericRowHeight}
                                                onRowSelected={(e) => {
                                                    this.handleRowSelection(e)
                                                }}
                                                onFirstDataRendered={(params)=>params.columnApi.autoSizeAllColumns()}
                                            >

                                            </AgGridReact>
                                        </div>
                                    </Form.Group>
                                    }
                                    <Row className="action-bar" style={{marginBottom: "1rem"}}>
                                    
                                    {data && <Button variant="primary" disabled={reportExpired}
                                                                            onClick={this.onBtnExport}
                                    >Export
                                        <img src={`../assets/images/excel.png`} className="label-img" alt=""
                                             style={{paddingLeft: ".5rem"}}/>
                                    </Button>
                                    }
                                    
                                </Row>
                            </Form>
                            {/*{currRow &&*/}
                            {/*<Row>*/}
                            {/*    <b> Deadheading Details:</b>*/}
                            {/*    <div className="ag-theme-alpine div-33vh" style={{width: "100%"}}>*/}
                            {/*        <AgGridReact*/}
                            {/*            rowSelection='single'*/}
                            {/*            columnDefs={DEADHEAD_DETAIL_REPORT_DETAIL_COLUMN_DEFS}*/}
                            {/*            rowData={currRow.details}*/}
                            {/*            suppressContextMenu={true}*/}
                            {/*            defaultColDef={getDefaultColDef}*/}
                            {/*            getRowHeight={getGenericRowHeight}*/}
                            {/*        >*/}
                            {/*        </AgGridReact>*/}
                            {/*    </div>*/}
                            {/*</Row>*/}
                            {/*}*/}
                        </Modal.Body>
                    </Modal.Dialog>
                </div>
            </>
        );
    }
}
export function mapStateToProps (state) {
    const {deadhead, currStudy} = state.study;
    return {
        deadhead,
        currStudy
    }
}
export const mapDispatchToProps ={
    changeDeadheadTrainStartsType,
    changeDeadheadDistrict,
    changeDeadheadSubDistrict,
    changeDeadheadBoardName,
    changeDeadheadCraft,
    getDeadheadReport
}
export default connect(mapStateToProps, mapDispatchToProps) (DeadheadingReport);
export {DeadheadingReport}