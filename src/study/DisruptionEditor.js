import React from "react";
import {connect} from "react-redux"
//import {<PERSON>ton, ButtonGroup, ButtonToolbar, Col, Form, Modal, Row} from "react-bootstrap";
//import {AgGridReact} from "ag-grid-react";
import {getDisruptionEditorReport} from "./studyActions";
import {AVAILABLE_IN_NEXT_VERSION} from "../config/ccpconstants";
//import {getDefaultColDef, getGenericRowHeight} from "../util/Utils";

class DisruptionEditor extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            currRow: undefined
        }
    }
    onGridReady = params => {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
    };
    handleDisruptionSelection = (event) => {
        let selectedRows = event.api.getSelectedRows()
        //let currView = selectedRows.length === 1 ? selectedRows[0].id : '';
      let  currRow = selectedRows[0];
        this.setState({currRow});

    }
    onBtnExport = () => {
        let params = {
        }
        this.gridApi.exportDataAsExcel(params);
    };

    //v2
    getReport = () => {
        this.props.getDisruptionEditorReport({studyId:this.props.currStudy.studyId});
    }

    render() {
      //  const {currRow} = this.state;
        return (
            <h6 style={{textAlign:"center", margin: "1rem"}}>{AVAILABLE_IN_NEXT_VERSION}
            </h6>

            /*<div className="modal-container">
                    <Modal.Dialog style={{maxWidth: "100%", margin: "0rem"}}>
                        <Modal.Header className="settings-model-header">
                            <Modal.Title style={{color: 'darkblue'}}> <b>Disruption Editor</b></Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                            <Row className="action-bar" style={{marginBottom: "1rem"}}>
                                <Button variant="primary"
                                        onClick={this.getReport}
                                >Get Report</Button>
                                {this.props.disruptionEditorReportData && <Button variant="primary"
                                                                                  onClick={this.onBtnExport}
                                >Export
                                    <img src={`../assets/images/excel.png`} className="label-img" alt=""
                                         style={{paddingLeft: ".5rem"}}/>
                                </Button>
                                }

                            </Row>
                            {this.props.disruptionEditorReportData && <Form.Group as={Row}>

                                <div className="ag-theme-alpine div-33vh" style={{width: "100%"}}>
                                    <AgGridReact
                                        rowSelection='single'
                                        modules={this.state.modules}
                                        columnDefs={DISRUPTION_EDITOR_REPORT_COLUMN_DEFS}
                                        rowData={this.props.disruptionEditorReportData}
                                        onGridReady={this.onGridReady}
                                        onRowSelected={(e) => {
                                            this.handleDisruptionSelection(e)
                                        }}
                                        defaultColDef={getDefaultColDef}
                                        getRowHeight={getGenericRowHeight}
                                    >

                                    </AgGridReact>
                                </div>
                            </Form.Group>
                            }
                            {this.props.disruptionEditorReportData &&
                            <Form.Group as={Row} className="action-bar">
                                <ButtonToolbar>
                                    <ButtonGroup className="mr-2">
                                        <Button variant="success" disabled
                                        >Add new disruption</Button>
                                    </ButtonGroup>
                                    {currRow &&
                                    <ButtonGroup className="mr-2">
                                        <Button variant="danger" disabled
                                        >Remove disruption</Button>
                                    </ButtonGroup>
                                    }
                                </ButtonToolbar>
                            </Form.Group>
                            }


                            {currRow && <Row> <Form style={{width: "100%"}}>
                                <Modal.Dialog style={{maxWidth: "100%"}}>

                                    <Modal.Body>
                                        <Form.Row>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Type:</Form.Text>

                                                <Form.Control as="select" style={{width: "50%"}}>
                                                    <option id={currRow.type}>{currRow.type}</option>

                                                </Form.Control>
                                            </Form.Group>

                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Affected Area Type:</Form.Text>
                                                <Form.Control as="select" style={{width: "50%"}}>
                                                    <option
                                                        id={currRow.affectedAreaType}>{currRow.affectedAreaType}</option>

                                                </Form.Control>
                                            </Form.Group>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Duration (hr):</Form.Text>
                                                <Form.Control style={{width: "50%"}}
                                                              type="number"
                                                              defaultValue={currRow.durationHrs}
                                                    // onChange={(event) => handleChange("trnMaxDelayM", event.target.value)}
                                                              id="durationHr"
                                                />
                                            </Form.Group>
                                        </Form.Row>
                                        <Form.Row>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Affected Area Orig:</Form.Text>
                                                <Form.Control type="text" style={{width: "50%", maxWidth: "100%"}}
                                                              value={currRow.affectedAreaOrig}>
                                                </Form.Control>
                                            </Form.Group>

                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Affected Area Dest:</Form.Text>

                                                <Form.Control type="text" style={{width: "50%", maxWidth: "100%"}}
                                                              value={currRow.affectedAreaDest}>
                                                </Form.Control>
                                            </Form.Group>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Seg:</Form.Text>
                                                <Form.Control as="select" style={{width: "50%", maxWidth: "100%"}}>
                                                    <option
                                                        id={currRow.affectedAreaSeg}>{currRow.affectedAreaSeg}</option>

                                                </Form.Control>
                                            </Form.Group>
                                        </Form.Row>
                                        <Form.Row>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Start Time:</Form.Text>
                                                <Form.Group as={Row} style={{width: "70%", marginLeft: "0.1rem"}}>
                                                    <Form.Control className="font-size-small font-weight-600"
                                                                  style={{width: "auto", padding: "0rem"}}
                                                                  required
                                                                  type="datetime-local"
                                                                  value={currRow.startTime}
                                                        // onChange={(event) => handleChange("studyStartDate", event.target.value)}
                                                    />
                                                </Form.Group>
                                            </Form.Group>


                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Up Tm (hr):</Form.Text>

                                                <Form.Control type="number" style={{width: "50%", maxWidth: "100%"}}
                                                              value={currRow.upTmHr}>
                                                </Form.Control>
                                            </Form.Group>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>% Trn Run:</Form.Text>
                                                <Form.Control type="number" min={0} max={100}
                                                              style={{width: "50%", maxWidth: "100%"}}
                                                              value={currRow.pctTrnRun}>
                                                </Form.Control>
                                            </Form.Group>
                                        </Form.Row>
                                        <Form.Row>
                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Cancel Whole Trn:</Form.Text>
                                                <Form.Control as="select" style={{width: "50%"}}>
                                                    <option
                                                        id={currRow.cancelWholeTrn}>{`${currRow.cancelWholeTrn}`}</option>
                                                </Form.Control>
                                            </Form.Group>

                                            <Form.Group as={Col} sm="4">
                                                <Form.Text>Hold Trn @ Orgn:</Form.Text>
                                                <Form.Control as="select" style={{width: "50%"}}>
                                                    <option id={currRow.holdTrnOrgn}>{`${currRow.holdTrnOrgn}`}</option>
                                                </Form.Control>
                                            </Form.Group>
                                        </Form.Row>
                                    </Modal.Body>
                                    <Modal.Footer style={{justifyContent: "center"}}>
                                        <Button variant="success" disabled>Save</Button>
                                        <Button variant="warning" disabled>Reset</Button>
                                    </Modal.Footer>
                                </Modal.Dialog>
                            </Form>
                            </Row>
                            }
                        </Modal.Body>
                    </Modal.Dialog>
                </div>*/
        );
    }
}

export function mapStateToProps(state) {
    const {disruptionEditorReportData, currStudy} = state.study || {};
    return {
        disruptionEditorReportData,
        currStudy
    }
}

export const mapDispatchToProps = {
    getDisruptionEditorReport

}
export default connect(mapStateToProps, mapDispatchToProps)(DisruptionEditor)
export {DisruptionEditor}