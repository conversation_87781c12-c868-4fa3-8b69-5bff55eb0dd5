import React from "react";
import {<PERSON><PERSON>, <PERSON>, Form, InputGroup, Modal} from "react-bootstrap";
import {
    FIELD_REQUIRED,
    OPD_TRAIN_TYPES_NOT_SELECTED,
    HISTORY_TRAIN_TYPES_NOT_SELECTED,
    STUDY_OPD_SCENARIO_SELECTOR_COLUMNS_DEFS,
    STUDY_OPD_PLAN_SELECTOR_COLUMNS_DEFS,
    UPSIDE_REPORT_SELECTOR_COLUMNS_DEFS,
    STUDY_ALGORITHMS,
    STUDY_ALGORITHM_GROUPS
} from "../config/ccpconstants";
import {
    getDay,
    getDefaultColDef,
    getDefaultLocalYardJobDates,
    getTodayISODate,
    getYesterdayISODate,
    geUSDateFromISODate,
    isSameDay
} from "../util/Utils";
import PopupGridRowSelector from "../components/PopupGridRowSelector";
import {connect} from "react-redux";
import {getMasterPlans} from "../plan/planActions";
import {
    addStudy,
    changeCreateStudyOpdTrnType,
    changeCreateStudyHistoryTrnType,
    changeStudyCreateDepartureTimeVariation,
    changeStudyCreateDesc,
    changeStudyCreateEndDate,
    changeStudyCreateLocalYardJobEndDate,
    changeStudyCreateLocalYardJobStartDate,
    changeStudyCreateHistoryStartDate,
    changeStudyCreateHistoryEndDate,
    changeStudyCreateName,
    changeStudyCreateOpdQuickFilter,
    changeStudyCreateUpsideQuickFilter,
    changeStudyCreateOpdScenario,
    changeStudyCreateOpdPlan,
    changeStudyCreateUpsideReport,
    changeStudyCreateStartDate,
    changeStudyCreateTrainSource,
    changeStudyCreateTransitVariation,
    clearFeedback,
    studyCreateReset
} from "./studyActions";
import PopupAlert from "../components/PopupAlert";
import {Hint, Typeahead} from "react-bootstrap-typeahead";
import {isEmpty, isNil} from "ramda";
import {isNullOrUndefined} from "react-svg-pan-zoom/build-commonjs/utils/is";

class StudyCreate extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            validated: false,
            dateRangeValid: true,
            endDateErrorMsg: undefined,
            startDateValid: true,
            startDateErrorMsg: undefined,
            showOPDScenarioSelectorToggle: true,
            missingTrnTypes: false,
            isOpdScenarioValid: true,
            formSubmitted: false,
            localYardJobDateRangeValid: false,
            localYardJobEndDateErrorMsg: undefined,
            localYardJobEndDate: this.props.studyCreate.localYardJobEndDate,
            localYardJobStartDate: this.props.studyCreate.localYardJobStartDate
        }
    }

    formRef = React.createRef();
    opdScenarioRef = React.createRef();
    opdPlanRef = React.createRef();
    upsideReportRef=React.createRef();

    handleSubmit = (event) => {
        const form = event.currentTarget;
        event.preventDefault();
        const {
            opdTrnTypes = [], historyTrnTypes = [],
            opdScenario = {},
            opdPlan = {},
            studyAlgorithm, startDate,
            upsideReport={}
        } = this.props.studyCreate || {};
        let dateRangeValid = this.validateEndDate(opdScenario);
        const isStudyAlgorithmOPD = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.OPD);
        const isStudyAlgorithmHybrid = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.HYBRID);
        const isStudyAlgorithmScenario = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.SCENARIO);
        const isStudyAlgorithmPlan = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.PLAN);

        let localYardJobDateRangeValid = isStudyAlgorithmOPD && this.validateLocalYardJobEndDate();
        let noOfOPDTrnTypesSelections = opdTrnTypes.reduce((acc, item) => item.selected ? acc + 1 : acc, 0);
        let noOfHistoryTrnTypesSelections = historyTrnTypes.reduce((acc, item) => item.selected ? acc + 1 : acc, 0);
        const missingOpdScenario = isStudyAlgorithmScenario && isEmpty(opdScenario);
        const missingOpdPlan = isStudyAlgorithmPlan && isEmpty(opdPlan);
        const missingTrnTypes = isStudyAlgorithmOPD
            && noOfOPDTrnTypesSelections === 0;
        const missingHistoryTrnTypes = isStudyAlgorithmHybrid
            && noOfHistoryTrnTypesSelections === 0;

        this.setState({
            missingOpdScenario: missingOpdScenario,
            missingOpdPlan: missingOpdPlan,
            missingTrnTypes: missingTrnTypes,
            missingHistoryTrnTypes: missingHistoryTrnTypes,
            formSubmitted: true
        });
        //check local yard job has same day
        const localYardJobStartDate = form.localYardJobStartDate && form.localYardJobStartDate.value;
        const isLocalYardJobStartDateValid = isStudyAlgorithmOPD && this.isLocalYardJobStartDateValid();
        const isHistoryStartDateValid = isStudyAlgorithmHybrid && this.isHistoryStartDateValid()
        const startDateValid = this.validateStartDate();
        let invalid = form.checkValidity() === false
            || !dateRangeValid
            || !startDateValid;
        if (isStudyAlgorithmOPD) {
            invalid = invalid || !localYardJobStartDate
                || !localYardJobDateRangeValid
                || !isLocalYardJobStartDateValid
                || missingTrnTypes;
        }
        if (isStudyAlgorithmHybrid) {
            invalid = invalid || !isHistoryStartDateValid
                || missingHistoryTrnTypes;
        }
        if (isStudyAlgorithmScenario) {
            invalid = invalid || missingOpdScenario;
        }
        if (isStudyAlgorithmPlan) {
            invalid = invalid || missingOpdPlan;
        }

        if (invalid) {
            event.stopPropagation();
            if (isStudyAlgorithmOPD) {
                if (!isLocalYardJobStartDateValid) {
                    this.setLocalYardJobErrorMessage(localYardJobStartDate, startDate);
                }
                if (missingTrnTypes) {
                    this.setTrnTypesErrorMessage();
                }
            }
            if (isStudyAlgorithmHybrid) {
                if (missingHistoryTrnTypes) {
                    this.setHistoryTrnTypesErrorMessage();
                }
            }

            this.setState({validated: true})
        } else {
            const {
                name,
                desc,
                endDate,
                transitVariation,
                departureTimeVariation
            } = this.props.studyCreate || {};
            const trnTypes = isStudyAlgorithmOPD ? opdTrnTypes.filter(item => item.selected === true).map(item => item.key).join(',') : ``;
            const historicalTrnTypes = isStudyAlgorithmOPD ? historyTrnTypes.filter(item => item.selected === true).map(item => item.key).join(',') : ``;
            // history start date / end date
            let historyStartDate = form.historyStartDate && form.historyStartDate.value;
            let addStudyObject = {
                name,
                desc,
                startDate,
                endDate,
                algorithm: studyAlgorithm.key
            };
            if (isStudyAlgorithmOPD) {
                addStudyObject = {
                    ...addStudyObject,
                    transitDist: transitVariation,
                    deptDist: departureTimeVariation,
                    opdTrnTypes: trnTypes,
                    localYardJobStartDate
                };
            }
            if (isStudyAlgorithmScenario) {
                addStudyObject = {
                    ...addStudyObject,
                    opdScenario: opdScenario.scenSa
                };
            }
            if (isStudyAlgorithmPlan) {
                addStudyObject = {
                    ...addStudyObject,
                    opdPlan: opdPlan.planSa
                };
            }
            if (isStudyAlgorithmHybrid) {
                addStudyObject = {
                    ...addStudyObject,
                    historicalTrnTypes,
                    historyStartDate
                };
            }
           if (upsideReport && upsideReport.upsideId) {
                addStudyObject = {
                    ...addStudyObject,
                    upsideId: upsideReport.upsideId
                };
            }
            this.props.addStudy(addStudyObject);
            this.setState({validated: false})
        }
    };
    selectOPDScenario = (row) => {
        const opdScenario = row;
        const {scenSa, scenarioName} = opdScenario || {}
        this.setState({showOPDScenarioSelector: false, showOPDScenarioSelectorToggle: false})
        this.props.changeStudyCreateOpdScenario(opdScenario)
        this.props.changeStudyCreateOpdQuickFilter(this.getOpdScenarioLabel(scenSa, scenarioName))
        this.validateEndDate(opdScenario);
        this.validateOpdScenario(opdScenario)
        const {studyStartDt = ``, studyEndDt = ``, targetStartDt = ``, targetEndDt = ``} = opdScenario || {}
        if (isEmpty(targetStartDt) || isEmpty(targetEndDt)) {
            this.invokePreValidation(studyStartDt, studyEndDt)
        } else {
            this.invokePreValidation(targetStartDt, targetEndDt)
        }
    }
    selectOpdScenarioTypeahead = (option) => {
        if (option.length > 0) {
            const {scenSa, scenarioName} = option[0];
            const {opdScenarios} = this.props;
            const opdScenario = opdScenarios.find(item => item.scenSa === scenSa);
            const {studyStartDt = ``, studyEndDt = ``, targetStartDt = ``, targetEndDt = ``} = opdScenario || {}
            this.props.changeStudyCreateOpdQuickFilter(this.getOpdScenarioLabel(scenSa, scenarioName))
            this.props.changeStudyCreateOpdScenario(opdScenario)
            this.validateOpdScenario(opdScenario)
            if (isEmpty(targetStartDt) || isEmpty(targetEndDt)) {
                this.invokePreValidation(studyStartDt, studyEndDt)
            } else {
                this.invokePreValidation(targetStartDt, targetEndDt)
            }
            // this.invokePreValidation(studyStartDt, studyEndDt)
        } else {
            this.opdScenarioRef.current.clear();
            this.props.changeStudyCreateOpdQuickFilter(``)
            this.props.changeStudyCreateOpdScenario({})
            this.validateOpdScenario(``)
            this.invokePreValidation()
        }
        //handle side effect of form already validated and end date is empty
        const form = this.formRef.current
        form.localYardJobEndDate && form.localYardJobEndDate.setCustomValidity('')
    }
    hideOPDScenarioSelector = () => {
        this.setState({showOPDScenarioSelector: false, showOPDScenarioSelectorToggle: false})
    }
    showOPDScenarioSelector = (event) => {
        this.setState({showOPDScenarioSelector: true})
    }
    showOPDScenarioSelectorChange = (event) => {
        this.setState({showOPDScenarioSelector: true})
    }
    selectOPDPlan = (row) => {
        const opdPlan = row;
        const {planSa, planName} = opdPlan || {}
        this.setState({showOPDPlanSelector: false, showOPDPlanSelectorToggle: false})
        this.props.changeStudyCreateOpdPlan(opdPlan)
        this.props.changeStudyCreateOpdQuickFilter(this.getOpdPlanLabel(planSa, planName));
        this.validateOpdPlan(opdPlan)
        const {studyStartDt = ``, studyEndDt = ``, targetStartDate = ``, targetEndDate = ``} = opdPlan || {}
        if (isEmpty(targetStartDate) || isEmpty(targetEndDate)) {
            this.invokePreValidation(studyStartDt, studyEndDt)
        } else {
            this.invokePreValidation(targetStartDate, targetEndDate)
        } 
    }

    selectOpdPlanTypeahead = (option) => {
        if (option.length > 0) {
            const {planSa, planName} = option[0];
            const {opdPlans} = this.props;
            const opdPlan = opdPlans.find(item => item.planSa === planSa);
            const {studyStartDt = ``, studyEndDt = ``, targetStartDt = ``, targetEndDt = ``} = opdPlan || {}
            this.props.changeStudyCreateOpdQuickFilter(this.getOpdPlanLabel(planSa, planName))
            this.props.changeStudyCreateOpdPlan(opdPlan)
            this.validateOpdPlan(opdPlan)
            if (isEmpty(targetStartDt) || isEmpty(targetEndDt)) {
                this.invokePreValidation(studyStartDt, studyEndDt)
            } else {
                this.invokePreValidation(targetStartDt, targetEndDt)
            }
            // this.invokePreValidation(studyStartDt, studyEndDt)
        } else {
            this.opdPlanRef.current.clear();
            this.props.changeStudyCreateOpdQuickFilter(``)
            this.props.changeStudyCreateOpdPlan({})
            this.validateOpdPlan(``)
            this.invokePreValidation()
        }
        //handle side effect of form already validated and end date is empty
        const form = this.formRef.current
        form.localYardJobEndDate && form.localYardJobEndDate.setCustomValidity('')
    }
    hideOPDPlanSelector = () => {
        this.setState({showOPDPlanSelector: false, showOPDPlanSelectorToggle: false})
    }
    showOPDPlanSelector = (event) => {
        this.setState({showOPDPlanSelector: true})
    }

    selectUpsideReport = (row) => {
        const upsideReport = row;
        const {upsideName, upsideDesc,userName} = upsideReport || {}
        this.setState({showUpsideReportSelector: false, showUpsideReportSelectorToggle: false})
        this.props.changeStudyCreateUpsideReport(upsideReport)
        this.props.changeStudyCreateUpsideQuickFilter(this.getUpsideReportLabel(upsideName, upsideDesc));
      
    }

    
    selectUpsideReportTypeahead = (option) => {
        if (option.length > 0) {
            const {upsideName, upsideDesc,userName,upsideId} = option[0];
            const {upsideReports} = this.props;
            const upsideReport = upsideReports.find(item => item.upsideId === upsideId);
           // const {studyStartDt = ``, studyEndDt = ``, targetStartDt = ``, targetEndDt = ``} = opdPlan || {}
            this.props.changeStudyCreateUpsideQuickFilter(this.getUpsideReportLabel(upsideName, upsideDesc))
            this.props.changeStudyCreateUpsideReport(upsideReport)
          
        } else {
            this.upsideReportRef.current.clear();
            this.props.changeStudyCreateUpsideQuickFilter(``)
            this.props.changeStudyCreateUpsideReport({})
           
        }
       
    }
    hideUpsideReportSelector = () => {
        this.setState({showUpsideReportSelector: false, showUpsideReportSelectorToggle: false})
    }
    showUpsideReportSelector = (event) => {
        this.setState({showUpsideReportSelector: true})
    }
   
    changeStartDateReq = (value) => {
        this.props.changeStudyCreateStartDate(value);
    }
    changeEndDateReq = (value) => {
        this.props.changeStudyCreateEndDate(value);
    }
    changeStartDate = (event) => {
        this.changeStartDateReq(event.target.value)
    }
    validateStartDateChange = (event) => {
        //  this.changeStartDateReq(event.target.value)
        const {opdScenario = {}} = this.props.studyCreate || {}
        const form = this.formRef.current
        const studyEndDt = form.endDate.value;
        this.validateStartDate()
        this.invokePreValidation(event.target.value, studyEndDt);
        this.validateEndDate(opdScenario);
        this.handleDateRangeWarning(event.target.value);
        if (this.state.isStudyAlgorithmOPD) {
            this.validateLocalYardJobEndDate();
            this.validateLocalYardJobStartDate();
            if (this.state.isStudyAlgorithmHybrid) {
                this.validateHistoryEndDate();
                this.validateHistoryStartDate();
            }
        }
    }
    changeEndDate = (event) => {
        this.changeEndDateReq(event.target.value);
    }

    validateEndDateChange = (event) => {
        //  this.changeEndDateReq(event.target.value);
        const {opdScenario = {}} = this.props.studyCreate || {}
        const form = this.formRef.current
        const studyStartDt = form.startDate.value;
        this.invokePreValidation(studyStartDt, event.target.value);
        this.validateEndDate(opdScenario)
        this.handleDateRangeWarning(event.target.value);
        if (this.state.isStudyAlgorithmOPD) {
            this.validateLocalYardJobStartDate();
            this.validateLocalYardJobEndDate();
            if (this.state.isStudyAlgorithmHybrid) {
                this.validateHistoryStartDate()
                this.validateHistoryEndDate();
            }
        }
    }

    invokePreValidation = (startDt, endDt) => {
        const ref = this.formRef.current;
        const {studyAlgorithm} = this.props.studyCreate || {};
        const isStudyAlgorithmOPD = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.OPD);
        if (!isStudyAlgorithmOPD) {
            this.setState({validated: true})
            return
        }
        const {start = ``, end = ``} = getDefaultLocalYardJobDates({start: startDt, end: endDt})
        ref.localYardJobStartDate.value = start;
        ref.localYardJobEndDate.value = end;
        let isHybrid = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.HYBRID);
        if (isHybrid) {
            ref.historyStartDate.value = start;
            ref.historyEndDate.value = end;
            this.setState({
                validated: true,
                localYardJobStartDate: start, localYardJobEndDate: end,
                historyStartDate: start, historyEndDate: end
            });
        } else {
            this.setState({
                validated: true,
                localYardJobStartDate: start, localYardJobEndDate: end
            });
        }
    }
    changeLocalYardJobStartDate = (event) => {
        this.props.changeStudyCreateLocalYardJobStartDate(event.target.value);
        this.setState({validated: true, localYardJobStartDate: event.target.value})
    }
    changeLocalYardJobEndDate = (event) => {
        this.props.changeStudyCreateLocalYardJobEndDate(event.target.value);
        this.setState({validated: true, localYardJobEndDate: event.target.value})
    }
    changeHistoryStartDate = (event) => {
        this.props.changeStudyCreateHistoryStartDate(event.target.value);
        this.setState({validated: true, historyStartDate: event.target.value})
    }
    changeHistoryEndDate = (event) => {
        this.props.changeStudyCreateHistoryEndDate(event.target.value);
        this.setState({validated: true, historyEndDate: event.target.value})
    }
    validateLocalYardJobStartDate = (event) => {
        this.validateLocalYardJobEndDate()
        this.validateLocalYardJobStartDate()
    }
    validateLocalYardJobEndDate = (event) => {
        this.validateLocalYardJobEndDate()
    }
    validateHistoryStartDate = (event) => {
        this.validateHistoryEndDate()
        this.validateHistoryStartDate()
    }
    validateLocalYardJobEndDate = (event) => {
        this.validateLocalYardJobEndDate()
    }
    validateHistoryEndDate = (event) => {
        this.validateHistoryEndDate()
    }

    handleDateRangeWarning = (dateL) => {
        const {opdScenario = {}} = this.props.studyCreate || {}
        if (!isEmpty(opdScenario)) {
            const {targetStartDt, targetEndDt} = opdScenario || {}
            const stDt = Date.parse(targetStartDt)
            const endDt = Date.parse(targetEndDt)
            const dateLcl = Date.parse(dateL)
            if (dateLcl < stDt || dateLcl > endDt) {
                let message = `You might be missing the Unit Trains, Please select Study date range between ${geUSDateFromISODate(targetStartDt)} and ${geUSDateFromISODate(targetEndDt)}`;
                this.setState({feedback: {message, type: 'warning'}})
            }
        }
    }
    onOpdFilterChanged = (value) => {
        if (value) {
            this.props.changeStudyCreateOpdQuickFilter(value)
        } else {
            this.props.changeStudyCreateOpdQuickFilter(``)
        }
        this.props.changeStudyCreateOpdScenario({})
    }
    onUpsideFilterChanged = (value) => {
        if (value) {
            this.props.changeStudyCreateUpsideQuickFilter(value)
        } else {
            this.props.changeStudyCreateUpsideQuickFilter(``)
        }
        this.props.changeStudyCreateUpsideReport({})
    }

    handleClose = (e) => {
        this.setState({feedback: undefined})
    }

    validateOpdScenario = (opdScenario) => {
        const missingOpdScenario = isEmpty(opdScenario);
        this.setState({missingOpdScenario: missingOpdScenario, isOpdScenarioValid: !missingOpdScenario})
        const form = this.formRef.current;
        form.endDate.setCustomValidity(``)
        if (!missingOpdScenario) {
            const {targetStartDt, targetEndDt} = opdScenario || {}
            if (isNil(targetStartDt) || isNil(targetEndDt)) {
                let message = `The plan for this scenario does not have unit trains from UTG.`;
                this.setState({feedback: {message, type: 'warning'}})
            }
        }
    }
    validateOpdPlan = (opdPlan) => {
        const missingOpdPlan = isEmpty(opdPlan);
        this.setState({missingOpdPlan: missingOpdPlan, isOpdPlanValid: !missingOpdPlan})
        const form = this.formRef.current;
        form.endDate.setCustomValidity(``)
        if (!missingOpdPlan) {
            const {targetStartDate, targetEndDate} = opdPlan || {}
            if (isNil(targetStartDate) || isNil(targetEndDate)) {
                let message = `The plan does not have unit trains from UTG.`;
                this.setState({feedback: {message, type: 'warning'}})
            }
        }
    }

    validateLocalYardJobStartDate = () => {
        const {studyAlgorithm} = this.props.studyCreate || {};
        if (studyAlgorithm === `HISTORY`) {
            return
        }
        const isSameDay = this.isLocalYardJobStartDateValid()
        const form = this.formRef.current;
        const startDate = form.startDate.value;
        const localYardJobStartDate = form.localYardJobStartDate && form.localYardJobStartDate.value;
        if (!isSameDay && form.localYardJobStartDate.checkValidity()) {
            this.setLocalYardJobErrorMessage(localYardJobStartDate, startDate)
        }
    }
    isLocalYardJobStartDateValid = () => {
        const form = this.formRef.current;
        const startDate = form.startDate.value;
        const localYardJobStartDate = form.localYardJobStartDate && form.localYardJobStartDate.value;
        return isSameDay(startDate, localYardJobStartDate)
    }
    isHistoryStartDateValid = () => {
        const form = this.formRef.current;
        const startDate = form.startDate.value;
        const historyStartDate = form.historyStartDate && form.historyStartDate.value;
        return isSameDay(startDate, historyStartDate)
    }
    setLocalYardJobErrorMessage = (localYardJobStartDate, startDate) => {
        let message = `Day of the week (${getDay(localYardJobStartDate)}) for Local Yard Job Start date is different from study start date, Please select the day of week (${startDate ? getDay(startDate) : 'EMPTY'}) same for OPD study start date as a start date here.`;
        this.updateFeedbackMessage('error', message)
    }
    setTrnTypesErrorMessage = () => {
        let message = `OPD train types(s) required.`;
        this.updateFeedbackMessage('error', message)
    }
    setHistoryTrnTypesErrorMessage = () => {
        let message = `History train types(s) required.`;
        this.updateFeedbackMessage('error', message)
    }
    validateHistoryStartDate = () => {
        const {studyAlgorithm} = this.props.studyCreate || {};
        if (!STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.HYBRID)) {
            return;
        }
        const isSameDay = this.isHistoryStartDateValid()
        const form = this.formRef.current;
        const startDate = form.startDate.value;
        const historyStartDate = form.historyStartDate && form.historyStartDate.value;
        if (!isSameDay && form.historyStartDate.checkValidity()) {
            this.setHistoryErrorMessage(historyStartDate, startDate)
        } else {
            const result = this.validateHistoryEndDate();
            if (!result) {
                this.setHistoryDateRangeErrorMessage();
                this.setState({
                    historyStartDate: startDate
                });
            }
        }

    }
    isLocalHistoryDateValid = () => {
        const form = this.formRef.current;
        const startDate = form.startDate.value;
        const historyStartDate = form.historyStartDate && form.historyStartDate.value;
        return isSameDay(startDate, historyStartDate)
    }
    setHistoryErrorMessage = (historyStartDate, startDate) => {
        let message = `Day of the week (${getDay(historyStartDate)}) for History Start date is different from study start date, Please select the day of week (${startDate ? getDay(startDate) : 'EMPTY'}) same for OPD study start date as a start date here.`;
        this.updateFeedbackMessage('error', message)
    }
    setHistoryDateRangeErrorMessage = (historyStartDate,) => {
        let message = "NOT VALIDATE RANGE: Part of your history date selection goes into future dates so dates are reset to default dates";
        this.updateFeedbackMessage('error', message)
    }
    updateFeedbackMessage = (type, message) => {
        this.setState({feedback: {message, type}})
    }

    validateStartDate = () => {
        const form = this.formRef.current;
        const {studyAlgorithm} = this.props.studyCreate || {};
        if (studyAlgorithm !== `HISTORY`) {
            return true;
        }
        const validDt = getYesterdayISODate();
        const startDt = form.startDate.valueAsNumber;
        const startDateValid = startDt < Date.parse(validDt);
        let startDateErrorMsg = startDateValid
            ?
            undefined
            : `Start date should be prior to ${geUSDateFromISODate(validDt)}`;

        startDateValid
            ? form.startDate.setCustomValidity('')
            : form.startDate.setCustomValidity('d');

        this.setState({
            startDateValid,
            startDateErrorMsg
        })
        return startDateValid
    }

    validateEndDate = (opdScenario) => {
        const form = this.formRef.current;
        let rangeValid = form.startDate.valueAsNumber < form.endDate.valueAsNumber
        const {studyAlgorithm} = this.props.studyCreate || {};
        const validDt = getYesterdayISODate();
        const endDt = form.endDate.valueAsNumber;
        const endDateValid = endDt <= Date.parse(validDt);
        const historyDateValid = studyAlgorithm === `HISTORY` ? endDateValid : true
        let endDateErrorMsg = rangeValid
            ?
            historyDateValid ?
                undefined :
                `End date should be prior or equal to ${geUSDateFromISODate(validDt)}`
            : `End date must be later than Start date`;
        let dateRangeValid = (rangeValid && historyDateValid)

        dateRangeValid
            ? form.endDate.setCustomValidity('')
            : form.endDate.setCustomValidity('d');

        this.setState({
            dateRangeValid,
            endDateErrorMsg
        })
        return dateRangeValid
    }

    validateLocalYardJobEndDate = () => {
        const form = this.formRef.current;
        const {studyAlgorithm} = this.props.studyCreate || {};
        if (studyAlgorithm === `HISTORY`) {
            return
        }
        let rangeValid = form.localYardJobStartDate.valueAsNumber < form.localYardJobEndDate.valueAsNumber
        const lclYrdEndDt = Date.parse(form.localYardJobEndDate.value)
        const today = Date.parse(getTodayISODate())
        let localYardJobEndDateErrorMsg = rangeValid
            ? lclYrdEndDt >= today ?
                `Local Yard Job End date must be prior to Today`
                : undefined
            : `Local Yard Job End date must be later than Local Yard Job Start date`;
        !localYardJobEndDateErrorMsg
            ? form.localYardJobEndDate.setCustomValidity('')
            : form.localYardJobEndDate.setCustomValidity('d');
        let localYardJobDateRangeValid = isNil(localYardJobEndDateErrorMsg)

        this.setState({
            localYardJobDateRangeValid: localYardJobDateRangeValid,
            localYardJobEndDateErrorMsg
        })
        return localYardJobDateRangeValid
    }

    validateHistoryEndDate = () => {
        const form = this.formRef.current;
        const {studyAlgorithm} = this.props.studyCreate || {};
        if (!STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.HYBRID)) {
            return
        }
        // let rangeValid = form.historyStartDate.valueAsNumber < form.historyEndDate.valueAsNumber

        const lclYrdEndDt = Date.parse(form.historyEndDate.value)
        const today = Date.parse(getTodayISODate());
        let rangeValid = lclYrdEndDt < today;

        this.setState({
            historyDateRangeValid: rangeValid
        })
        return rangeValid;
    }

    handleNameChange = (event) => {
        this.props.changeStudyCreateName(event.target.value);
    }
    handleTrainSourceChange = (event) => {
        let studyAlgorithmId = event.target.selectedOptions[0].id;
        let studyAlgorithm = STUDY_ALGORITHMS[0];
        STUDY_ALGORITHMS.forEach(value => {
            if (value.key === studyAlgorithmId) studyAlgorithm = value;
        });
        this.props.changeStudyCreateTrainSource(studyAlgorithm);
        this.setState({
            localYardJobEndDate: undefined,
            localYardJobStartDate: undefined,
            historyEndDate: undefined,
            historyStartDate: undefined,
            studyAlgorithm
        });
    }

    handleDescChange = (event) => {
        this.props.changeStudyCreateDesc(event.target.value);
    }
    handleTransitVariationChange = (event) => {
        this.props.changeStudyCreateTransitVariation(event.target.checked);
    }
    handleDepartureTimeVariationChange = (event) => {
        this.props.changeStudyCreateDepartureTimeVariation(event.target.checked);
    }

    handleOpdTrainTypeSelection = (key, selected) => {
        this.props.changeCreateStudyOpdTrnType({key, selected});
    }
    handleHistoryTrainTypeSelection = (key, selected) => {
        this.props.changeCreateStudyHistoryTrnType({key, selected});
    }

    clear = () => {
        this.reset();
        this.props.feedback && this.props.clearFeedback();
        this.setState({
            dateRangeValid: true, validated: false, missingTrnTypes: false, missingOpdScenario: false,
            endDateErrorMsg: undefined, localYardJobStartDate: undefined, localYardJobEndDate: undefined,
            startDateErrorMsg: undefined, startDateValid: true
        })
    }

    reset = () => {
        this.props.studyCreateReset();
        this.formRef.current && this.formRef.current.studyName.focus();
    }
    onGridReady = params => {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        this.gridColumnApi.autoSizeAllColumns();
        const {opdScenario} = this.props.studyCreate || {};
        (opdScenario !== undefined && opdScenario.id !== undefined) && this.gridApi.forEachNode((node) => {
            if ((node.data.id === opdScenario.id)) {
                node.setSelected(true)
                this.gridColumnApi.ensureIndexVisible(opdScenario.id);
            }
        })
    };

    getOpdScenarioLabel = (scenSa, scenarioName) => {
        return `${scenSa} - ${scenarioName}`;
    }

    getUpsideReportLabel = (name, desc) => {
        return `${name} - ${desc}`;
    }

    getOpdPlanLabel = (planSa, planName) => {
        return `${planSa} - ${planName}`;
    }

    componentDidMount() {
        const {name} = this.props.studyCreate || {};
        (!name && this.formRef.current) && this.formRef.current.studyName.focus();
    }

    render() {
        const {
            opdScenario = {},
            opdPlan = {},
            upsideReport ={},
            name,
            desc,
            studyAlgorithm,
            transitVariation,
            departureTimeVariation,
            startDate,
            endDate,
            updated,
            opdTrnTypes,
            historyTrnTypes,
            opdQuickFilter,
            upsideReportQuickFilter

        } = this.props.studyCreate || {};

        const isStudyAlgorithmOPD = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.OPD);
        const isStudyAlgorithmHybrid = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.HYBRID);
        ;
        const isStudyAlgorithmPlan = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.PLAN);
        ;
        const isStudyAlgorithmScenario = STUDY_ALGORITHM_GROUPS.isInGroup(studyAlgorithm, STUDY_ALGORITHM_GROUPS.SCENARIO);
        const {
            missingTrnTypes, missingHistoryTrnTypes, missingOpdScenario, missingOpdPlan, validated, formSubmitted,
            localYardJobStartDate,
            historyStartDate
        } = this.state || {}
        let {
            localYardJobEndDate,
            historyEndDate
        } = this.state || {}
        const {opdScenarios,upsideReports} = this.props || {}
        const {opdPlans} = this.props || {}
        if (isStudyAlgorithmOPD) {
            if (isNullOrUndefined(startDate) || isNullOrUndefined(endDate) || isNullOrUndefined(localYardJobStartDate) || isNullOrUndefined(localYardJobEndDate)) {
            } else {
                const startDate1 = Date.parse(startDate);
                const endDate1 = Date.parse(endDate);
                const localYardJobStartDate1 = Date.parse(localYardJobStartDate);
                let localYardJobEndDate1 = Date.parse(localYardJobEndDate);
                if (isNaN(startDate1) || isNaN(endDate1) || isNaN(localYardJobStartDate1) || isNaN(localYardJobEndDate1)) {
                } else {
                    const diffInTime = endDate1 - startDate1;
                    const diffInDays = diffInTime / (1000 * 60 * 60 * 24);
                    const timeToBeAdded = diffInDays * (1000 * 60 * 60 * 24);
                    localYardJobEndDate1 = localYardJobStartDate1 + timeToBeAdded;
                    const localYardJobEndDate2 = new Date(localYardJobEndDate1);
                    localYardJobEndDate = localYardJobEndDate2.toISOString().substring(0, 10)
                    if (isStudyAlgorithmHybrid) {
                        if (isNullOrUndefined(historyStartDate) || isNullOrUndefined(historyEndDate)) {
                        } else {
                            const historyStartDate1 = Date.parse(historyStartDate);
                            let historyEndDate1 = Date.parse(historyEndDate);
                            if (isNaN(historyStartDate1) || isNaN(historyEndDate1)) {
                            } else {
                                historyEndDate1 = historyStartDate1 + timeToBeAdded;
                                const historyEndDate2 = new Date(historyEndDate1);
                                historyEndDate = historyEndDate2.toISOString().substring(0, 10)
                            }
                        }
                    }
                }
            }
        }
        let disableHistoryStartDate = isStudyAlgorithmHybrid;
        if (isStudyAlgorithmScenario) {
            disableHistoryStartDate = disableHistoryStartDate && isEmpty(opdScenario);
        }
        if (isStudyAlgorithmPlan) {
            disableHistoryStartDate = disableHistoryStartDate && isEmpty(opdPlan);
        }

        return (
            <>
                <Form noValidate validated={validated} onSubmit={this.handleSubmit} ref={this.formRef}
                      className="font-weight-600 font-size-small">
                    <Form.Row sm={12}>
                        <Form.Group as={Col} controlId="validationCustom01">
                            <Modal.Dialog style={{maxWidth: "100%", margin: "0rem"}}>
                                <Modal.Header className="settings-model-header">
                                    <Modal.Title style={{color: 'darkblue'}}> <b>Create a Study</b></Modal.Title>
                                </Modal.Header>
                                <Modal.Body className="ccp-form">
                                    <Form.Row>
                                        <Form.Group as={Col} sm={6} controlId="studyName">
                                            <Form.Text className="ccp-label">Study name *</Form.Text>
                                            <Form.Control
                                                required
                                                type="text"
                                                maxLength={128}
                                                value={name}
                                                onChange={this.handleNameChange}
                                                placeholder="Study Name"
                                            />
                                            <Form.Control.Feedback
                                                type="invalid">{FIELD_REQUIRED}</Form.Control.Feedback>
                                        </Form.Group>
                                        <Form.Group as={Col} sm={6} controlId="description">
                                            <Form.Text className="ccp-label">Description</Form.Text>
                                            <Form.Control
                                                as="textarea"
                                                value={desc}
                                                maxLength={1024}
                                                onChange={this.handleDescChange}
                                                placeholder="Description"
                                            />
                                            <Form.Control.Feedback
                                                type="invalid">{FIELD_REQUIRED}</Form.Control.Feedback>
                                        </Form.Group>
                                    </Form.Row>
                                    <Form.Row>
                                        <Form.Group as={Col} sm={6} controlId="studyAlgorithm">
                                            <Form.Text className="ccp-label">Train source</Form.Text>
                                            <Form.Control onChange={this.handleTrainSourceChange}
                                                          as="select">
                                                {
                                                    STUDY_ALGORITHMS.map(item =>
                                                        <option key={item.key} id={item.key}
                                                                selected={studyAlgorithm === item.key}>{item.desc}</option>
                                                    )
                                                }
                                                ]
                                            </Form.Control>
                                        </Form.Group>
                                    </Form.Row>
                                    <Form.Row>
                                        {isStudyAlgorithmOPD && <>
                                            {isStudyAlgorithmScenario &&
                                            <Form.Group as={Col} sm={6} controlId="opd-scenario">
                                                <Form.Text className="ccp-label">OPD scenario *</Form.Text>
                                                <InputGroup>
                                                    <Typeahead ref={this.opdScenarioRef}
                                                               id="opd-scenario-select"
                                                               inputProps={{required: true}}
                                                               renderInput={({
                                                                                 inputRef,
                                                                                 referenceElementRef,
                                                                                 ...inputProps
                                                                             }) => (
                                                                   <Hint>
                                                                       <Form.Control
                                                                           {...inputProps}
                                                                           ref={(node) => {
                                                                               if (node) {
                                                                                   node.value = opdQuickFilter;
                                                                               }
                                                                               inputRef(node);
                                                                               referenceElementRef(node);
                                                                           }}
                                                                       />
                                                                   </Hint>
                                                               )}
                                                               clearButton
                                                               labelKey={option => this.getOpdScenarioLabel(option.scenSa, option.scenarioName)}
                                                               onInputChange={(text, e) => {
                                                                   this.onOpdFilterChanged(text)
                                                               }}
                                                               onChange={this.selectOpdScenarioTypeahead}
                                                               options={opdScenarios}
                                                               value={opdQuickFilter}
                                                               placeholder="OPD Scenario"
                                                               selected={Object.keys(opdScenario).length > 0 ? [opdScenario] : []}
                                                    />
                                                    <InputGroup.Append>
                                                        <Button variant="outline-light"
                                                                onClick={this.showOPDScenarioSelector}
                                                                style={{borderColor: "#ced4da"}}>
                                                            <img src={`../assets/images/search.svg`}
                                                                 className="label-img"
                                                                 alt="" style={{
                                                                paddingLeft: "0rem",
                                                                borderRadius: "0rem",
                                                                width: "1rem",
                                                                height: "1rem"
                                                            }}/>
                                                        </Button>
                                                    </InputGroup.Append>
                                                </InputGroup>
                                                {(formSubmitted && missingOpdScenario) ?
                                                    <div className={`invalid-feedback`}
                                                         style={{display: "block"}}>{FIELD_REQUIRED}</div> : ``}
                                            </Form.Group>}
                                            {isStudyAlgorithmPlan &&
                                            <Form.Group as={Col} sm={6} controlId="opd-plan">
                                                <Form.Text className="ccp-label">OPD plan *</Form.Text>
                                                <InputGroup>
                                                    <Typeahead ref={this.opdPlanRef}
                                                               id="opd-plan-select"
                                                               inputProps={{required: true}}
                                                               renderInput={({
                                                                                 inputRef,
                                                                                 referenceElementRef,
                                                                                 ...inputProps
                                                                             }) => (
                                                                   <Hint>
                                                                       <Form.Control
                                                                           {...inputProps}
                                                                           ref={(node) => {
                                                                               if (node) {
                                                                                   node.value = opdQuickFilter;
                                                                               }
                                                                               inputRef(node);
                                                                               referenceElementRef(node);
                                                                           }}
                                                                       />
                                                                   </Hint>
                                                               )}
                                                               clearButton
                                                               labelKey={option => this.getOpdPlanLabel(option.planSa, option.planName)}
                                                               onInputChange={(text, e) => {
                                                                   this.onOpdFilterChanged(text)
                                                               }}
                                                               onChange={this.selectOpdPlanTypeahead}
                                                               options={opdPlans}
                                                               value={opdQuickFilter}
                                                               placeholder="OPD Plan"
                                                               selected={Object.keys(opdPlan).length > 0 ? [opdPlan] : []}
                                                    />
                                                    <InputGroup.Append>
                                                        <Button variant="outline-light"
                                                                onClick={this.showOPDPlanSelector}
                                                                style={{borderColor: "#ced4da"}}>
                                                            <img src={`../assets/images/search.svg`}
                                                                 className="label-img"
                                                                 alt="" style={{
                                                                paddingLeft: "0rem",
                                                                borderRadius: "0rem",
                                                                width: "1rem",
                                                                height: "1rem"
                                                            }}/>
                                                        </Button>
                                                    </InputGroup.Append>
                                                </InputGroup>
                                                {(formSubmitted && missingOpdPlan) ?
                                                    <div className={`invalid-feedback`}
                                                         style={{display: "block"}}>{FIELD_REQUIRED}</div> : ``}
                                            </Form.Group>}
                                            <Form.Group as={Col} sm={3} controlId="opdTrainTypes">
                                                <Form.Text className="ccp-label">OPD train type(s) *</Form.Text>
                                                <Modal.Dialog style={{margin: "0rem", maxWidth: "100%"}}>
                                                    <Modal.Body
                                                        style={{
                                                            overflow: "scroll",
                                                            maxHeight: "110px",
                                                            padding: "0rem 1rem"
                                                        }}>
                                                        {opdTrnTypes && opdTrnTypes.map(item => {
                                                            return <Form.Check
                                                                type="checkbox"
                                                                checked={item.selected}
                                                                onChange={(e) => this.handleOpdTrainTypeSelection(item.key, e.target.checked)}
                                                                id={`${item.key}`}
                                                                key={`${item.key}`}
                                                                label={item.desc}
                                                            ></Form.Check>
                                                        })
                                                        }
                                                    </Modal.Body>
                                                </Modal.Dialog>
                                                {missingTrnTypes ? <div className={`invalid-feedback`}
                                                                        style={{display: "block"}}>{OPD_TRAIN_TYPES_NOT_SELECTED}</div> : ``}
                                            </Form.Group>
                                            {isStudyAlgorithmHybrid &&
                                            <Form.Group as={Col} sm={3} controlId="historyTrainTypes">
                                                <Form.Text className="ccp-label">History train type(s) *</Form.Text>
                                                <Modal.Dialog style={{margin: "0rem", maxWidth: "100%"}}>
                                                    <Modal.Body
                                                        style={{
                                                            overflow: "scroll",
                                                            maxHeight: "110px",
                                                            padding: "0rem 1rem"
                                                        }}>
                                                        {historyTrnTypes && historyTrnTypes.map(item => {
                                                            return <Form.Check
                                                                type="checkbox"
                                                                checked={item.selected}
                                                                onChange={(e) => this.handleHistoryTrainTypeSelection(item.key, e.target.checked)}
                                                                id={`${item.key}`}
                                                                key={`${item.key}`}
                                                                label={item.desc}
                                                            ></Form.Check>
                                                        })
                                                        }
                                                    </Modal.Body>
                                                </Modal.Dialog>
                                                {missingHistoryTrnTypes ? <div className={`invalid-feedback`}
                                                                               style={{display: "block"}}>{HISTORY_TRAIN_TYPES_NOT_SELECTED}</div> : ``}
                                            </Form.Group>}
                                        </>}
                                    </Form.Row>
                                    <Form.Row>
                                        <Form.Group as={Col} sm={6} controlId="startDate">
                                            <Form.Text className="ccp-label">Start date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={startDate}
                                                          onChange={this.changeStartDate}
                                                          onBlur={this.validateStartDateChange}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={isStudyAlgorithmOPD && isEmpty(opdScenario) && isEmpty(opdPlan)}
                                            />
                                            {/*<Form.Control.Feedback*/}
                                            {/*    type="invalid">{studyStartDt?`Start date must be in range between ${geUSDateFromISODate(studyStartDt)} and ${gePreviousUSDateFromISODate(studyEndDt)}`: `${STUDY_CREATE_START_DATE_PRIOR_YESTERDAY}`}</Form.Control.Feedback>*/}
                                            {!this.state.startDateValid && <Form.Control.Feedback
                                                type="invalid">{this.state.startDateErrorMsg}</Form.Control.Feedback>
                                            }
                                        </Form.Group>
                                        <Form.Group as={Col} sm={6} controlId="endDate">
                                            <Form.Text className="ccp-label">End date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={endDate}
                                                          onChange={this.changeEndDate}
                                                          onBlur={this.validateEndDateChange}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={isStudyAlgorithmOPD && isEmpty(opdScenario) && isEmpty(opdPlan)}
                                            />
                                            {!this.state.dateRangeValid && <Form.Control.Feedback
                                                type="invalid">{this.state.endDateErrorMsg}</Form.Control.Feedback>
                                            }
                                        </Form.Group>
                                        {this.state.feedback &&
                                        <PopupAlert hide={true} message={this.state.feedback.message}
                                                    variant={this.state.feedback.type || 'warning'}
                                                    handleClose={this.handleClose}/>}
                                    </Form.Row>
                                    {isStudyAlgorithmHybrid &&
                                    <Form.Row>
                                        <Form.Group as={Col} sm={6} controlId="historyStartDate">
                                            <Form.Text className="ccp-label">History Start date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={historyStartDate}
                                                          max={getYesterdayISODate()}
                                                          onChange={this.changeHistoryStartDate}
                                                          onBlur={this.validateHistoryStartDate}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={disableHistoryStartDate}
                                            />
                                            <Form.Control.Feedback
                                                type="invalid">{historyStartDate ? `History Start date must be prior to Today` : `${FIELD_REQUIRED}`}</Form.Control.Feedback>
                                        </Form.Group>
                                        <Form.Group as={Col} sm={6} controlId="historyEndDate">
                                            <Form.Text className="ccp-label">History End date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={historyEndDate}
                                                          max={getYesterdayISODate()}
                                                          onChange={this.changeHistoryEndDate}
                                                          onBlur={this.validateHistoryEndDate}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={true}
                                            />
                                            {!this.state.historyDateRangeValid && <Form.Control.Feedback
                                                type="invalid">{this.state.historyEndDateErrorMsg}</Form.Control.Feedback>
                                            }
                                            {/*<Form.Control.Feedback*/}
                                            {/*    type="invalid">{historyEndDate ? `End date must be at least one day prior to Today` : `${FIELD_REQUIRED}`}</Form.Control.Feedback>*/}
                                        </Form.Group>
                                    </Form.Row>
                                    }
                                    {isStudyAlgorithmOPD &&
                                    <Form.Row>
                                        <Form.Group as={Col} sm={6} controlId="localYardJobStartDate">
                                            <Form.Text className="ccp-label">Local Yard Job Start date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={localYardJobStartDate}
                                                          max={getYesterdayISODate()}
                                                          onChange={this.changeLocalYardJobStartDate}
                                                          onBlur={this.validateLocalYardJobStartDate}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={isStudyAlgorithmOPD && isEmpty(opdScenario) && isEmpty(opdPlan)}
                                            />
                                            <Form.Control.Feedback
                                                type="invalid">{localYardJobStartDate ? `Local Yard Job Start date must be prior to Today` : `${FIELD_REQUIRED}`}</Form.Control.Feedback>
                                        </Form.Group>
                                        <Form.Group as={Col} sm={6} controlId="localYardJobEndDate">
                                            <Form.Text className="ccp-label">Local Yard Job End date *</Form.Text>
                                            <Form.Control className="font-size-small font-weight-600"
                                                          required
                                                          type="date"
                                                          value={localYardJobEndDate}
                                                          max={getYesterdayISODate()}
                                                          onChange={this.changeLocalYardJobEndDate}
                                                          onBlur={this.validateLocalYardJobEndDate}
                                                          style={{width: "auto", minWidth: "30%"}}
                                                          disabled={isStudyAlgorithmOPD}
                                            />
                                            {!this.state.localYardJobDateRangeValid && <Form.Control.Feedback
                                                type="invalid" style={{display:'block'}}>{this.state.localYardJobEndDateErrorMsg}</Form.Control.Feedback>
                                            }
                                            {/*<Form.Control.Feedback*/}
                                            {/*    type="invalid">{localYardJobEndDate ? `End date must be at least one day prior to Today` : `${FIELD_REQUIRED}`}</Form.Control.Feedback>*/}
                                        </Form.Group>
                                    </Form.Row>

                                    
                                    }
                                     {isStudyAlgorithmOPD &&
                                     <Form.Row style={{marginBottom: "0rem"}}>
                                            <Form.Group as={Col} sm={6} controlId="upside-report">
                                                <Form.Text className="ccp-label">Upside Report</Form.Text>
                                                <InputGroup>
                                                <Typeahead ref={this.upsideReportRef}
                                                               id="upside-report-select"
                                                               renderInput={({
                                                                                 inputRef,
                                                                                 referenceElementRef,
                                                                                 ...inputProps
                                                                             }) => (
                                                                   <Hint>
                                                                       <Form.Control
                                                                           {...inputProps}
                                                                           ref={(node) => {
                                                                               if (node) {
                                                                                   node.value = upsideReportQuickFilter;
                                                                               }
                                                                               inputRef(node);
                                                                               referenceElementRef(node);
                                                                           }}
                                                                       />
                                                                   </Hint>
                                                               )}
                                                               clearButton
                                                               labelKey={option => this.getUpsideReportLabel(option.upsideName, option.upsideDesc)}
                                                               onInputChange={(text, e) => {
                                                                   this.onUpsideFilterChanged(text)
                                                               }}
                                                               onChange={this.selectUpsideReportTypeahead}
                                                               options={upsideReports}
                                                               value={upsideReportQuickFilter}
                                                               placeholder="Upside Report"
                                                               selected={Object.keys(upsideReport).length > 0 ? [upsideReport] : []}
                                                    />
                                                    <InputGroup.Append>
                                                        <Button variant="outline-light"
                                                                onClick={this.showUpsideReportSelector}
                                                                style={{borderColor: "#ced4da"}}>
                                                            <img src={`../assets/images/search.svg`}
                                                                 className="label-img"
                                                                 alt="" style={{
                                                                paddingLeft: "0rem",
                                                                borderRadius: "0rem",
                                                                width: "1rem",
                                                                height: "1rem"
                                                            }}/>
                                                        </Button>
                                                    </InputGroup.Append>
                                                </InputGroup>
                                            </Form.Group>
                                            </Form.Row>
                                    }

                                    {(isStudyAlgorithmOPD || studyAlgorithm === "CREWPRO") &&
                                    <Form.Row style={{marginBottom: "0rem"}}>
                                        <Form.Group as={Col} sm="6" style={{marginBottom: "0rem"}}>
                                            <Form.Text className={`ccp-label`}>Apply variation</Form.Text>
                                            <Form.Row style={{
                                                alignItems: "flex-end",
                                                marginBottom: "0rem",
                                                border: "1px solid #ced4da",
                                                borderRadius: ".25rem"
                                            }}>
                                                <Form.Group as={Col} style={{margin: ".5rem 0"}}>
                                                    <Form.Check
                                                        style={{color: "black !important"}}
                                                        type={`checkbox`}
                                                        checked={transitVariation}
                                                        onChange={this.handleTransitVariationChange}
                                                        id={`applytransitvariation`}
                                                        label={`Transit time`}
                                                    />
                                                </Form.Group>
                                                <Form.Group as={Col} style={{margin: ".5rem 0"}}>
                                                    <Form.Check type={`checkbox`}
                                                                checked={departureTimeVariation}
                                                                onChange={this.handleDepartureTimeVariationChange}
                                                                id={`departuretimevariation`}
                                                                label={`Departure time`}
                                                    />
                                                </Form.Group>
                                            </Form.Row>
                                        </Form.Group>
                                    </Form.Row>
                                    }
                                    <PopupGridRowSelector show={this.state.showOPDScenarioSelector}
                                                          onHide={this.hideOPDScenarioSelector}
                                                          size="xxl"
                                                          filterLabel={`Filter OPD Scenarios...`}
                                                          quickFilterCallBack={this.onOpdFilterChanged}
                                                          quickFilter={opdQuickFilter}
                                                          title="OPD Scenario"
                                                          style={{margin: "1rem 1rem auto auto "}}
                                                          rowData={opdScenarios}
                                                          onGridReady={this.onGridReady}
                                                          columnDefs={STUDY_OPD_SCENARIO_SELECTOR_COLUMNS_DEFS}
                                                          defaultColDef={getDefaultColDef}
                                                          callBackSelection={this.selectOPDScenario}
                                    />
                                    <PopupGridRowSelector show={this.state.showOPDPlanSelector}
                                                          onHide={this.hideOPDPlanSelector}
                                                          size="xxl"
                                                          filterLabel={`Filter OPD Plans...`}
                                                          quickFilterCallBack={this.onOpdFilterChanged}
                                                          quickFilter={opdQuickFilter}
                                                          title="OPD Plan"
                                                          style={{margin: "1rem 1rem auto auto "}}
                                                          rowData={opdPlans}
                                                          onGridReady={this.onGridReady}
                                                          columnDefs={STUDY_OPD_PLAN_SELECTOR_COLUMNS_DEFS}
                                                          defaultColDef={getDefaultColDef}
                                                          callBackSelection={this.selectOPDPlan}
                                    />
                                    <PopupGridRowSelector show={this.state.showUpsideReportSelector}
                                                          onHide={this.hideUpsideReportSelector}
                                                          size="xxl"
                                                          filterLabel={`Filter Upsides...`}
                                                          quickFilterCallBack={this.onUpsideFilterChanged}
                                                          quickFilter={upsideReportQuickFilter}
                                                          title="Upside Report"
                                                          style={{margin: "1rem 1rem auto auto "}}
                                                          rowData={upsideReports}
                                                          onGridReady={this.onGridReady}
                                                          columnDefs={UPSIDE_REPORT_SELECTOR_COLUMNS_DEFS}
                                                          defaultColDef={getDefaultColDef}
                                                          callBackSelection={this.selectUpsideReport}
                                    />
                                </Modal.Body>
                                <Modal.Footer style={{justifyContent: "center", paddingBottom: "0rem"}}
                                              className="ccp-form">
                                    <Button type="submit" variant="success">
                                        {`Create `}
                                    </Button>
                                    <Button variant="warning" onClick={this.clear} disabled={!updated}>Clear</Button>
                                </Modal.Footer>
                            </Modal.Dialog>
                        </Form.Group>
                    </Form.Row>
                    {this.props.feedback && this.props.feedback.key === "ADD_STUDY_SUCCESS" &&
                    <PopupAlert hide={true} variant={this.props.feedback.variant} message={this.props.feedback.message}
                                handleClose={this.clear}/>}
                </Form>
            </>
        );
    }
}

function mapStateToProps(state) {
    const {masterPlans} = state.plan;
    const {feedback, opdScenarios, opdPlans, studyCreate,upsideReports} = state.study;
    return {
        plans: masterPlans,
        feedback,
        opdScenarios,
        opdPlans,
        studyCreate,
        upsideReports
    }
}

export const mapDispatchToProps = {
    getMasterPlans,
    addStudy,
    clearFeedback,
    changeStudyCreateOpdScenario,
    changeStudyCreateOpdPlan,
    changeStudyCreateUpsideReport,
    changeStudyCreateStartDate,
    changeStudyCreateLocalYardJobStartDate,
    changeStudyCreateHistoryStartDate,
    changeStudyCreateEndDate,
    changeStudyCreateLocalYardJobEndDate,
    changeStudyCreateHistoryEndDate,
    changeStudyCreateName,
    changeStudyCreateDesc,
    changeStudyCreateTrainSource,
    changeStudyCreateTransitVariation,
    changeStudyCreateDepartureTimeVariation,
    studyCreateReset,
    changeCreateStudyOpdTrnType,
    changeCreateStudyHistoryTrnType,
    changeStudyCreateOpdQuickFilter,
    changeStudyCreateUpsideQuickFilter
}

export default connect(mapStateToProps, mapDispatchToProps)(StudyCreate);