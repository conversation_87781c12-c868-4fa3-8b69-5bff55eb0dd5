import React from "react";
import {<PERSON><PERSON>, Col, Form, InputGroup, Row} from "react-bootstrap";
import {connect} from "react-redux";
import {changeCurrentView, getMasterStudys, refreshMasterStudies,getUpsideReports} from "./studyActions";
import {CCP_APP_REFRESH_RATE_SECONDS} from "../config/envconstants";
import {getHoursDifference} from "../util/Utils";
import {AgGridReact} from "ag-grid-react";
import {MULTI_SORT_KEY, CREW_PRO} from "../config/ccpconstants";

class StudyTree extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            components: {fileCellRenderer: getFileCellRenderer()},
            frameworkComponents : {
            },
            columnDefs: [],
            defaultColDef: {flex: 1},
            autoGroupColumnDef: {
                headerName: 'Studies',
                minWidth: 300,
                cellRendererParams: {suppressCount: true, innerRenderer: 'fileCellRenderer'},
                valueGetter: (params) => {
                    return params.node.data.hierarchyName;
                }

            },
            getRowHeight: function () {return 30},
            groupDefaultExpanded: 0,
            getDataPath: function (data) {
                return data.studyHierarchy;
            },
            reSync: null,
            startTime: Date.now(),
        };
    }
    filterStudiesText = React.createRef();

    onGridReady = params => {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        this.expandRowSelection();
    };
    expandRowSelection= () =>{
        const {studyId, scenarioId=-1, view} = this.props.currStudy  || {};
        ( studyId !== undefined && view !== undefined && this.gridApi !== undefined) && this.gridApi.forEachLeafNode((node) => {
            if ((node.data.studyId === studyId) && (node.data.view === view) && (scenarioId === -1 || scenarioId === node.data.scenarioId)) {
                node.setSelected(true)
                let x = node;
                while (x.id !== "ROOT_NODE_ID") {
                    let parent = x.parent;
                    parent.setExpanded(true)
                    x = parent;
                }
                this.gridApi.ensureNodeVisible(node);
            }
        })
    }
    onFilterTextBoxChanged = () => {
        this.gridApi.setQuickFilter(
            document.getElementById('filter-text-box').value
        );
    };
    clearFilterBox = () =>{
        this.gridApi.setQuickFilter("" );
        document.getElementById('filter-text-box').value = "";

    }

    handleHide = () =>{
        this.props.hide()
    }

    handleViewChange = (event) => {
        if (!event.node.selected) {
            return;
        }
        let currStudy= event.api.getSelectedNodes()[0] && event.api.getSelectedNodes()[0].data;
        const {currStudy:oldCurrStudy={}} = this.props;
        currStudy && this.props.changeCurrentView({currStudy, loadFilters:(oldCurrStudy.studyId!==currStudy.studyId) ||(oldCurrStudy.scenarioId!==currStudy.scenarioId) ||(oldCurrStudy.view!==currStudy.view)})

    }
    getRowNodeId = (data) => {
        return data["id"];
    }

    componentDidMount() {
        this.props.refreshMasterStudies();
        this.props.getUpsideReports()
       // let reSync = setInterval(this.refresh, CCP_APP_REFRESH_RATE_SECONDS * 1000);
       //this.setState({reSync});
       const {currStudy} = this.props;
        (!currStudy && this.filterStudiesText.current) && this.filterStudiesText.current.focus();
    }
    refresh = ()  =>{
        const now = Date.now();
        let hourDiff = getHoursDifference(this.state.startTime, now);
        let variant = this.props.feedback && this.props.feedback.variant;
        if(hourDiff===0 && 'danger' !== variant) {
            this.props.refreshMasterStudies();
        }
    }
    componentWillUnmount() {
        clearInterval(this.state.reSync);
    }
    componentDidUpdate(prevProps, prevState, snapshot) {
        if(this.props.refreshCounter>prevProps.refreshCounter) {
            this.expandRowSelection()
        }
    }
    render() {
        const {hideStudyTree} = this.props;
        return (
            <>
                <div style={{width: '100%', height: '100%'}} >
                    <div className="example-wrapper">
                        <div style={{width: '100%'}}>

                            <Row>
                                <Col style={{width:"50px", maxWidth:"65px"}}>
                                    <Button variant="outline-primary" onClick={this.handleHide}
                                            style={{borderRadius:"0rem",width:"50px", maxWidth:"50px", borderColor: "#CDD4DA" }}>
                                        <img src={hideStudyTree? `../assets/images/caret-right-square-fill.svg`: `../assets/images/caret-left-square-fill.svg`}
                                             alt=""
                                        />
                                    </Button>
                                </Col>
                                {!hideStudyTree && <Col style={{width: "50px", maxWidth: "50px", padding: "0rem"}}>
                                    <Button variant="outline-primary" onClick={this.props.getMasterStudys}
                                            style={{borderRadius: "0rem", width: "50px", maxWidth: "50px", borderColor: "#CDD4DA" }}>
                                        <img
                                            src={`../assets/images/arrow-clockwise.svg`}
                                            alt=""
                                        />
                                    </Button>
                                </Col>
                                }
                                {!hideStudyTree&& <Col style={{paddingLeft: "0rem"}}>
                                    <InputGroup>
                                        <Form.Control
                                            style={{borderRadius:"0rem"}}
                                            type="text"
                                            id="filter-text-box"
                                            ref={this.filterStudiesText}
                                            placeholder="Filter studies..."
                                            onInput={() => this.onFilterTextBoxChanged()}/>
                                        <InputGroup.Append>
                                            <Button variant="secondary" onClick={this.clearFilterBox} style={{borderRadius:"0rem"}}>
                                                <img src={`../assets/images/backspace.svg`}
                                                     className="label-img"
                                                     alt="" style={{paddingLeft: "0rem", borderRadius:"0rem", width:"1rem" , height:"1rem"}}/>
                                            </Button>
                                        </InputGroup.Append>
                                    </InputGroup>
                                </Col>
                                }

                            </Row>
                        </div>
                        {!hideStudyTree &&
                        <div className="ag-theme-alpine" style={{width: "100%", height: '100%'}}>
                            <AgGridReact
                                rowSelection='single'
                                multiSortKey={MULTI_SORT_KEY}
                                //  modules={this.state.modules}
                                defaultColDef={this.state.defaultColDef}
                                columnDefs={this.state.columnDefs}
                                autoGroupColumnDef={this.state.autoGroupColumnDef}
                                getDataPath={this.state.getDataPath}
                                treeData={true}
                                animateRows={true}
                                onRowSelected={(e) => {
                                    this.handleViewChange(e)
                                }}
                                groupDefaultExpanded={this.state.groupDefaultExpanded}
                                rowData={this.props.studies}
                                onGridReady={this.onGridReady}
                                components={this.state.components}
                                getRowHeight={this.state.getRowHeight}
                                headerHeight='0'
                                frameworkComponents={this.state.frameworkComponents}
                                suppressContextMenu={true}
                            >
                            </AgGridReact>
                        </div>
                        }
                    </div>
                </div>
            </>
        );
    }
}

function getFileCellRenderer() {
    function FileCellRenderer() {}
    FileCellRenderer.prototype.init = function(params) {
        let tempDiv = document.createElement('div');
        const value = params.value;
        const {data} = params ||{}
        const icon = getFileIcon(data);
        tempDiv.innerHTML = icon
            ? '<span><i class="' +
            icon +
            '"></i>' +
            '<span class="filename"></span>' +
            value +
            '</span>'
            : value;
        this.eGui = tempDiv.firstChild;
    };
    FileCellRenderer.prototype.getGui = function() {
        return this.eGui;
    };
    return FileCellRenderer;
}

function getFileIcon(data) {
    const {view}  = data ||{}
    switch(view) {
        case`newstudy`: {
            return 'icon create-study';
        }
        case`crewprostudies`: {
            return 'icon-spl folder-special';
        }
        case`studysummary`: {
            const {studyType} = data;
            return studyType === CREW_PRO? 'icon-spl folder' :'icon-spl folder-shared' ;
        }
        case`copystudy`: {
            return 'icon copy-study';
        }
        case`diagnosticreport`:
        case`disruptioneditor`:
        case`trainviewer`:
        case`scenariosettings`:
        case`boardsummary`:
        case`trainbalancereport`:
        case`deadheadingreport`:
        case`traindelayreport`:
        case`simulationtrainreport`:
        case`trainstatisticssummarybypoolreport`:
        case`hiregroupreport`:
        case`hiregroupheadcountreport`:
        case`turnutilizationreport`:
        {
            return 'icon report';
        }
        case`scenariosummary`: {
            return 'icon-spl folder';
        }
        default : {
        }
    }
}

function mapStateToProps (state) {
    const {studies, currStudyTreeIndex, feedback, refreshCounter, currStudy, hideStudyTree} = state.study || {}
    return {
        studies,
        currStudyTreeIndex,
        feedback,
        refreshCounter,
        currStudy, hideStudyTree
    }
}

export const mapDispatchToProps = {
    changeCurrentView, getMasterStudys, refreshMasterStudies,getUpsideReports
}
export default connect(mapStateToProps,mapDispatchToProps) (StudyTree);
export {StudyTree}
