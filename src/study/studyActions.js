import * as StudyActions from "./studyActionTypes"
import {busy, feedbackApp, free, notifyAppRequest} from "../app/appActions";
import {
    addStudyAPI,
    copyStudyAPI,
    deleteScenarioAPI,
    deleteStudyAPI,
    deleteTrainViewerTrainsAPI,
    getBoardSummaryAPI,
    getBoardSummaryDetailsAPI,
    getBoardSummaryExportDetailsAPI,
    getBoardSummaryFiltersAPI,
    getCrewProfilePlansAPI,
    getCrewProfileSimpleFiltersAPI,
    getDeadheadAPI,
    getDeadheadFiltersAPI,
    getDiagnosticsReportAPI,
    getDisruptionEditorReportAPI,
    getMasterStudiesAPI,
    getOpdScenariosAPI,
    getOpdPlansAPI,
    getUpsideReportsAPI,
    getOpdTrnTypesAPI,
    getScenarioConfigurationAPI,
    getScenarioJobSummaryAPI,
    getSimulationTrainFiltersAPI,
    getSimulationTrainReportAPI,
    getTDRDataAPI,
    getTrainBalanceInputAPI,
    getTrainBalanceSimulationAPI,
    getTrainDelayFiltersAPI,
    getTrainStatisticsSummaryAPI,
    getTrainViewerAPI,
    getTrainViewerDetailsAPI,
    getTrainViewerExportDetailsAPI,
    getTrainViewerFiltersAPI,
    getTurnUtilizationAPI,
    getTurnUtilizationFiltersAPI,
    updateScenarioAPI,
    updateStudyAPI,
    updateTrainViewerTrainsAPI,
    getHireGroupDataAPI,
    getHireGroupHeadCountAPI
} from "../config/API";
import {
    BOARD_SUMMARY_EXPPORT_DETAILS_COLUMN_DEFS,
    DETAIL,
    SIMULATION_TRAIN_DETAIL_COLUMN_DEFS,
    TRAIN_VIEWER_COLUMN_DEFS,
    TRAIN_VIEWER_DETAIL_EXPAND_COLUMN_DEFS,
    HIRE_GROUP_EXPORT_DETAIL_COLUMN_DEFS,
    HIRE_GROUP_EXPORT_COLUMN_DEFS,
    HIRE_GROUP_EXPORT_DETAIL_COLUMN_DEFS_AVG,
    HIRE_GROUP_EXPORT_COLUMN_DEFS_AVG,
    HIRE_GROUP_HEADCOUNT_EXPORT_DETAIL_COLUMN_DEFS,
    HIRE_GROUP_HEADCOUNT_EXPORT_COLUMN_DEFS

} from "../config/ccpconstants";
import {getDateObjForExcel, getETTimeStampObjForExcel, getExcelFileName, getExcelSheetName} from "../util/Utils";
import {excelExportDetails,excelExportDetailsWithCallback} from "../util/ExcelExportUtils";

export const getStudiesSuccess = (data) =>{
    return {
        type: StudyActions.GET_STUDIES_SUCCESS,
        payload:data
    }
}

export const getMasterStudys = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getMasterStudiesAPI()
            .then(response => response.data)
            .then(data => {
                const {items=[]} =data;
                dispatch(getMasterStudysSuccess(items))
               dispatch(getStudiesSuccess())
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S002`, error))
            }).then(()=> dispatch(free()))
    }
}

export const  refreshMasterStudies = () => {
    return (dispatch) => {
        getMasterStudiesAPI().then(response => response.data)
            .then(data => {
                const {items=[]} =data;
                dispatch(getMasterStudysSuccess(items))
                dispatch(getStudiesSuccess())
            })
            .catch(error => {
                error &&  dispatch(refreshStudiesFailure(error.message));
                dispatch(notifyAppRequest(`CCP-S003`, error))
            })
    }
}
export const refreshStudiesFailure = (data) => {
    return {
        type: StudyActions.REFRESH_STUDIES_FAILURE,
        payload: data
    }
}
export const getMasterStudysRequest = () => {
    return {
        type: StudyActions.GET_MASTER_STUDIES_REQUEST,
        payload: true
    }
}

export const getMasterStudysSuccess = (data) => {
    return {
        type: StudyActions.GET_MASTER_STUDIES_SUCCESS,
        payload: data
    }
}

export const addStudy = (req) => {
    return (dispatch) => {
        dispatch(busy())
        addStudyAPI(req)
            .then(response => {
                dispatch(addStudySuccess({}))
                dispatch(refreshMasterStudies())
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S004`, error))
            }).then(()=>dispatch(free()))
    }
}

export const addStudySuccess = (data) => {
    return {
        type: StudyActions.ADD_STUDY_SUCCESS,
        payload: data
    }
}
export const addStudyFail = (error) => {
    return {
        type: StudyActions.ADD_STUDY_FAIL,
        payload: error
    }
}

export const changeStudyCreateName = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_NAME,
        payload:value
    }
}
export const changeStudyCreateDesc = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_DESC,
        payload:value
    }
}
export const changeStudyCreateOpdScenario = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_OPD_SCENARIO,
        payload:value
    }
}
export const changeStudyCreateOpdPlan = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_OPD_PLAN,
        payload:value
    }
}
export const changeStudyCreateUpsideReport = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_UPSIDE_REPORT,
        payload:value
    }
}
export const changeStudyCreateOpdQuickFilter = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_OPD_QUICK_FILTER,
        payload:value
    }
}
export const changeStudyCreateUpsideQuickFilter = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_UPSIDE_QUICK_FILTER,
        payload:value
    }
}
export const changeCreateStudyOpdTrnType = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_OPD_TRAIN_TYPE,
        payload:value
    }
}
export const changeCreateStudyHistoryTrnType = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_HISTORY_TRAIN_TYPE,
        payload:value
    }
}
export const changeStudyCreateStartDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_START_DATE,
        payload:value
    }
}
export const changeStudyCreateLocalYardJobStartDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_LOCAL_YARD_JOB_START_DATE,
        payload:value
    }
}
export const changeStudyCreateHistoryStartDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_HISTORY_START_DATE,
        payload:value
    }
}
export const changeStudyCreateEndDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_END_DATE,
        payload:value
    }
}
export const changeStudyCreateLocalYardJobEndDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_LOCAL_YARD_JOB_END_DATE,
        payload:value
    }
}
export const changeStudyCreateHistoryEndDate = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_HISTORY_END_DATE,
        payload:value
    }
}
export const changeStudyCreateTrainSource = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_TRAIN_SOURCE,
        payload:value
    }
}
export const changeStudyCreateTransitVariation = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_TRANSIT_VARIATION,
        payload:value
    }
}
export const changeStudyCreateDepartureTimeVariation = (value) => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_DEPARTURE_TIME_VARIATION,
        payload:value
    }
}
export const studyCreateReset = () => {
    return {
        type: StudyActions.CHANGE_STUDY_CREATE_RESET}
}


export const updateStudy = (study) => {
    return (dispatch) => {
        dispatch(busy())
        updateStudyAPI(study)
            .then((response) => {
                dispatch(updateStudySuccess())
                dispatch(refreshMasterStudies())
            }).catch((error) => {
            //error &&  dispatch(updateStudyFail(error.message))
            dispatch(notifyAppRequest(`CCP-S005`, error))
        })
            .then(()=>{dispatch(free())}
            );
    }
}


export const updateStudySuccess = (data) => {
    return {
        type: StudyActions.UPDATE_STUDY_SUCCESS,
        payload: data
    }
}
export const updateStudyFail = (error) => {
    return {
        type: StudyActions.UPDATE_STUDY_FAIL,
        payload: error
    }
}

export const deleteStudy = (studyId) => {
    return (dispatch) => {
        dispatch(busy())
        deleteStudyAPI(studyId)
            .then((response) => {
                dispatch(deleteStudySuccess())
            }).catch((error) => {
           // dispatch(deleteStudyFail(error.message))
            dispatch(notifyAppRequest(`CCP-S006`, error))

        })
            .then(()=> {
                dispatch(refreshMasterStudies());
                dispatch(free());
            });
    }
}


export const deleteStudySuccess = (data) => {
    return {
        type: StudyActions.DELETE_STUDY_SUCCESS,
        payload: data
    }
}

export const changeCurrentViewData = (data) => {
    return {
        type: StudyActions.CHANGE_CURRENT_VIEW,
        payload:data

    }

}
export const toggleStudyTree = (data) => {
    return {
        type: StudyActions.TOGGLE_STUDY_TREE,
        payload:data

    }
}


export const changeCurrentView = (data) => {
    return (dispatch) => {
        dispatch(changeCurrentViewData(data));
        const {studyId, view, scenarioId} = data.currStudy || {}
        const {loadFilters} = data
        if (loadFilters){
            switch (view) {
            case 'newstudy':
                dispatch(updateOpdTrnTypes());
                break;
            case 'trainviewer':
               dispatch(getTrainViewerFilters({studyId}));
               dispatch(getCrewProfileSimpleFilters());
                break;
            case 'boardsummary':
                dispatch(getBoardSummaryFilters({scenarioId}));
                break;
            case 'deadheadingreport':
                dispatch(getDeadheadFilters({scenarioId}));
                break;
            case 'traindelayreport':
                dispatch(getTrainDelayFilters({scenarioId}));
                break;
            case 'simulationtrainreport':
                dispatch(getSimulationTrainFilters({scenarioId}));
                break;
            case 'turnutilizationreport':
                dispatch(getTurnUtilizationFilters({scenarioId}));
                break;
            case "scenariosettings" : {
                dispatch(getScenarioConfiguration({scenarioId}));
                break;
            }
            case "scenariosummary" : {
                dispatch(getScenarioJobSummary({scenarioId}));
                break;
            }
            case "hiregroupreport" : {
                dispatch(getHireGroupData({scenarioId}));
                break;
            }
            case "hiregroupheadcountreport" : {
                dispatch(getHireGroupHeadCountData({scenarioId}));
                break;
            }
            default:
                break;
            }
        }

    }
}


export const copyStudy = (study) => {
    return (dispatch) => {
        dispatch(busy())
        copyStudyAPI(study)
            .then((response) => {
                dispatch(copyStudySuccess(study))
            }).catch((error) => {
            error && dispatch(copyStudyFail(error.message))
            dispatch(notifyAppRequest(`CCP-S007`, error))
        })
            .then(()=>{dispatch(free())}
            );
    }
}

export const copyStudySuccess = (data) => {
    return {
        type: StudyActions.COPY_STUDY_SUCCESS,
        payload: data
    }
}
export const copyStudyFail = (error) => {
    return {
        type: StudyActions.COPY_STUDY_FAIL,
        payload: error
    }
}

export const getOpdScenarios = () => {
    return (dispatch) =>{
        dispatch(busy())
        getOpdScenariosAPI()
            .then(response => response.data)
            .then(data => {
                dispatch(getOpdScenariosSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S008`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getOpdScenariosSuccess =(data) =>{
    return {type: StudyActions.GET_OPD_SCENARIOS_SUCCESS,
        payload:data}
}

//upside report data from backend
export const getUpsideReports = () => {
    return (dispatch) =>{
        dispatch(busy())
        getUpsideReportsAPI()
            .then(response => response.data)
            .then(data => {
                dispatch(getUpsideReportsSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S008`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getUpsideReportsSuccess =(data) =>{
    return {type: StudyActions.GET_UPSIDE_REPORTS_SUCCESS,
        payload:data}
}
export const getOpdPlans = () => {
    return (dispatch) =>{
        dispatch(busy())
        getOpdPlansAPI()
            .then(response => response.data)
            .then(data => {
                dispatch(getOpdPlansSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S008`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getOpdPlansSuccess =(data) =>{
    return {type: StudyActions.GET_OPD_PLANS_SUCCESS,
        payload:data}
}

export const getOpdTrnTypes = () => {
    return (dispatch) =>{
        dispatch(busy())
        getOpdTrnTypesAPI()
            .then(response => response.data)
            .then(data => {
                dispatch(getOpdTrnTypesSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S028`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getOpdTrnTypesSuccess =(data) =>{
    return {type: StudyActions.GET_OPD_TRN_TYPES_SUCCESS,
        payload:data}
}

export const updateOpdTrnTypes =() =>{
    return {type: StudyActions.UPDATE_OPD_TRN_TYPES,
        }
}

export const getDiagnosticReport = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getDiagnosticsReportAPI(req)
            .then(response => response.data)
            .then(data => {
               dispatch(getDiagnosticReportSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S009`, error))
            }).then(()=>dispatch(free()))
    }
}


export const getDiagnosticReportSuccess =(data) =>{
    return {type: StudyActions.GET_DIAGNOSTIC_REPORT_SUCCESS,
        payload:data}
}

export const changeDiagnosticReportType = (reportType) => {
    return {
        type: StudyActions.CHANGE_DIAGNOSTIC_REPORT_TYPE,
        payload:reportType
    }
}

export const getTrainViewerFail =(error) =>{
    return {type: StudyActions.GET_TRAIN_VIEWER_FAIL,
        payload:error}
}


export const getTrainViewerDetails = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getTrainViewerDetailsAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTrainViewerDetailsSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S010`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getTrainViewerDetailsSuccess =(data) =>{
    return {type: StudyActions.GET_TRAIN_VIEWER_DETAILS_SUCCESS,
        payload:data}
}

export const updateTrainViewerTrains = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        updateTrainViewerTrainsAPI(req)
            .then(response => {
                const {status} = response;
                if(status ===200) {
                    dispatch(feedbackApp({variant:`success`, message:`Train(s) updated successfully`}))
                    dispatch(changeTrainViewerReportExpired(true));
                }
                })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S032`, error))
            }).then(()=>dispatch(free()))
    }
}

export const updateTrainViewerTrainsSuccess =(data) =>{
    return {type: StudyActions.UPDATE_TRAIN_VIEWER_TRAINS_SUCCESS,
        payload:data}
}

export const deleteTrainViewerTrains = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        deleteTrainViewerTrainsAPI(req)
            .then(response => {
                const {status} = response;
                if(status ===200) {
                    dispatch(feedbackApp({variant:`warning`, message:`Train(s) deleted successfully`}))
                    dispatch(changeTrainViewerReportExpired(true))
                }
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S033`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getCrewProfilePlans = (req) => {
    return (dispatch) =>{
        const {profileId, crewOrgnOs} = req;
        if(profileId  && crewOrgnOs) {
            dispatch(busy())
            getCrewProfilePlansAPI(req)
                .then(response => response.data)
                .then(data => {
                    dispatch(getCrewProfilePlansSuccess(data.items));
                })
                .catch(error => {
                    dispatch(notifyAppRequest(`CCP-S032`, error))
                }).then(()=>dispatch(free()))
        } else {
            dispatch(getCrewProfilePlansSuccess([]));
        }

    }
}

export const getCrewProfilePlansSuccess =(data) =>{
    return {type: StudyActions.GET_CREW_PROFILE_PLANS_SUCCESS,
        payload:data}
}


export const changeTVTrainCrewDistrict = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_TRAIN_CREW_DISTRICT,
        payload:data
    }
}
export const changeTVTrainType = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_TRAIN_TYPE,
        payload:data
    }
}
export const changeTVTrain = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_TRAIN,
        payload:data
    }
}
export const changeTVCrewOriginOs = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_CREW_ORIGIN_OS,
        payload:data
    }
}
export const changeTVBoardType = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_BOARD_TYPE,
        payload:data
    }
}
export const changeTrainViewerReportExpired = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_REPORT_EXPIRED,
        payload:data
    }
}
export const clearTrainViewerReport = (data) => {
    return {
        type: StudyActions.CLEAR_TRAIN_VIEWER_REPORT,
        payload:data
    }
}
export const clearTrainViewerDetailData = (data) => {
    return {
        type: StudyActions.CLEAR_TRAIN_VIEWER_DETAIL_DATA,
        payload:data
    }
}
export const changeTVDetailDataRow = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_VIEWER_DETAIL_DATA_ROW,
        payload:data
    }
}
export const exportTrainViewer = (req) => {
    return (dispatch) => {
        dispatch(busy())
        const { mode} = req||{}
      const api =  mode ===DETAIL ? getTrainViewerExportDetailsAPI(req) : getTrainViewerAPI(req)
            api.then(response => response.data)
            .then(data => {
                const {studyId, studyName, trnCrewDistr, trnType} = req;
                const modeText = mode === DETAIL? `Details`:``
                const fileName = getExcelFileName(`Train viewer ${modeText} ${studyId} ${studyName}`);
                const sheetName = getExcelSheetName(`Train viewer ${trnCrewDistr} ${trnType}`);
                const headers = (mode === DETAIL? TRAIN_VIEWER_DETAIL_EXPAND_COLUMN_DEFS:TRAIN_VIEWER_COLUMN_DEFS);
                const {items=[]} = data;
                const rows= mode === DETAIL?
                    items.map(item=>{
                        const {fromTs, toTs, trnOrgnDt, effDt, expDt } = item
                        return {...item,
                            fromTs: getETTimeStampObjForExcel(fromTs),
                            toTs: getETTimeStampObjForExcel(toTs),
                            trnOrgnDt: getDateObjForExcel(trnOrgnDt),
                            effDt: getDateObjForExcel(effDt),
                            expDt: getDateObjForExcel(expDt)
                        }
                    })
                    :items;

                const sheets=[
                    {name:sheetName, headers, rows},
                ]
                excelExportDetails({fileName, sheets});
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S030`, error))
            }).then(()=> dispatch(free()))
    }
}


export const exportTrainViewerDetails = (req) => {
    return (dispatch) => {
        dispatch(busy())
        dispatch(trainViewerDownloadStatus(true))
        const { mode } = req || {}
        req = { ...req, startRow: 1, numRows: 15000 };

        let trainViewerData = [];

        const fetchPaginationData = (req) => {
            const api = getTrainViewerExportDetailsAPI(req)
            api.then(response => response.data).then(data => {
                trainViewerData = [...trainViewerData, ...data.items];
                if (data.items.length >= 15000) {
                    const { startRow } = req;
                    req = { ...req, startRow: startRow +15000 }
                    fetchPaginationData(req)
                } else {
                    const { studyId, studyName, trnCrewDistr, trnType } = req;
                    const modeText = mode === DETAIL ? `Details` : ``
                    const fileName = getExcelFileName(`Train viewer ${modeText} ${studyId} ${studyName}`);
                    const sheetName = getExcelSheetName(`Train viewer ${trnCrewDistr} ${trnType}`);
                    const headers = (mode === DETAIL ? TRAIN_VIEWER_DETAIL_EXPAND_COLUMN_DEFS : TRAIN_VIEWER_COLUMN_DEFS);
                    const items = trainViewerData;
                    const rows = mode === DETAIL ?
                        items.map(item => {
                            const { fromTs, toTs, trnOrgnDt, effDt, expDt } = item
                            return {
                                ...item,
                                fromTs: getETTimeStampObjForExcel(fromTs),
                                toTs: getETTimeStampObjForExcel(toTs),
                                trnOrgnDt: getDateObjForExcel(trnOrgnDt),
                                effDt: getDateObjForExcel(effDt),
                                expDt: getDateObjForExcel(expDt)
                            }
                        })
                        : items;

                    const sheets = [
                        { name: sheetName, headers, rows },
                    ]
                    excelExportDetailsWithCallback({ fileName, sheets },()=>{
                        dispatch(trainViewerDownloadStatus(false));
                        dispatch(free());
                    });


                }
            }).catch(error => {
                dispatch(notifyAppRequest(`CCP-S030`, error))
            }).then(() => {
               
               
            
            })

        }
        fetchPaginationData(req);



    }
}




export const trainViewerDownloadStatus = (data) => {
    return {
        type: StudyActions.TRAIN_VIEWER_DOWNLOAD_STATUS,
        payload: data
    }
}

//Study scenario
export const updateScenario = (scenario) => {
    return (dispatch) => {
        dispatch(busy())
        updateScenarioAPI(scenario)
            .then((response) => {
                dispatch(updateScenarioSuccess())
                dispatch(refreshMasterStudies())
            }).catch((error) => {
            dispatch(notifyAppRequest(`CCP-S011`, error))
        })
            .then(()=>{
                dispatch(free())
            });
    }
}


export const updateScenarioSuccess = (data) => {
    return {
        type: StudyActions.UPDATE_SCENARIO_SUCCESS,
        payload: data
    }
}
export const updateScenarioFail = (error) => {
    return {
        type: StudyActions.UPDATE_SCENARIO_FAIL,
        payload: error
    }
}

export const deleteScenario = (scenarioId) => {
    return (dispatch) => {
        dispatch(busy())
        deleteScenarioAPI(scenarioId)
            .then((response) => {
                dispatch(deleteScenarioSuccess())
                dispatch(refreshMasterStudies())
            }).catch((error) => {
           // error && dispatch(deleteScenarioFail(error.message))
            dispatch(notifyAppRequest(`CCP-S012`, error))
        })
            .then(()=>{dispatch(free())}
            );
    }
}

export const deleteScenarioSuccess = (data) => {
    return {
    type: StudyActions.DELETE_SCENARIO_SUCCESS,
        payload: data
    }
}
export const deleteScenarioFail = (error) => {
    return {
        type: StudyActions.DELETE_SCENARIO_FAIL,
        payload: error
    }
}

// Train Balance Report
export const getTrainBalanceReport = (req) => {
    const {balancePeriod} = req || {}
    const api = ("INPUTTRAIN" === balancePeriod) ? getTrainBalanceInputAPI(req) : getTrainBalanceSimulationAPI(req);
    return (dispatch) => {
        dispatch(busy())
        api.then(response => response.data)
            .then(data => {
                dispatch(getTrainBalanceReportSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest('CCP-S013', error))
            }).then(() => dispatch(free()))
    }
}

    export const getTrainBalanceReportSuccess =(data) =>{
        return {type: StudyActions.GET_TRAIN_BALANCE_REPORT_SUCCESS,
            payload:data}
    }

    export const getTrainBalanceReportFail =(error) =>{
        return {type: StudyActions.GET_TRAIN_BALANCE_REPORT_FAIL,
            payload:error}
    }

export const changeTBRTrainStartsType = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_BALANCE_REPORT_TRAIN_STARTS_TYPE,
        payload:data
    }
}
export const changeTBRBalancePeriod = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_BALANCE_REPORT_BALANCE_PERIOD,
        payload:data
    }
}
export const changeTBRBoardType = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_BALANCE_REPORT_BOARD_TYPE,
        payload:data
    }
}
export const changeTBRCurrRowId = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_BALANCE_REPORT_CURR_ROW_ID,
        payload:data
    }
}
//Deadheading report
export const changeDeadheadTrainStartsType = (value) => {
    return {
        type: StudyActions.CHANGE_DEADHEAD_TRAIN_STARTS_TYPE,
        payload: value
    }
}
export const changeDeadheadDistrict = (value) => {
    return {
        type: StudyActions.CHANGE_DEADHEAD_DISTRICT,
        payload: value
    }
}
export const changeDeadheadSubDistrict = (value) => {
    return {
        type: StudyActions.CHANGE_DEADHEAD_SUB_DISTRICT,
        payload: value
    }
}
export const changeDeadheadBoardName = (value) => {
    return {
        type: StudyActions.CHANGE_DEADHEAD_BOARD_NAME,
        payload: value
    }
}
export const changeDeadheadCraft = (value) => {
    return {
        type: StudyActions.CHANGE_DEADHEAD_CRAFT,
        payload: value
    }
}

export const getDeadheadReport = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getDeadheadAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getDeadheadReportSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S014`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getDeadheadReportSuccess =(data) =>{
    return {type: StudyActions.GET_DEADHEAD_REPORT_SUCCESS,
        payload:data}
}

//Simulation Train Report
export const getSimulationTrainReport = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getSimulationTrainReportAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getSimulationTrainReportSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest(`CCP-S015`, error))
            }).then(()=>dispatch(free()))
    }
}


export const getSimulationTrainReportSuccess =(data) =>{
    return {type: StudyActions.GET_SIMULATION_TRAIN_REPORT_SUCCESS,
        payload:data}
}

export const getSimulationTrainReportFail =(error) =>{
    return {type: StudyActions.GET_SIMULATION_TRAIN_REPORT_FAIL,
        payload:error}
}

export const changeSTRTrainType = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_TRAIN_TYPE,
        payload:value
    }
}
export const clearSimulationTrainReport= (value) => {
    return {
        type: StudyActions.CLEAR_SIMULATION_TRAIN_REPORT,
        payload:value
    }
}
export const changeSimulationTrainDetailData = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_DETAIL_DATA,
        payload:value
    }
}
export const changeSTRLineSegment = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_LINE_SEGMENT,
        payload:value
    }
}
export const changeSTRTrainId = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_TRAIN_ID,
        payload:value
    }
}
export const changeSTRDistrict = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_DISTRICT,
        payload:value
    }
}
export const changeSTRDelayType = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_DELAY_TYPE,
        payload:value
    }
}
export const changeSTRFromOs = (value) => {
    return {
        type: StudyActions.CHANGE_SIMULATION_TRAIN_REPORT_FROM_OS,
        payload:value
    }
}

export const exportSimulationTrainReport = (req) => {
    return (dispatch) => {
        dispatch(busy())
        const myPromise = new Promise((resolve, reject) => {
            setTimeout(() => {
                try {
                    const {scenarioId, scenarioName, distr, delayType, trnType, data} = req;
                    let fileName = getExcelFileName(`Simulation Train Details ${scenarioId} ${scenarioName}`);
                    const sheetName = getExcelSheetName(`Simulation Train ${delayType ? delayType : ''} ${distr ? distr : ''} ${trnType ? trnType : ''} `);
                    const headers = SIMULATION_TRAIN_DETAIL_COLUMN_DEFS
                    const rows = data.map(item=>{
                        const {fromTs, toTs, trnOrgnDt, coOnDutyStartTime, enOnDutyStartTime} = item;
                        return {...item,
                            fromTs: getETTimeStampObjForExcel(fromTs),
                            toTs: getETTimeStampObjForExcel(toTs),
                            trnOrgnDt: getDateObjForExcel(trnOrgnDt),
                            coOnDutyStartTime: getETTimeStampObjForExcel(coOnDutyStartTime),
                            enOnDutyStartTime: getETTimeStampObjForExcel(enOnDutyStartTime)
                        }
                    })
                    const sheets=[
                        {name:sheetName, headers, rows},
                    ]
                    excelExportDetails({fileName, sheets});
                    resolve('success');
                } catch (e) {
                    reject(e);
                }
            }, 100);
        });
        myPromise
            .then(() => dispatch(free()))
            .catch((error) => error && dispatch(notifyAppRequest(`CCP-S029`, error)))
    }
}


//Train Statistics Summary By Pool Report
export const getTrainStatisticsSummaryReport = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getTrainStatisticsSummaryAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTrainStatisticsSummaryReportSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest(`CCP-S016`, error))
            }).then(()=>dispatch(free()))
    }
}

export const getTrainStatisticsSummaryReportSuccess =(data) =>{
    return {type: StudyActions.GET_TRAIN_STATISTICS_SUMMARY_SUCCESS,
        payload:data}
}

export const changeTSSGroupBy = (groupBy) => {
    return {
        type: StudyActions.CHANGE_TRAIN_STATISTICS_SUMMARY_GROUP_BY,
        payload:groupBy
    }
}
export const changeTSSTrainStartsType = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_STATISTICS_SUMMARY_TRAIN_STARTS_TYPE,
        payload:data
    }
}
export const changeTSSCraft = (data) => {
    return {
        type: StudyActions.CHANGE_TRAIN_STATISTICS_SUMMARY_CRAFT,
        payload:data
    }
}

//Disruption Editor
export const getDisruptionEditorReport = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getDisruptionEditorReportAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getDisruptionEditorReportSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(getDisruptionEditorReportFail(error.message))
            }).then(()=>dispatch(free()))
    }
}

export const getDisruptionEditorReportRequest =(data) => {
    return {
        type: StudyActions.GET_DISRUPTION_EDITOR_REPORT_REQUEST,
        payload:true
    }
}
export const getDisruptionEditorReportSuccess =(data) =>{
    return {type: StudyActions.GET_DISRUPTION_EDITOR_REPORT_SUCCESS,
        payload:data}
}

export const getDisruptionEditorReportFail =(error) =>{
    return {type: StudyActions.GET_DISRUPTION_EDITOR_REPORT_FAIL,
        payload:error}
}



//Turn Utilization Report
export const getTurnUtilizationReport = (req) => {
    return (dispatch) =>{
        dispatch(busy());
        getTurnUtilizationAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTURSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(getTURFail(error.message))
            }).then(()=>dispatch(free()))
    }
}

export const getTURSuccess =(data) =>{
    return {type: StudyActions.GET_TURN_UTILIZATION_REPORT_SUCCESS,
        payload:data}
}

export const getTURFail =(error) =>{
    return {type: StudyActions.GET_TURN_UTILIZATION_REPORT_FAIL,
        payload:error}
}
export const changeTURTrainStartsType = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_TRAIN_STARTS_TYPE,
        payload: data
    }
}
export const changeTURBoardType = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_BOARD_TYPE,
        payload: data
    }
}
export const changeTURBoardName = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_BOARD_NAME,
        payload: data
    }
}
export const changeTURDistrict = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_DISTRICT,
        payload: data
    }
}
export const changeTURSubDistrict = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_SUB_DISTRICT,
        payload: data
    }
}
export const changeTURCraft = (data) => {
    return {
        type: StudyActions.CHANGE_TURN_UTILIZATION_REPORT_CRAFT,
        payload: data
    }
}
export const clearTURFilters = () => {
    return {
        type: StudyActions.CLEAR_TURN_UTILIZATION_REPORT,
    }
}

//Board Summary
export const getBoardSummary = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getBoardSummaryAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getBoardSummarySuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S017",error))
            }).then(()=>dispatch(free()))
    }
}

export const getBoardSummarySuccess =(data) =>{
    return {type: StudyActions.GET_BOARD_SUMMARY_SUCCESS,
        payload:data}
}

export const exportBoarSummaryDetails = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getBoardSummaryExportDetailsAPI(req)
            .then(response => response.data)
            .then(data => {
                const {studyId, scenarioId, scenarioName, trainStartsType, boardType, distr, subDistr} = req;
                const fileName = getExcelFileName(`Board Summary Details ${studyId} ${scenarioId} ${scenarioName}`);
                const sheet = getExcelSheetName(`Board Summary ${trainStartsType} ${boardType} ${distr} ${subDistr}`);
                const {items = []} = data || {}
                const sheets = [
                    {name: sheet, headers: BOARD_SUMMARY_EXPPORT_DETAILS_COLUMN_DEFS, rows: items}
                ]
                excelExportDetails({fileName, sheets});
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-P028`, error))
            }).then(() => dispatch(free()))
    }
}

export const getBoardSummaryDetails = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getBoardSummaryDetailsAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getBoardSummaryDetailsSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S018",error))
            }).then(()=>dispatch(free()))
    }
}

export const getBoardSummaryDetailsSuccess =(data) =>{
    return {type: StudyActions.GET_BOARD_SUMMARY_DETAILS_SUCCESS,
        payload:data}
}

export const getBoardSummaryFail =(error) =>{
    return {type: StudyActions.GET_BOARD_SUMMARY_FAIL,
        payload:error}
}
export const changeBoardSummaryTrainStartsType = (data) => {
    return {
        type: StudyActions.CHANGE_BOARD_SUMMARY_TRAIN_STARTS_TYPE,
        payload: data
    }
}

export const changeBoardSummaryBoardType = (data) => {
    return {
        type: StudyActions.CHANGE_BOARD_SUMMARY_BOARD_TYPE,
        payload: data
    }
}

export const changeBoardSummaryDistrict = (data) => {
    return {
        type: StudyActions.CHANGE_BOARD_SUMMARY_DISTRICT,
        payload: data
    }
}

export const changeBoardSummarySubDistrict = (data) => {
    return {
        type: StudyActions.CHANGE_BOARD_SUMMARY_SUB_DISTRICT,
        payload: data
    }
}
export const changeBoardSummaryCurrRow = (data) => {
    return {
        type: StudyActions.CHANGE_BOARD_SUMMARY_CURR_ROW,
        payload: data
    }
}

//Train Delay Report
export const getTDRData = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getTDRDataAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTDRDataSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S019",error))
            }).then(()=>dispatch(free()))
    }
}
export const getTDRDataSuccess =(data) =>{
    return {type: StudyActions.GET_TDR_DATA_SUCCESS,
        payload:data}
}
export const changeTDRTrainType = (data) => {
    return {
        type: StudyActions.CHANGE_TDR_TRAIN_TYPE,
        payload: data
    }
}
export const changeTDRGroupBy = (data) => {
    return {
        type: StudyActions.CHANGE_TDR_GROUP_BY,
        payload: data
    }
}
export const changeTDRShowOS = (data) => {
    return {
        type: StudyActions.CHANGE_TDR_SHOW_OS,
        payload: data
    }
}

export const changeTDRShowValues = (data) => {
    return {
        type: StudyActions.CHANGE_TDR_SHOW_VALUES,
        payload: data
    }
}

export const changeTDRStatsType = (data) => {
    return {
        type: StudyActions.CHANGE_TDR_STATS_TYPE,
        payload: data

    }
}
export const clearFeedback = () =>{
    return{
        type: StudyActions.STUDY_CLEAR_FEEDBACK,
    }
}

//Scenario Configuration
export const getScenarioConfiguration = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getScenarioConfigurationAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getScenarioConfigurationSuccess(data));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S020",error))
            }).then(()=>dispatch(free()))
    }
}

export const getScenarioConfigurationSuccess =(data) =>{
    return {type: StudyActions.GET_SCENARIO_CONFIGURATION_SUCCESS,
        payload:data}
}
//Scenario job summary
export const getScenarioJobSummary = (req) => {
    return (dispatch) =>{
        dispatch(busy())
        getScenarioJobSummaryAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getScenarioJobSummarySuccess(data));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S031",error))
            }).then(()=>dispatch(free()))
    }
}

export const getScenarioJobSummarySuccess =(data) =>{
    return {type: StudyActions.GET_SCENARIO_JOB_SUMMARY_SUCCESS,
        payload:data}
}

export const changeScenarioConfigurationTab =(data) =>{
    return {type: StudyActions.CHANGE_SCENARIO_CONFIGURATION_TAB,
        payload:data}
}

export const getTrainViewerFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getTrainViewerFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTrainViewerFiltersSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S021`, error))
            }).then(()=> dispatch(free()))
    }
}

export const getCrewProfileSimpleFilters = () => {
    return (dispatch) => {
        dispatch(busy())
        getCrewProfileSimpleFiltersAPI()
            .then(response => response.data)
            .then(data => {
                dispatch(getCrewProfileSimpleFiltersSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S033`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getTrainViewerFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_TRAIN_VIEWER_FILTERS_SUCCESS,
        payload: data
    }
}
export const getCrewProfileSimpleFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_CREW_PROFILE_SIMPLE_FILTERS_SUCCESS,
        payload: data
    }
}

export const getBoardSummaryFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getBoardSummaryFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getBoardSummaryFiltersSuccess(data));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S022`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getBoardSummaryFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_BOARD_SUMMARY_FILTERS_SUCCESS,
        payload: data
    }
}

export const getDeadheadFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getDeadheadFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getDeadheadFiltersSuccess(data));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S023`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getDeadheadFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_DEADHEAD_FILTERS_SUCCESS,
        payload: data
    }
}

export const getTrainDelayFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getTrainDelayFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTrainDelayFiltersSuccess(data));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S024`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getTrainDelayFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_TRAIN_DELAY_FILTERS_SUCCESS,
        payload: data
    }
}

export const getSimulationTrainFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getSimulationTrainFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getSimulationTrainFiltersSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S025`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getSimulationTrainFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_SIMULATION_TRAIN_FILTERS_SUCCESS,
        payload: data
    }
}
export const getTurnUtilizationFilters = (req) => {
    return (dispatch) => {
        dispatch(busy())
        getTurnUtilizationFiltersAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getTurnUtilizationFiltersSuccess(data.items));
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S026`, error))
            }).then(()=> dispatch(free()))
    }
}
export const getTurnUtilizationFiltersSuccess = (data) => {
    return {
        type: StudyActions.GET_TURN_UTILIZATION_FILTERS_SUCCESS,
        payload: data
    }
}

//Hire Group Crew Starts Report
export const getHireGroupData = (req) => {
    return (dispatch) =>{
        dispatch(clearHireGroupData())
        dispatch(busy())
        getHireGroupDataAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getHireGroupDataSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S019",error))
            }).then(()=>dispatch(free()))
    }
}
export const clearHireGroupData =() =>{
    return {type: StudyActions.HIRE_GROUP_DATA_CLEAR}
}
export const getHireGroupDataSuccess =(data) =>{
    return {type: StudyActions.HIRE_GROUP_DATA_SUCCESS,
        payload:data}
}

export const exportHireGroupCrewStarts = (req) => {
    return (dispatch) => {
        dispatch(busy())
        const { mode} = req||{}
      const api =  getHireGroupDataAPI(req)
            api.then(response => response.data)
            .then(data => {
                const {scenarioId,scenarioName,trainStartsType} = req;
                const modeText = mode === DETAIL? `Details`:``
                const fileName = getExcelFileName(`Hire Group Crew Starts ${modeText} ${scenarioId} ${scenarioName}`);
                const sheetName = getExcelSheetName(`Hire Group Crew Starts ${scenarioId} ${scenarioName}`);
                let columnDefs=[];
                if(trainStartsType==='TOTALSTARTS'){
                    columnDefs=(mode === DETAIL? HIRE_GROUP_EXPORT_DETAIL_COLUMN_DEFS:HIRE_GROUP_EXPORT_COLUMN_DEFS);
                }else{
                    columnDefs=(mode === DETAIL? HIRE_GROUP_EXPORT_DETAIL_COLUMN_DEFS_AVG:HIRE_GROUP_EXPORT_COLUMN_DEFS_AVG);
                }
                const headers = columnDefs;
                const {items=[]} = data;
                const rows=items;

                const sheets=[
                    {name:sheetName, headers, rows},
                ]
                excelExportDetails({fileName, sheets});
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S030`, error))
            }).then(()=> dispatch(free()))
    }
}
//Hire Group Head Count Report
export const getHireGroupHeadCountData = (req) => {
    return (dispatch) =>{
       // dispatch(clearHireGroupData())
        dispatch(busy())
        getHireGroupHeadCountAPI(req)
            .then(response => response.data)
            .then(data => {
                dispatch(getHireGroupHeadCountDataSuccess(data.items));
            })
            .catch(error => {
                error && dispatch(notifyAppRequest("CCP-S019",error))
            }).then(()=>dispatch(free()))
    }
}

export const getHireGroupHeadCountDataSuccess =(data) =>{
    return {type: StudyActions.HIRE_GROUP_HEAD_COUNT_DATA_SUCCESS,
        payload:data}
}

export const exportHireGroupHeadCount = (req) => {
    return (dispatch) => {
        dispatch(busy())
        const { mode} = req||{}
      const api = getHireGroupHeadCountAPI(req);
            api.then(response => response.data)
            .then(data => {
                const {scenarioId,scenarioName} = req;
                const modeText = mode === DETAIL? `Details`:``
                const fileName = getExcelFileName(`Hire Group Head Count ${modeText} ${scenarioId} ${scenarioName}`);
                const sheetName = getExcelSheetName(`Hire Group Head Count ${scenarioId} ${scenarioName}`);
                let columnDefs=[];
               
                    columnDefs=(mode === DETAIL? HIRE_GROUP_HEADCOUNT_EXPORT_DETAIL_COLUMN_DEFS:HIRE_GROUP_HEADCOUNT_EXPORT_COLUMN_DEFS);
               
                const headers = columnDefs;
                const {items=[]} = data;
                const rows=items;

                const sheets=[
                    {name:sheetName, headers, rows},
                ]
                excelExportDetails({fileName, sheets});
            })
            .catch(error => {
                dispatch(notifyAppRequest(`CCP-S030`, error))
            }).then(()=> dispatch(free()))
    }
}


