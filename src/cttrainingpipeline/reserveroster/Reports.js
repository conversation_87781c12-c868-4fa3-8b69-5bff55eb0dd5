import React, { Component } from "react";
import { connect } from "react-redux";
import {
  Container,
  Row,
  Col,
  Form,
  Button,
  Tab,
  Nav,
  Card,
} from "react-bootstrap";
import { Typeahead } from "react-bootstrap-typeahead";
import { AgGridReact } from "ag-grid-react";
import * as XLSX from "xlsx";
import { getCtTraineesAPI } from "../ctTraineeActions";
import { getCtClassesLocation, getCtClasses } from "../ctClassesActions";
import "react-bootstrap-typeahead/css/Typeahead.css";

class Reports extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      datesToLocationsMap: [], // Array of {dates: [], locations: []} objects
      activeTab: "all",
      dataInitialized: false,
      exportAsSingleFile: true, // Default to single file export
    };
    this.datesRef = React.createRef();
    this.locationsRef = React.createRef();
  }

  componentDidMount() {
    const hasData =
      this.props.trainees?.length > 0 &&
      this.props.classLocations?.length > 0 &&
      this.props.classes?.length > 0;

    if (hasData) {
      this.setState({ loading: false, dataInitialized: true });
    } else {
      if (!this.props.trainees?.length) {
        this.props.getCtTraineesAPI();
      }
      if (!this.props.classLocations?.length) {
        this.props.getCtClassesLocation();
      }
      if (!this.props.classes?.length) {
        this.props.getCtClasses();
      }
    }
  }

  componentDidUpdate(prevProps) {
    const hasData =
      this.props.trainees?.length > 0 &&
      this.props.classLocations?.length > 0 &&
      this.props.classes?.length > 0;

    if (hasData && !this.state.dataInitialized) {
      this.setState({ loading: false, dataInitialized: true });
    }
  }

  // Helper function to check if a trainee is confirmed
  isConfirmed = (trainee) => {
    const { confirmed } = trainee;
    return (
      confirmed === 1 ||
      confirmed === true ||
      confirmed === "true" ||
      confirmed === "yes"
    );
  };

  getColumnDefs = () => {
    if (!this.props.trainees || this.props.trainees.length === 0) return [];

    const orderedColumns = [
      "traineeId",
      "pid",
      "fname",
      "lname",
      "email",
      "phone",
      "address",
      "city",
      "state",
      "req",
      "reqTitle",
      "confirmed",
    ];

    const sampleTrainee = this.props.trainees[0];
    const allFields = Object.keys(sampleTrainee);
    const orderedColumnDefs = orderedColumns
      .filter((field) => allFields.includes(field))
      .map((field) => ({
        headerName:
          field.charAt(0).toUpperCase() +
          field.slice(1).replace(/([A-Z])/g, " $1"),
        field: field,
        sortable: true,
        filter: true,
        resizable: true,
        cellRenderer:
          field === "confirmed"
            ? (params) => {
                return this.isConfirmed(params.data) ? "Yes" : "No";
              }
            : undefined,
      }));

    const remainingColumns = allFields
      .filter((field) => !orderedColumns.includes(field))
      .map((field) => ({
        headerName:
          field.charAt(0).toUpperCase() +
          field.slice(1).replace(/([A-Z])/g, " $1"),
        field: field,
        sortable: true,
        filter: true,
        resizable: true,
      }));
    return [...orderedColumnDefs, ...remainingColumns];
  };

  handleTabSelect = (tabKey) => {
    this.setState({ activeTab: tabKey });
  };

  // Add a new date-location pair to the selection
  addDateLocationSelection = () => {
    this.setState((prevState) => ({
      datesToLocationsMap: [
        ...prevState.datesToLocationsMap,
        { dates: [], locations: [] },
      ],
    }));
  };

  // Remove a date-location pair from the selection
  removeDateLocationSelection = (index) => {
    this.setState((prevState) => ({
      datesToLocationsMap: prevState.datesToLocationsMap.filter(
        (_, i) => i !== index
      ),
    }));
  };

  // Update the dates for a specific date-location pair
  handleDateChange = (selected, index) => {
    const isAllSelected = selected.some((item) => item.id === "all");
    let newSelected;

    if (isAllSelected) {
      if (
        this.state.datesToLocationsMap[index]?.dates.length !==
        this.props.classes.length
      ) {
        newSelected = [
          { id: "all", classDate: "Select All" },
          ...this.props.classes.map((cls) => ({
            id: cls.classId,
            classDate: cls.classDate,
          })),
        ];
      } else {
        newSelected = [];
      }
    } else {
      newSelected = selected;
    }

    this.setState((prevState) => {
      const updatedMap = [...prevState.datesToLocationsMap];
      updatedMap[index] = {
        ...updatedMap[index],
        dates: newSelected,
      };
      return { datesToLocationsMap: updatedMap };
    });
  };

  // Update the locations for a specific date-location pair
  handleLocationChange = (selected, index) => {
    const isAllSelected = selected.some((item) => item.id === "all");
    let newSelected;

    if (isAllSelected) {
      const allLocations = this.getAvailableLocationsForDates(
        this.state.datesToLocationsMap[index]?.dates || []
      );

      if (
        this.state.datesToLocationsMap[index]?.locations.length !==
        allLocations.length
      ) {
        newSelected = [
          { id: "all", location: "Select All" },
          ...allLocations.map((loc) => ({
            id: loc.classLocationId,
            location: loc.location,
          })),
        ];
      } else {
        newSelected = [];
      }
    } else {
      newSelected = selected;
    }

    this.setState((prevState) => {
      const updatedMap = [...prevState.datesToLocationsMap];
      updatedMap[index] = {
        ...updatedMap[index],
        locations: newSelected,
      };
      return { datesToLocationsMap: updatedMap };
    });
  };

  // Get available locations for selected dates
  getAvailableLocationsForDates = (selectedDates) => {
    const selectedDateValues = selectedDates
      .filter((item) => item.id !== "all")
      .map((item) => item.classDate);

    if (selectedDateValues.length === 0) return [];

    const relevantClasses = this.props.classes.filter((cls) =>
      selectedDateValues.includes(cls.classDate)
    );

    return this.props.classLocations
      .filter((loc) =>
        relevantClasses.some((cls) => cls.classId === loc.classId)
      )
      .reduce((unique, loc) => {
        const exists = unique.some((item) => item.location === loc.location);
        if (!exists) {
          unique.push(loc);
        }
        return unique;
      }, []);
  };

  // Get all valid date-location combinations based on current selections
  getDateLocationCombinations = () => {
    const combinations = [];

    this.state.datesToLocationsMap.forEach((selection) => {
      const selectedDates = selection.dates
        .filter((item) => item.id !== "all")
        .map((item) => item.classDate);

      const selectedLocs = selection.locations
        .filter((item) => item.id !== "all")
        .map((item) => item.location);

      if (selectedDates.length === 0 || selectedLocs.length === 0) return;

      // Find all matching class locations
      const matchingCombos = this.props.classLocations
        .filter(
          (loc) =>
            selectedLocs.includes(loc.location) &&
            this.props.classes.some(
              (cls) =>
                selectedDates.includes(cls.classDate) &&
                cls.classId === loc.classId
            )
        )
        .map((loc) => {
          const classInfo = this.props.classes.find(
            (cls) => cls.classId === loc.classId
          );
          const trainees = this.props.trainees.filter(
            (trainee) =>
              trainee.classLocationId === loc.classLocationId &&
              this.isConfirmed(trainee)
          );
          return {
            date: classInfo.classDate,
            location: loc.location,
            classLocationId: loc.classLocationId,
            trainees,
          };
        })
        .filter((combo) => combo.trainees.length > 0); // Filter out combinations with no confirmed trainees

      combinations.push(...matchingCombos);
    });

    return combinations;
  };

  // Get all trainees from valid combinations
  getAllConfirmedTrainees = () => {
    const combinations = this.getDateLocationCombinations();
    const allTrainees = [];

    combinations.forEach((combo) => {
      combo.trainees.forEach((trainee) => {
        // Avoid duplicates
        if (!allTrainees.some((t) => t.traineeId === trainee.traineeId)) {
          allTrainees.push(trainee);
        }
      });
    });

    return allTrainees;
  };

  // Create a styled worksheet for a specific combination
  createWorksheet = (combo, columnDefs, columnHeaders) => {
    const headerData = [
      ["Class Date:", combo.date],
      ["Location:", combo.location],
      ["Total Confirmed Trainees:", combo.trainees.length.toString()],
      [],
    ];
    const ws = XLSX.utils.aoa_to_sheet(headerData);
    XLSX.utils.sheet_add_aoa(ws, [columnHeaders], {
      origin: { r: headerData.length, c: 0 },
    });

    const traineeData = combo.trainees.map((trainee) =>
      columnDefs.map((col) => {
        const value = trainee[col.field];

        if (col.field === "confirmed") {
          return this.isConfirmed(trainee) ? "Yes" : "No";
        }
        if (value === null || value === undefined) return "";
        if (typeof value === "boolean") return value ? "Yes" : "No";
        if (value instanceof Date) return value.toISOString().split("T")[0];
        return value.toString();
      })
    );
    XLSX.utils.sheet_add_aoa(ws, traineeData, {
      origin: { r: headerData.length + 1, c: 0 },
    });

    const range = XLSX.utils.decode_range(ws["!ref"]);

    const headerRowStyle = {
      font: { bold: true, color: { rgb: "000000" } },
      fill: { fgColor: { rgb: "CCCCCC" } },
      alignment: { horizontal: "center", vertical: "center" },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" },
      },
    };

    const dataCellStyle = {
      alignment: { horizontal: "left", vertical: "center", wrapText: true },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" },
      },
    };

    const titleStyle = {
      font: { bold: true, color: { rgb: "000000" } },
      alignment: { horizontal: "left", vertical: "center" },
    };

    for (let R = range.s.r; R <= range.e.r; R++) {
      for (let C = range.s.c; C <= range.e.c; C++) {
        const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
        if (!ws[cellRef]) ws[cellRef] = { v: "" };

        if (R < headerData.length - 1) {
          ws[cellRef].s = titleStyle;
        } else if (R === headerData.length) {
          ws[cellRef].s = headerRowStyle;
        } else {
          ws[cellRef].s = dataCellStyle;
        }
      }
    }

    const defaultWidth = 12;
    const columnWidths = columnDefs.map((col) => {
      switch (col.field) {
        case "traineeId":
        case "pid":
          return 10;
        case "email":
        case "address":
        case "notes":
        case "comments":
          return 30;
        case "phone":
          return 15;
        case "confirmed":
          return 10;
        default:
          return defaultWidth;
      }
    });

    ws["!cols"] = columnWidths.map((width) => ({ width }));
    ws["!rows"] = Array(range.e.r + 1).fill({ hpt: 25 });

    return ws;
  };

  handleExportAsSeparateFiles = () => {
    const combinations = this.getDateLocationCombinations();
    if (combinations.length === 0) return;

    const columnDefs = this.getColumnDefs();
    const columnHeaders = columnDefs.map((col) => col.headerName);
    const today = new Date().toISOString().split("T")[0];

    combinations.forEach((combo) => {
      if (combo.trainees.length > 0) {
        const wb = XLSX.utils.book_new();
        const ws = this.createWorksheet(combo, columnDefs, columnHeaders);

        const sheetName = `${combo.date}_${combo.location.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}`.slice(0, 31);

        XLSX.utils.book_append_sheet(wb, ws, "Report");

        const fileName = `Confirmed_Trainees_${
          combo.date
        }_${combo.location.replace(/[^a-zA-Z0-9]/g, "_")}_${today}.xlsx`;

        XLSX.writeFile(wb, fileName);
      }
    });
  };

  handleExportAsSingleFile = () => {
    const combinations = this.getDateLocationCombinations();
    if (combinations.length === 0) return;

    const wb = XLSX.utils.book_new();
    const columnDefs = this.getColumnDefs();
    const columnHeaders = columnDefs.map((col) => col.headerName);

    combinations.forEach((combo) => {
      if (combo.trainees.length > 0) {
        const ws = this.createWorksheet(combo, columnDefs, columnHeaders);

        const sheetName = `${combo.date}_${combo.location.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}`.slice(0, 31);

        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      }
    });

    XLSX.writeFile(
      wb,
      `Confirmed_Trainees_Report_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  handleExport = () => {
    if (this.state.exportAsSingleFile) {
      this.handleExportAsSingleFile();
    } else {
      this.handleExportAsSeparateFiles();
    }
  };

  toggleExportMode = () => {
    this.setState((prevState) => ({
      exportAsSingleFile: !prevState.exportAsSingleFile,
    }));
  };

  render() {
    const { loading, datesToLocationsMap, activeTab } = this.state;
    const { classes } = this.props;

    // Common styles
    const headingStyle = {
      color: "darkblue",
      fontWeight: "bolder",
      marginBottom: "1rem",
      padding: "0.5rem 0",
      borderBottom: "2px solid #dee2e6",
    };

    // Default column definition for AgGrid
    const defaultColDef = {
      filter: true,
      sortable: true,
      resizable: true,
      flex: 1,
      minWidth: 100,
    };

    if (loading) {
      return (
        <Container fluid className="px-4 mt-4">
          <Row>
            <Col>
              <Card className="shadow-sm">
                <Card.Body className="text-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <h4 className="mt-3">Loading data...</h4>
                  <p className="text-muted">
                    Please wait while we load the necessary information.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      );
    }

    const allConfirmedTrainees = this.getAllConfirmedTrainees();
    const dateLocationCombinations = this.getDateLocationCombinations();

    return (
      <Container fluid className="px-4 mt-4">
        {/* Main Heading */}
        <Row className="mb-4">
          <Col>
            <Card className="shadow-sm" style={{ border: "1px solid #000" }}>
              <Card.Body>
                <h2 style={headingStyle} className="mb-2">
                  Trainee Reports
                </h2>
                <p className="text-muted">
                  Select dates and locations to generate reports for confirmed
                  trainees.
                </p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Date-Location Selection Cards */}
        {datesToLocationsMap.map((selection, index) => (
          <Row key={index} className="mb-3">
            <Col>
              <Card className="shadow-sm" style={{ border: "1px solid #000" }}>
                <Card.Body className="py-4">
                  <Row>
                    <Col md={5}>
                      <Form.Group>
                        <Form.Label className="fw-bold">Class Dates</Form.Label>
                        <Typeahead
                          id={`dates-typeahead-${index}`}
                          multiple
                          onChange={(selected) =>
                            this.handleDateChange(selected, index)
                          }
                          options={[
                            { id: "all", classDate: "Select All" },
                            ...classes.map((cls) => ({
                              id: cls.classId,
                              classDate: cls.classDate,
                            })),
                          ]}
                          placeholder="Select dates..."
                          selected={selection.dates}
                          labelKey="classDate"
                          maxHeight="200px"
                          renderMenuItemChildren={(option) => (
                            <div className="d-flex align-items-center">
                              <Form.Check
                                type="checkbox"
                                checked={selection.dates.some(
                                  (item) => item.id === option.id
                                )}
                                readOnly
                                className="me-2"
                              />
                              <span>{option.classDate}</span>
                            </div>
                          )}
                        />
                      </Form.Group>
                    </Col>

                    <Col md={5}>
                      <Form.Group>
                        <Form.Label className="fw-bold">Locations</Form.Label>
                        <Typeahead
                          id={`locations-typeahead-${index}`}
                          multiple
                          onChange={(selected) =>
                            this.handleLocationChange(selected, index)
                          }
                          options={[
                            { id: "all", location: "Select All" },
                            ...this.getAvailableLocationsForDates(
                              selection.dates
                            ).map((loc) => ({
                              id: loc.classLocationId,
                              location: loc.location,
                            })),
                          ]}
                          placeholder="Select locations..."
                          selected={selection.locations}
                          labelKey="location"
                          disabled={selection.dates.length === 0}
                          maxHeight="200px"
                          renderMenuItemChildren={(option) => (
                            <div className="d-flex align-items-center">
                              <Form.Check
                                type="checkbox"
                                checked={selection.locations.some(
                                  (item) => item.id === option.id
                                )}
                                readOnly
                                className="me-2"
                              />
                              <span>{option.location}</span>
                            </div>
                          )}
                        />
                      </Form.Group>
                    </Col>

                    <Col md={2} className="d-flex align-items-end">
                      <Button
                        variant="danger"
                        onClick={() => this.removeDateLocationSelection(index)}
                        className="mb-2 w-100"
                      >
                        Remove
                      </Button>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ))}

        {/* Action Buttons */}
        <Row className="mb-4">
          <Col>
            <Card className="shadow-sm border-0">
              <Card.Body className="py-3">
                <Button
                  variant="outline-primary"
                  onClick={this.addDateLocationSelection}
                  className="me-2"
                >
                  <i className="bi bi-plus-circle me-1"></i> Add Date-Location
                  Filter
                </Button>

                {dateLocationCombinations.length > 0 && (
                  <>
                    <Button
                      variant="success"
                      onClick={this.handleExport}
                      className="me-2"
                    >
                      <i className="bi bi-file-excel me-1"></i> Export to Excel
                      <img
                        src="../assets/images/excel.png"
                        className="label-img"
                        alt=""
                        style={{
                          paddingLeft: ".5rem",
                          width: "16px",
                          height: "16px",
                        }}
                      />
                    </Button>

                    <Form.Check
                      type="switch"
                      id="export-mode-switch"
                      label={
                        this.state.exportAsSingleFile
                          ? "Export as single file"
                          : "Export as separate files"
                      }
                      checked={this.state.exportAsSingleFile}
                      onChange={this.toggleExportMode}
                      className="d-inline-block ms-3"
                      style={{
                        position: "relative",
                        top: "8px",
                      }}
                    />
                  </>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Results Section */}
        {dateLocationCombinations.length > 0 ? (
          <Row>
            <Col>
              <Card
                className="shadow-sm mb-4"
                style={{ border: "1px solid #000" }}
              >
                <Card.Body>
                  <h3 style={headingStyle}>Confirmed Candidates Information</h3>

                  {/* Scrollable Tabs */}
                  <div
                    className="overflow-auto mb-3"
                    style={{ whiteSpace: "nowrap" }}
                  >
                    <Nav
                      variant="tabs"
                      className="mb-3 flex-nowrap"
                      activeKey={activeTab}
                      onSelect={this.handleTabSelect}
                    >
                      <Nav.Item>
                        <Nav.Link eventKey="all" className="px-4">
                          All Confirmed Trainees ({allConfirmedTrainees.length})
                        </Nav.Link>
                      </Nav.Item>
                      {dateLocationCombinations.map(
                        ({ date, location, trainees, classLocationId }) => (
                          <Nav.Item
                            key={`${date}-${location}-${classLocationId}`}
                          >
                            <Nav.Link
                              eventKey={`${date}-${location}-${classLocationId}`}
                              className="px-4"
                            >
                              {date} - {location} ({trainees.length})
                            </Nav.Link>
                          </Nav.Item>
                        )
                      )}
                    </Nav>
                  </div>

                  {/* Tab Content with Grid */}
                  <Tab.Content>
                    <Tab.Pane active={activeTab === "all"}>
                      <div
                        className="ag-theme-alpine"
                        style={{
                          height: "calc(100vh - 450px)",
                          width: "100%",
                          minHeight: "450px",
                        }}
                      >
                        <AgGridReact
                          columnDefs={this.getColumnDefs()}
                          rowData={allConfirmedTrainees}
                          defaultColDef={defaultColDef}
                          pagination={true}
                          paginationPageSize={50}
                          animateRows={true}
                          enableCellTextSelection={true}
                          rowSelection="multiple"
                          suppressRowClickSelection={false}
                        />
                      </div>
                    </Tab.Pane>
                    {dateLocationCombinations.map(
                      ({ date, location, trainees, classLocationId }) => (
                        <Tab.Pane
                          key={`${date}-${location}-${classLocationId}`}
                          active={
                            activeTab ===
                            `${date}-${location}-${classLocationId}`
                          }
                        >
                          <div
                            className="ag-theme-alpine"
                            style={{
                              height: "calc(100vh - 450px)",
                              width: "100%",
                              minHeight: "450px",
                            }}
                          >
                            <AgGridReact
                              columnDefs={this.getColumnDefs()}
                              rowData={trainees}
                              defaultColDef={defaultColDef}
                              pagination={true}
                              paginationPageSize={50}
                              animateRows={true}
                              enableCellTextSelection={true}
                              rowSelection="multiple"
                              suppressRowClickSelection={false}
                            />
                          </div>
                        </Tab.Pane>
                      )
                    )}
                  </Tab.Content>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ) : // Empty State
        datesToLocationsMap.length > 0 ? (
          <Row>
            <Col>
              <Card className="shadow-sm" style={{ border: "1px solid #000" }}>
                <Card.Body className="text-center py-5">
                  <h4>No confirmed trainees found for the selected criteria</h4>
                  <p className="text-muted">
                    Try selecting different dates or locations, or check if
                    there are confirmed trainees for these selections.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ) : (
          <Row>
            <Col>
              <Card className="shadow-sm border-0">
                <Card.Body className="text-center py-5">
                  <div className="mb-3">
                    <i
                      className="bi bi-plus-circle"
                      style={{ fontSize: "2rem", color: "#6c757d" }}
                    ></i>
                  </div>
                  <h4>No selection criteria added</h4>
                  <p className="text-muted">
                    Click "Add Date-Location Filter" to start building your
                    report.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </Container>
    );
  }
}

const mapStateToProps = (state) => ({
  trainees: state.cttp.cttpTraineesRowData,
  classLocations: state.cttp.classLocations,
  classes: state.cttp.cttpClassesRowData,
});

const mapDispatchToProps = {
  getCtTraineesAPI,
  getCtClassesLocation,
  getCtClasses,
};

export default connect(mapStateToProps, mapDispatchToProps)(Reports);
