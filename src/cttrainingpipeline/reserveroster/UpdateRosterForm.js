import React, { Component } from "react";
import { AgGridReact } from "ag-grid-react";
import { Container, Row, Col, Form, Button, Modal } from "react-bootstrap";
import AlertModal from "./alertModal";
import ClassTraineeComponent from "./AssignClassesContent";
import { TRAINEE_INFORMATION_COLUMN_DEFS } from "../../config/ccpconstants";
import {
  getCtTraineesAPI,
  unassignClassesForTrainees,
  updateTrainee,
} from "../ctTraineeActions";
import { getCtClassesLocation, getCtClasses } from "../ctClassesActions";
import { connect } from "react-redux";
import { isEqual } from "lodash";

class UpdateRosterForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      selectedRows: [],
      showModal: false,
      selectedTraineeIds: [],
      selectedRowData: {},
      showModal2: false,
      classDetails: null,
      uniqueLocations: [],
    };
    this.handleRowSelection = this.handleRowSelection.bind(this);
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleBatchEmail = this.handleBatchEmail.bind(this);
    this.toggleModal = this.toggleModal.bind(this);
    this.toggleModal2 = this.toggleModal2.bind(this);
    this.handleUnassign = this.handleUnassign.bind(this);
    this.handleUpdate = this.handleUpdate.bind(this);
    this.fetchClassDetails = this.fetchClassDetails.bind(this);
  }

  componentDidMount() {
    const {
      cttpTraineesRowData,
      rowData,
      classLocations,
      getCtTraineesAPI,
      getCtClasses,
      getCtClassesLocation,
    } = this.props;

    if (!cttpTraineesRowData || cttpTraineesRowData.length === 0) {
      getCtTraineesAPI();
    } else {
      this.setState({ loading: false });
    }

    if (!rowData || rowData.length === 0) {
      getCtClasses();
    }

    if (!classLocations || classLocations.length === 0) {
      getCtClassesLocation();
    }
  }

  componentDidUpdate(prevProps) {
    const { cttpTraineesRowData, rowData, classLocations } = this.props;
    const { loading } = this.state;
    if (loading && cttpTraineesRowData && cttpTraineesRowData.length > 0) {
      this.setState({ loading: false });
    }
    const dataLoaded =
      !isEqual(cttpTraineesRowData, prevProps.cttpTraineesRowData) ||
      !isEqual(rowData, prevProps.rowData) ||
      !isEqual(classLocations, prevProps.classLocations);

    if (dataLoaded && loading) {
      this.setState({ loading: false });
    }
  }

  fetchClassDetails(classLocationId) {
    const { classLocations, rowData } = this.props;

    if (!classLocations || !rowData) {
      return;
    }

    const classLocation = classLocations.find(
      (location) => location.classLocationId === classLocationId
    );

    if (classLocation) {
      const classDetails = rowData.find(
        (cls) => cls.classId === classLocation.classId
      );

      if (classDetails) {
        const concatenatedDetails = `${classDetails.classDate}, ${classLocation.location}`;
        this.setState({ classDetails: concatenatedDetails });
      }
    }
  }

  handleRowSelection(event) {
    if (!event || !event.api) {
      return;
    }

    const selectedNodes = event.api.getSelectedNodes();
    if (!selectedNodes) {
      return;
    }

    const selectedDataArray = selectedNodes.map((node) => node.data);

    if (selectedDataArray && selectedDataArray.length > 0) {
      // Extract unique locations for warning display
      const uniqueLocationsArray = Array.from(
        new Set(
          selectedDataArray
            .map((item) => item.location)
            .filter((location) => location != null)
        )
      );
      const selectedRow = selectedDataArray[selectedDataArray.length - 1];
      const completeSelectedRow = {
        traineeId: selectedRow.traineeId || "",
        pid: selectedRow.pid || "",
        attempt: selectedRow.attempt || "",
        location: selectedRow.location || "",
        notes: selectedRow.notes || "",
        comments: selectedRow.comments || "",
        classLocationId: selectedRow.classLocationId || "",
        status: selectedRow.status || "",
        confirmed: selectedRow.confirmed || "",
        ...selectedRow,
      };
      this.setState(
        {
          selectedRows: selectedDataArray,
          selectedTraineeIds: selectedDataArray.map((row) => row.traineeId),
          selectedRowData: completeSelectedRow,
          classDetails: null,
          uniqueLocations: uniqueLocationsArray,
        },
        () => {
          if (selectedRow.classLocationId) {
            this.fetchClassDetails(selectedRow.classLocationId);
          }
        }
      );
    } else {
      this.setState({
        selectedRowData: {},
        classDetails: null,
        selectedRows: [],
        selectedTraineeIds: [],
      });
    }
  }

  handleInputChange(e, fieldName) {
    if (!e || !e.target) {
      return;
    }

    const value = e.target.value;
    this.setState((prevState) => ({
      selectedRowData: {
        ...prevState.selectedRowData,
        [fieldName]: value,
      },
    }));
  }

  handleBatchEmail() {
    const { selectedRows } = this.state;

    if (!selectedRows || selectedRows.length === 0) {
      alert("No trainees selected for email.");
      return;
    }

    try {
      const bccField = selectedRows
        .map((row) => (row && row.email ? encodeURIComponent(row.email) : ""))
        .filter((email) => email)
        .join("; ");

      if (!bccField) {
        alert("No valid email addresses found for selected rows.");
        return;
      }

      const mailtoLink = `mailto:?bcc=${bccField}&subject=PEP Candidate Information&body=`;
      window.open(mailtoLink, "_blank");
    } catch (error) {
      console.error("Error creating email link:", error);
      alert("There was an error creating the email. Please try again.");
    }
  }

  toggleModal() {
    this.setState((state) => ({ showModal: !state.showModal }));
  }

  toggleModal2() {
    this.setState((state) => ({ showModal2: !state.showModal2 }));
  }

  handleUnassign() {
    const { selectedTraineeIds } = this.state;
    const { unassignClassesForTrainees } = this.props;

    if (!selectedTraineeIds || selectedTraineeIds.length === 0) {
      alert("No trainees selected for unassignment.");
      return;
    }

    unassignClassesForTrainees(selectedTraineeIds);
    this.toggleModal();
    if (this.gridApi) {
      this.gridApi.deselectAll();
    }

    this.setState({
      selectedRowData: {},
      selectedRows: [],
      selectedTraineeIds: [],
    });
  }

  handleUpdate() {
    const { selectedRowData } = this.state;
    const { updateTrainee, getCtTraineesAPI } = this.props;

    if (!selectedRowData || !selectedRowData.traineeId) {
      alert("No trainee selected for update.");
      return;
    }

    // Use exact same payload structure as the original code
    const payload = {
      traineeId: selectedRowData.traineeId,
      pid: selectedRowData.pid,
      attempt: selectedRowData.attempt,
      location: selectedRowData.location,
      notes: selectedRowData.notes,
      comments: selectedRowData.comments,
      status: selectedRowData.status,
      confirmed: parseInt(selectedRowData.confirmed, 10),
    };

    console.log("Sending update payload:", payload);

    updateTrainee(payload)
      .then(() => {
        console.log("Update successful");

        getCtTraineesAPI();

        if (this.gridApi) {
          this.gridApi.deselectAll();
        }

        this.setState({
          selectedRowData: {},
          selectedRows: [],
          selectedTraineeIds: [],
          loading: true,
        });
      })
      .catch((error) => {
        console.error("Update failed:", error);
        alert("There was an error updating the trainee information.");

        this.setState({ loading: false });
      });
  }
  render() {
    const { cttpTraineesRowData } = this.props;
    const {
      loading,
      selectedRowData,
      showModal,
      showModal2,
      uniqueLocations,
      classDetails,
    } = this.state;

    const actionsColumnDef = {
      headerName: "Actions",
      field: "actions",
      minWidth: 70,
      maxWidth: 100,
      resizable: true,
      cellRenderer: (params) => {
        if (!params || !params.data) {
          return null;
        }

        const isClassLocationIdKnown = !!params.data.classLocationId;

        return isClassLocationIdKnown ? (
          <Button
            variant="danger"
            size="sm"
            style={{ padding: "2px 8px", fontSize: "12px" }}
            onClick={(e) => {
              if (e) {
                e.stopPropagation();
              }

              if (params.node) {
                params.node.setSelected(true);
              }

              if (params.context && params.context.componentParent) {
                params.context.componentParent.toggleModal();
              }
            }}
            aria-label={`Unassign trainee ${params.data.traineeId}`}
          >
            Un-Assign
          </Button>
        ) : null;
      },
    };

    const columnDefs = [actionsColumnDef, ...TRAINEE_INFORMATION_COLUMN_DEFS];

    if (loading) {
      return (
        <div role="status" aria-live="polite">
          Loading...
        </div>
      );
    }

    return (
      <Container fluid>
        <AlertModal />

        <Row className="mb-3">
          <Col style={{ border: "1px solid #ccc", padding: "10px" }}>
            <div
              className="ag-theme-alpine"
              style={{
                height: "30rem",
                width: "100%",
                marginBottom: "1rem",
              }}
            >
              <h2 style={{ color: "darkblue", fontWeight: "bolder" }}>
                PEP Candidates Information
              </h2>
              {cttpTraineesRowData && cttpTraineesRowData.length > 0 ? (
                <AgGridReact
                  columnDefs={columnDefs}
                  rowData={cttpTraineesRowData}
                  onRowSelected={this.handleRowSelection}
                  context={{ componentParent: this }}
                  defaultColDef={{
                    filter: true,
                    sortable: true,
                    resizable: true,
                    suppressSizeToFit: false,
                    minWidth: 70,
                    width: 150,
                  }}
                  onGridReady={(params) => {
                    if (params && params.api) {
                      this.gridApi = params.api;
                    }
                    if (params && params.columnApi) {
                      this.gridColumnApi = params.columnApi;
                    }
                  }}
                  pagination={true}
                  paginationPageSize={20}
                  rowSelection={"multiple"}
                  rowMultiSelectWithClick={true}
                  suppressRowClickSelection={false}
                />
              ) : (
                <div>No trainee data available</div>
              )}
            </div>
            <div style={{ textAlign: "left", marginTop: "3rem" }}>
              <Button
                onClick={this.handleBatchEmail}
                disabled={
                  !this.state.selectedRows ||
                  this.state.selectedRows.length === 0
                }
                aria-label="Send batch email to selected trainees"
              >
                Email
              </Button>
              {uniqueLocations && uniqueLocations.length > 1 && (
                <div
                  style={{ color: "red", marginTop: "10px" }}
                  aria-live="polite"
                  role="alert"
                >
                  <span>⚠️ Warning:</span> Selected PEP candidates are from
                  different locations
                </div>
              )}
            </div>
          </Col>
        </Row>

        {selectedRowData && Object.keys(selectedRowData).length > 0 && (
          <Row className="mt-3">
            <Col
              style={{
                border: "1px solid #ccc",
                padding: "10px",
                fontWeight: "bold",
              }}
            >
              <Form>
                <Row>
                  <Col md={6}>
                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Trainee ID</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          type="text"
                          value={selectedRowData.traineeId || ""}
                          readOnly
                          disabled
                          aria-label="Trainee ID"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">PID</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          type="text"
                          value={selectedRowData.pid || ""}
                          readOnly
                          aria-label="PID"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Location</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          type="text"
                          value={selectedRowData.location || ""}
                          onChange={(e) =>
                            this.handleInputChange(e, "location")
                          }
                          aria-label="Location"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Attempt</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          type="text"
                          value={selectedRowData.attempt || ""}
                          onChange={(e) => this.handleInputChange(e, "attempt")}
                          aria-label="Attempt"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Notes</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          as="textarea"
                          value={selectedRowData.notes || ""}
                          onChange={(e) => this.handleInputChange(e, "notes")}
                          style={{ height: "50px" }}
                          aria-label="Notes"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Comments</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          as="textarea"
                          value={selectedRowData.comments || ""}
                          onChange={(e) =>
                            this.handleInputChange(e, "comments")
                          }
                          style={{ height: "50px" }}
                          aria-label="Comments"
                        />
                      </div>
                    </div>

                    <Button
                      onClick={this.handleUpdate}
                      variant="primary"
                      className="me-2"
                      aria-label="Update trainee information"
                    >
                      Update
                    </Button>
                  </Col>

                  <Col md={6}>
                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Status</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          as="textarea"
                          value={selectedRowData.status || ""}
                          onChange={(e) => this.handleInputChange(e, "status")}
                          style={{ height: "50px" }}
                          aria-label="Status"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">Confirmed</Form.Label>
                      <div className="col-7">
                        <Form.Control
                          as="textarea"
                          value={selectedRowData.confirmed || ""}
                          onChange={(e) =>
                            this.handleInputChange(e, "confirmed")
                          }
                          style={{ height: "50px" }}
                          aria-label="Confirmation Status"
                        />
                      </div>
                    </div>

                    <div className="mb-3 row align-items-center">
                      <Form.Label className="col-3 mb-0">
                        Class Details
                      </Form.Label>
                      <div className="col-7">
                        {selectedRowData.classLocationId ? (
                          <>
                            <Form.Control
                              type="text"
                              value={
                                (classDetails ? classDetails + ", " : "") +
                                selectedRowData.classLocationId
                              }
                              disabled
                              aria-label="Assigned class details"
                            />
                            <p
                              style={{ color: "red" }}
                              aria-live="polite"
                              role="alert"
                              className="mt-2"
                            >
                              Unassign first to update the class
                            </p>
                          </>
                        ) : (
                          <Button
                            onClick={this.toggleModal2}
                            variant="primary"
                            aria-label="Assign class to trainee"
                          >
                            Assign Class
                          </Button>
                        )}
                      </div>
                    </div>
                  </Col>
                </Row>
              </Form>
            </Col>
          </Row>
        )}

        {/* Modal for Unassignment */}
        <Modal
          show={showModal}
          onHide={this.toggleModal}
          aria-labelledby="unassign-modal-title"
        >
          <Modal.Header closeButton>
            <Modal.Title id="unassign-modal-title">
              Confirm Unassignment
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to unassign class for the following
              trainee(s):
            </p>
            <ul>
              {this.state.selectedTraineeIds &&
                this.state.selectedTraineeIds.map((id) => (
                  <li key={id}>{id}</li>
                ))}
            </ul>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={this.toggleModal}>
              Cancel
            </Button>
            <Button variant="danger" onClick={this.handleUnassign}>
              Yes, Unassign
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Modal for Class Assignment */}
        <Modal
          show={showModal2}
          onHide={this.toggleModal2}
          aria-labelledby="assign-modal-title"
          size="lg"
        >
          <Modal.Header closeButton>
            <Modal.Title id="assign-modal-title">
              Assign Class For Trainee
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <ClassTraineeComponent selectedRowData={selectedRowData} />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={this.toggleModal2}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>
    );
  }
}

const mapStateToProps = (state) => ({
  cttpTraineesRowData: state.cttp.cttpTraineesRowData,
  rowData: state.cttp.cttpClassesRowData,
  classLocations: state.cttp.classLocations,
});

const mapDispatchToProps = {
  getCtTraineesAPI,
  unassignClassesForTrainees,
  updateTrainee,
  getCtClasses,
  getCtClassesLocation,
};

export default connect(mapStateToProps, mapDispatchToProps)(UpdateRosterForm);
