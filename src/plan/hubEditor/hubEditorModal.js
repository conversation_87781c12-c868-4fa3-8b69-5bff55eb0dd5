import { <PERSON><PERSON>, Col, Form, Modal } from "react-bootstrap";
import React from "react";

//Pool's turn add / edit component
class HubEditorModal extends React.Component {
    constructor(props) {
        super(props);
        const {show = false} = props;
        this.state = {
            show,
            hubName: props.hubName || ``,
            description: props.description || ``,
            duplicateError:false
        }
    }

    onApply = () => {
        const {handleSuccess, handleClose} = this.props;
        let {hubName, description} = this.state;
        handleClose();
        handleSuccess({hubName, description});
    }

    render() {
        const {title, show, handleClose, successLabel,hubData} = this.props;
        const {hubName, description,duplicateError } = this.state
        return (<>
            <Modal size="sm"
                   style={{opacity: 2}}
                   tabIndex="2"
                   role="dialog"
                   show={show}
                   onHide={handleClose}
                   aria-labelledby="contained-modal-title-vcenter"
                   centered>
                <Modal.Header closeButton className="settings-model-header"
                              style={{marginRight: "1rem", alignItems: "center"}}>
                    <Modal.Title style={{color: 'darkblue'}}> <b>
                        {title}
                    </b>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Row>
                            <Form.Group as={Col} md="12" controlId="hubName">
                                <Form.Text>Hub Name*:</Form.Text>
                                <Form.Control
                                    required
                                    type="text"
                                    maxLength={100}
                                    minLength={5}
                                    value={hubName}
                                    onChange={(event) => {
                                        const hubName= event.target.value;
                                        const index = hubData?.findIndex(item => {
                                            return item.hubName?.toString().trim().toUpperCase() === hubName?.trim().toUpperCase()
                                        });

                                        if(index > -1){
                                            this.setState({duplicateError:true})
                                        }
                                        else{
                                            this.setState({duplicateError:false})
                                        }

                                        this.setState({hubName: event.target.value ? event.target.value : ``})
                                        
                                    }}
                                    placeholder={`Hub Name`}
                                />
                                
                        {duplicateError && <p style={{color:'red'}}>Error! Same Hub Name already exists.</p>}
                            </Form.Group>
                           
                        </Form.Row>
                        <Form.Row>
                            <Form.Group as={Col} md="12" controlId="created">
                                <Form.Text>Hub Description*:</Form.Text>
                                <Form.Control
                                    required
                                    type="text"
                                    maxLength={100}
                                    minLength={5}
                                    value={description}
                                    onChange={(event) => {
                                        this.setState({description: event.target.value ? event.target.value : ``})
                                    }}
                                    placeholder={`Hub Description`}
                                />
                            </Form.Group>
                        </Form.Row>
                       
                    </Form>
                </Modal.Body>
                <Modal.Footer style={{justifyContent: "center"}}>
                    <Button variant="success"
                     disabled={this.state.description.length === 0 || this.state.hubName.length === 0 || duplicateError}
                     onClick={this.onApply}>{successLabel}</Button>
                    <Button variant="warning" onClick={handleClose}>Cancel</Button>
                </Modal.Footer>
            </Modal>

        </>)
    }
}
export default HubEditorModal;