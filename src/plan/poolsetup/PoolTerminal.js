import { But<PERSON>, Col, Form, Modal } from "react-bootstrap";
import React from "react";
import {  Typeahead } from 'react-bootstrap-typeahead';

//Pool's terminal add / edit component
class PoolTerminal extends React.Component {
    OSRef = React.createRef();
    constructor(props) {
        super(props);
        const {
            show = false,
            poolTerminal = {}
        } = props;
        const {
            id = ``,
            leadTime = ``,
            os = ``,
            poolHomeAway = ``,
            restHrs = ``,
            tieupShortCo = ``,
            tieupShortEn = ``,
            tieupThruCo = ``,
            tieupThruEn = ``,
            undistRest = `Y`,
            showTerminalWaring = false,
            showOSWaring = false,
            osDropdown = os ? [{ label: os }] : [],
        } = poolTerminal;
        this.state = {
            show,
            id,
            leadTime,
            os,
            poolHomeAway,
            restHrs,
            tieupShortCo,
            tieupShortEn,
            tieupThruCo,
            tieupThruEn,
            undistRest,
            showTerminalWaring,
            showOSWaring,
            osDropdown,
            updateTerminalWarning:false
        }
    }
    number_test = (n) => {
       
        var result = (n - Math.floor(n)) !== 0;
        if (result) {
            n.toString().split(".")[1].length > 2 ?
                this.setState({ restHrs: parseFloat(n).toFixed(2) }) :
                this.setState({ restHrs: n })
        }
        else {
            this.setState({ restHrs: n })
        }

    }
    convertValue = (n) => {        
        var result = (n - Math.floor(n)) !== 0;
        if(n.split("-").length ===2 || parseFloat(n) === 0)
            return '';
        // if( n.toString().split(".")[0].length > 2){            
        //     return '100.00';
        // }
        // else 
        if (result) {

           return n.toString().split(".")[1].length > 2 ? parseFloat(n).toFixed(2) : n;
        }
        else {
           return n;
        }

    }
    onApply = () => {
        const {handleSuccess, handleClose} = this.props;
        const {
            id,
            leadTime,
            os,
            poolHomeAway,
            restHrs,
            tieupShortCo,
            tieupShortEn,
            tieupThruCo,
            tieupThruEn,
            undistRest
        } = this.state;
        handleClose();
        handleSuccess({
            id,
            leadTime,
            os,
            poolHomeAway,
            restHrs,
            tieupShortCo,
            tieupShortEn,
            tieupThruCo,
            tieupThruEn,
            undistRest
        });
    }

    render() {
        const {title, show, handleClose, successLabel,poolTerminal={}} = this.props;
        const {
            leadTime,
            poolHomeAway,
            restHrs,
            tieupShortCo,
            tieupShortEn,
            tieupThruCo,
            tieupThruEn,
            undistRest,
            osDropdown
        } = this.state
        return (<>
            <Modal size="md"
                style={{ opacity: 2 }}
                tabindex="2"
                role="dialog"
                show={show}
                onHide={handleClose}
                aria-labelledby="contained-modal-title-vcenter"
                centered>
                <Modal.Header closeButton className="settings-model-header"
                    style={{ marginRight: "1rem", alignItems: "center" }}>
                    <Modal.Title style={{ color: 'darkblue' }}> <b>
                        {title}
                    </b>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={this.onApply}>
                        <Form.Row>
                            <Form.Group as={Col} md="6" controlId="poolHomeAway">
                                <Form.Text>Terminal:</Form.Text>
                                
                                <Form.Control
                                    required
                                    as={"select"}
                                    value={poolHomeAway}
                                    onChange={(event) => {
                                        const index = this.props?.currRow?.findIndex(item => item.poolHomeAway?.toString() === event.target.value);
                                        this.setState({ poolHomeAway: event.target.value ? event.target.value : `` });
                                        if (index > -1)
                                            this.setState({ showTerminalWaring: true })
                                        else{
                                            this.setState({ showTerminalWaring: false });
                                          if(event.target.value !=='Select' && poolTerminal.poolHomeAway!==undefined && poolTerminal.poolHomeAway?.toString()!==event.target.value.toString()){
                                            this.setState({ updateTerminalWarning: true })}else{this.setState({ updateTerminalWarning: false });}
                                        }
                                            
                                    }}
                                    placeholder={`Terminal`}
                                >
                                    <option selected>Select</option>
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                </Form.Control>
                                {this.state.showTerminalWaring && <p>Warning! Terminal already exists. Only proceed if you want to override existing information of the same Terminal.</p>}
                                {!this.state.showTerminalWaring && this.state.updateTerminalWarning && <p>Warning! If you proceed and update the terminal number here, please update terminal number from crew profile editor accordingly as well.</p>}
                            </Form.Group>
                            <Form.Group as={Col} md="6" controlId="os">
                                <Form.Text>OS:</Form.Text>
                                <Typeahead ref={this.OSRef}
                                    id="form-example"
                                    onInputChange={(text) => {
                                      
                                        this.setState({ os:text })
                                    }}
                                    onChange={(event) => {
                                        const index = this.props?.currRow?.findIndex(item => item.os?.toString() ===( event[0] ? event[0].label : ``));
                                        this.setState({ os: event[0] ? event[0].label : `` })
                                        this.setState({ osDropdown: event });
                                        if (index > -1)
                                            this.setState({ showOSWaring: true })
                                        else
                                            this.setState({ showOSWaring: false })

                                        
                                    }}
                                    options={this.props?.osCodes}
                                    placeholder="Select OS"
                                    selected={osDropdown}
                                />
                                 {this.state.showOSWaring && <p style={{color:'red'}}>Error! OS code already been used for another terminal. Please select a different OS code.</p>}
                            </Form.Group>
                        </Form.Row>
                        <Form.Row>
                            <Form.Group as={Col} md="6" controlId="leadTime">
                                <Form.Text>Lead Time in Mins:</Form.Text>
                                <Form.Control
                                    required
                                    type="number"

                                    value={leadTime}
                                    onChange={(event) => {
                                        this.setState({ leadTime: this.convertValue(event.target.value ? event.target.value : ``) });
                                        // this.setState({ leadTime: event.target.value ? event.target.value : `` })
                                    }}
                                    placeholder={`Lead Time in Mins`}
                                />
                            </Form.Group>
                            <Form.Group as={Col} md="6" controlId="restHrs">
                                <Form.Text>Rest (Hrs):</Form.Text>
                                <Form.Control
                                    required
                                    type="number"
                                    value={restHrs}
                                    step=".01"
                                    onChange={(event) => {
                                        // this.number_test(event.target.value);
                                         this.setState({ restHrs: this.convertValue(event.target.value ? event.target.value : ``) });
                                    }}
                                      placeholder={`Rest (Hrs)`}
                                />
                            </Form.Group>
                        </Form.Row>
                        <Form.Row>
                            <Form.Group as={Col} md="6" controlId="undistRest">
                                <Form.Text>Undisturb Rest:</Form.Text>
                                <Form.Control
                                    required
                                    as={"select"}
                                    value={undistRest}
                                    onChange={(event) => {
                                        this.setState({ undistRest: event.target.value ? event.target.value : `` })
                                    }}
                                    placeholder={`Undisturb Rest`}
                                >
                                    <option selected value="Y">Yes</option>
                                    <option value="N">No</option>
                                </Form.Control>
                            </Form.Group>
                        </Form.Row>
                        <Form.Row>
                            <Form.Group as={Col} md="6" controlId="tieupShortEn">
                                <Form.Text>Eng. Tie-Up Short:</Form.Text>
                                <Form.Control
                                    required
                                    as={"select"}
                                    value={tieupShortEn}
                                    onChange={(event) => {
                                       // const index = this.props?.currRow?.findIndex(item => item.tieupShortEn?.toString() === event.target.value);
                                        this.setState({ tieupShortEn: event.target.value ? event.target.value : `` });
                                    }}
                                    placeholder={`Eng. Tie-Up Short`}
                                >
                                    <option selected>Select</option>
                                    <option value="1">1 - Home On-duty Time</option>
                                    <option value="2">2 - Last On-duty Time</option>
                                    <option value="3">3 - Arrival On-duty Time</option>
                                    <option value="4">4 - Tie-Up Time</option>
                                </Form.Control>
                            </Form.Group>
                            <Form.Group as={Col} md="6" controlId="tieupThruEn">
                                <Form.Text>Eng. Tie-Up Thru:</Form.Text>
                                <Form.Control
									required
                                    as={"select"}
                                    value={tieupThruEn}
                                    onChange={(event) => {
										//const index = this.props?.currRow?.findIndex(item => item.tieupThruEn?.toString() === event.target.value);
                                        this.setState({tieupThruEn: event.target.value ? event.target.value : ``});
                                    }}
                                    placeholder={`Eng. Tie-Up Short`}
                                >
								    <option selected>Select</option>
                                    <option value="1">1 - Home On-duty Time</option>
                                    <option value="2">2 - Last On-duty Time</option>
                                    <option value="3">3 - Arrival On-duty Time</option>
                                    <option value="4">4 - Tie-Up Time</option>
							 </Form.Control> 
                            </Form.Group>
                        </Form.Row>
                        <Form.Row>
                            <Form.Group as={Col} md="6" controlId="tieupShortCo">
                                <Form.Text>Cond. Tie-Up Short:</Form.Text>
                                <Form.Control
                                    required
                                    as={"select"}
                                    value={tieupShortCo}
                                    onChange={(event) => {
                                        //const index = this.props?.currRow?.findIndex(item => item.tieupShortCo?.toString() === event.target.value);
                                        this.setState({ tieupShortCo: event.target.value ? event.target.value : `` });
                                    }}
                                    placeholder={`Eng. Tie-Up Short`}
                                >
                                    <option selected>Select</option>
                                    <option value="1">1 - Home On-duty Time</option>
                                    <option value="2">2 - Last On-duty Time</option>
                                    <option value="3">3 - Arrival On-duty Time</option>
                                    <option value="4">4 - Tie-Up Time</option>
                                </Form.Control>
                            </Form.Group>
                            <Form.Group as={Col} md="6" controlId="tieupThruCo">
                                <Form.Text>Cond. Tie-Up Thru:</Form.Text>
                                <Form.Control
                                    required
                                    as={"select"}
                                    value={tieupThruCo}
                                    onChange={(event) => {
                                       // const index = this.props?.currRow?.findIndex(item => item.tieupThruCo?.toString() === event.target.value);
                                        this.setState({ tieupThruCo: event.target.value ? event.target.value : `` });
                                    }}
                                    placeholder={`Eng. Tie-Up Short`}
                                >
                                    <option selected>Select</option>
                                    <option value="1">1 - Home On-duty Time</option>
                                    <option value="2">2 - Last On-duty Time</option>
                                    <option value="3">3 - Arrival On-duty Time</option>
                                    <option value="4">4 - Tie-Up Time</option>
                                </Form.Control>
                            </Form.Group>
                        </Form.Row>


                        {/*<Form.Row>*/}
                        {/*    <Form.Group as={Col} md="12" controlId="created">*/}
                        {/*        <Form.Text>Craft:</Form.Text>*/}
                        {/*        <Form.Control*/}
                        {/*            as="select"*/}
                        {/*            onChange={(event) => this.setState({craft: event.target.selectedOptions[0].id})}*/}
                        {/*        >*/}
                        {/*            {CRAFTS_VALID.map((option) => <option id={option.key}*/}
                        {/*                                                  key={option.key}*/}
                        {/*                                                  selected={option.key === (craft != undefined ? craft : '')}*/}
                        {/*            >{option.desc}*/}
                        {/*            </option>)}*/}
                        {/*        </Form.Control>*/}

                        {/*    </Form.Group>*/}
                        {/*</Form.Row>*/}
                    </Form>
                </Modal.Body>
                <Modal.Footer style={{ justifyContent: "center" }}>
                    <Button variant="success" type="submit" disabled={this.state.showOSWaring || this.state.os.length === 0} onClick={this.onApply}>{successLabel}</Button>
                    <Button variant="warning" onClick={handleClose}>Cancel</Button>
                </Modal.Footer>
            </Modal>

        </>)
    }
}

export default PoolTerminal;