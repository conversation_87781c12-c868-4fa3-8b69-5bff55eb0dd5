/* eslint eqeqeq: "off"*/
import React from "react";
import {connect} from "react-redux"
import {
    getHubEditorData,
    saveHubEditor,
    updateHubEditor,
    removeHubEditor,
    getHubDetails,
    changeHubDetailCurrTab
} from "./planActions";
import {Button, ButtonGroup, ButtonToolbar, Form, Modal, Row, Tab, Tabs} from "react-bootstrap";
import {
    HUBEDITOR_COLUMN_DEFS,
    HUBEDITOR_POOLS_COLUMN_DEFS,
    HUBEDITOR_EXB_COLUMN_DEFS,
    MULTI_SORT_KEY
} from "../config/ccpconstants";
import {AgGridReact} from "ag-grid-react";
import {getDefaultColDef, getGenericRowHeight} from "../util/Utils";
import HubEditorModal  from './hubEditor/hubEditorModal'
import PopupActionAlert from "../components/PopupActionAlert";
import PopupAlert from "../components/PopupAlert";
import HubConfirmSave from "./hubEditor/HubConfirmSave";

class HubEditor extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            mode: ``,
            selectedRowIndex:``,
            selectedRowEvent:'',
            addHub:false,
            clonedEvent:null,
            clearBottomPane:false
        };
        this.refOne=React.createRef();
    }
    // This function will be called on first time render of component.
    // This will set the ag-grid reference in state, auto adjust the table columns, and select the current row in table
    onGridReady = params => {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        params.columnApi.autoSizeAllColumns();
        if(this.state.currData){
        this.setState({currData:undefined});
        }
    };

    onHubPoolsGridReady = params => {
        this.hubPoolsgridApi = params.api;
        this.hubPoolsgridColumnApi = params.columnApi;
        params.columnApi.autoSizeAllColumns();
    }

    refOne=null;
    handleRowSelection = (event) => {
        if (!event.node.selected) {
            return;
        }
        
        if(this.state.isUpdated  && !this.state.addHub ){
            this.setState({selectedRowIndex:event.rowIndex})
            this.setState({selectedRowEvent:event});
            this.setState({mode:"ShowWarning"});            
            return;
        }

        if(this.state.addHub){
            this.setState({addHub:false});
        } 
        const selectedRows = event.api.getSelectedRows()
        const currRow = selectedRows[0];
        this.setState({currRow});
        if(currRow && currRow.id){
        this.props.getHubDetails({id:currRow.id});
        this.setState({clearBottomPane:false});
        }
    }
    componentDidMount(){
        document.addEventListener("click",this.handleClickOutside,true);
    }

    handleClickOutside =(e) =>{
        const {target}=e;
        if(!this.refOne.current.contains(e.target) && this.state.mode!=='EDIT_HUBEDITOR' && this.state.mode!=='ShowWarning' && this.state.mode!=='REMOVE_HUBEDITOR'){
            if(this.state.isUpdated  && !this.state.addHub){
                const nativeMouseEvent=new Event(e.type,e);
                this.setState({clonedEvent:{target,nativeMouseEvent}});
                e.stopPropagation();
                 this.setState({mode:"ShowWarning"});          
                 return;
             }
             if(this.state.addHub){
                this.setState({addHub:false});
            }
        }
       
    }
    componentWillUnmount() {
        document.removeEventListener("click",this.handleClickOutside,true);
    }

    addHubEditor = () =>{
        if(this.state.isUpdated  && !this.state.addHub ){
          this.setState({mode:"ShowWarning"});            
            return;
        }
        if(this.state.addHub){
            this.setState({addHub:false});
        } 
       
        const mode = `ADD_HUBEDITOR`
        this.setState({mode});
    }
    editHubEditor = () =>{
        const mode = `EDIT_HUBEDITOR`;
        const currData = this.props.hubEditor.data;
        !this.state.hasOwnProperty("currData") || this.state.currData === undefined ? this.setState({ mode, currData }) : this.setState({ mode })
        this.setState({mode});
    }
    removeHubEditor = () => {
        const mode = `REMOVE_HUBEDITOR`
        const { currRow = {} } = this.state;
        const currData = this.props.hubEditor.data;
        const {detailData}=this.props.hubEditor;
        const hasPoolsExbs=detailData && detailData.hubName===currRow.hubName && ((detailData.extraboards && detailData.extraboards.length>0) || (detailData.pools && detailData.pools.length>0));
        const { id, hubName } = currRow;
        const feedback = hasPoolsExbs?{ variant: `error`, message: `This hub associated with a Pool or an Extraboard, If you want to delete it, please disassociate the pool or extraboard`,hasPoolsExbs:true }:
        { variant: `danger`, message: `Delete hub with ${id?id:''} ${id?'-':''} ${hubName}`,hasPoolsExbs:false };
        !this.state.hasOwnProperty("currData") || this.state.currData === undefined ? this.setState({ mode, feedback, currData }) : this.setState({ mode, feedback })
    }
    addHubEditorSystem  = (obj) =>{
        const curr = {
            action: `ADD`,
            currRecord: true,
            ...obj,
            planId: this.props.currPlan.planId
        };
       
        this.gridApi.setRowData([curr, ...this.props.hubEditor.data]);
        this.setState({isUpdated: true, currRow: curr,addHub:true},this.changeRowSelection);
        this.setState({clearBottomPane:true});
        
    }
    editHubEditorSystem = (obj) => {
        let { currRow = {}, currData: data = [] } = this.state;
        // const { exbTurns: turnsOrg = [] } = currRow;
        if(currRow && currRow.id){
            const index = data.findIndex(item => item.id.toString() === currRow.id.toString());
            if (index >= 0) {
               obj.currRecord= true;
               obj.action='UPDATE';
                const updatedHub = { ...data[index], ...obj };
                const currData = [
                    ...data.slice(0, index),
                    updatedHub,
                    ...data.slice(index + 1),
                ] 
                this.setState({ isUpdated: true, currData,  currRow: { ...updatedHub, action: `UPDATE` }, mode: ``,addHub:true },this.changeRowSelection)
            }
        }else{
           
            this.addHubEditorSystem(obj);
        }
        
    }
    removeHubEditorSystem  = () => {
        let { currRow = {} } = this.state;
        if(currRow.id){
         this.props.removeHubEditor(currRow);
        this.setState({currRow: undefined, isUpdated: false, mode:``,currData:undefined});
        }else{
            this.reset();
        }

        
    }
    changeRowSelection = params => {
        this.gridApi && this.gridApi.forEachNode((node) => {
            if ((node.data.currRecord === true)) {
                node.setSelected(true)
                node.setSelected(true)
                this.gridApi.ensureIndexVisible(node);
            }
        })
    };
    onHide = () => {
        const mode = ``;
       
        this.setState({mode,errorAlert:false,errorMsg:``,feedback:{}})

       // this.state.isUpdated = false;
    }
    reset = () => {
        this.setState({ currData:[...this.props.hubEditor.data], isUpdated: false, currRow: undefined,mode:`` })
        this.gridApi.setRowData([...this.props.hubEditor.data]);
        this.gridApi.deselectAll();
    }
    onHideHubConfirmSave = () =>{
        const mode = ``
        this.setState({mode,isUpdated:false},()=>{
            this.reset();
            if(this.state.selectedRowEvent && this.state.selectedRowIndex){
            this.state.selectedRowEvent.api.getDisplayedRowAtIndex(this.state.selectedRowIndex).setSelected(true)
            this.handleRowSelection(this.state.selectedRowEvent);
            }
            if(this.state.clonedEvent){
                const {target,nativeMouseEvent}=this.state.clonedEvent;
                target.dispatchEvent(nativeMouseEvent);
                this.setState({clonedEvent:null});
            }
        });
        
    }

    save = () => {
        const {currRow={}} = this.state;
        // currRow?.hubName && delete currRow.hubName;        
         if (currRow.action === "ADD") {           
            this.props.saveHubEditor(currRow);
        } else if (currRow.action === "UPDATE") {
            this.props.updateHubEditor(currRow);
        }else{
            const feedback = { variant: `warning`, message: `Nothing to save...`,nothingToSave:true };

            this.setState({ feedback,errorAlert:true});
        }
        this.setState({currRow: undefined, isUpdated: false, mode:``,currData:undefined});
    }

    
    handleTabChange = (e) => {
        this.setState({ currTab: e })
        this.props.changeHubDetailCurrTab(e);
    }

    render() {
        const {data,tab,detailData} = this.props.hubEditor || {};
        const {isEditEnabled = false} = this.props;
        const {mode, isUpdated, currData = data}  = this.state; 
        var {currRow,clearBottomPane}=this.state;
        if(detailData && !clearBottomPane && currRow){    
        currRow={...currRow,extraboards:detailData.extraboards,pools:detailData.pools}
        }
        return (
            <>
             {mode === `ShowWarning` && 
                    <HubConfirmSave handleClose={this.onHideHubConfirmSave} title={`Do you want to save the changes?`} show={true} mode={mode}
                            handleSuccess={this.save} successLabel={`Yes`}/>
                }
                <div ref={this.refOne} className="modal-container">
                    <Modal.Dialog style={{maxWidth: "100%", margin: "0rem"}}>
                        <Modal.Header className="settings-model-header">
                            <Modal.Title style={{color: 'darkblue'}}> <b>Hub Editor</b></Modal.Title>
                        </Modal.Header>

                        <Modal.Body className={`ccp-form`}>
                        <Row style={{ marginBottom: "1rem" }} className="action-bar">
                                </Row>
                            <Form.Group as={Row}>
                                {data &&
                                <div className="ag-theme-alpine div-33vh" style={{width: "100%"}}>
                                    <AgGridReact
                                        rowSelection='single'
                                        multiSortKey={MULTI_SORT_KEY}
                                        defaultColDef={getDefaultColDef}
                                        columnDefs={HUBEDITOR_COLUMN_DEFS}
                                        rowData={currData}
                                        onGridReady={this.onGridReady}
                                        getRowHeight={getGenericRowHeight}
                                        onRowSelected={(e) => {
                                            this.handleRowSelection(e)
                                        }}
                                        suppressContextMenu={true}
                                        onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                    >
                                    </AgGridReact>
                                </div>
                                }
                            </Form.Group>

                            {data  && data.length !== 0 && <><Row className="action-bar" style={{marginBottom: "1rem"}}>
                                    <ButtonToolbar>
                                        <ButtonGroup className="mr-2">
                                        <Button variant="success" disabled={!isEditEnabled}
                                                onClick={this.addHubEditor}
                                        >Add new hub</Button>
                                        </ButtonGroup>
                                        <ButtonGroup className="mr-2">
                                         <Button variant="danger" disabled={!(isEditEnabled && currRow)}
                                                onClick={this.removeHubEditor}
                                            >Remove hub</Button>
                                        </ButtonGroup>
                                        <ButtonGroup className="mr-2">
                                            <Button variant="primary"  disabled={!(isEditEnabled && currRow )}
                                                onClick={this.editHubEditor}
                                            >Edit hub</Button>
                                        </ButtonGroup>
                                    </ButtonToolbar>
                                    {
                                        mode === "ADD_HUBEDITOR" &&  <HubEditorModal handleClose={this.onHide} title={`Add New Hub`} show={true} mode={mode}
                                        handleSuccess={this.addHubEditorSystem} successLabel={`Add`} hubData={currData}/>
                                    }
                                     {(mode === `REMOVE_HUBEDITOR`||this.state.errorAlert)
                                                && ((this.state.feedback.hasPoolsExbs||this.state.feedback.nothingToSave)?<PopupAlert hide={true} variant={this.state.feedback.variant}
                                                    closeLabel={`Ok`} 
                                                    message={this.state.feedback.message}
                                                    handleClose={this.onHide}
                                                    />:
                                                  <PopupActionAlert hide={true} variant={this.state.feedback.variant}
                                                    successLabel={`Remove`} closeLabel={`Cancel`}
                                                    message={this.state.feedback.message}
                                                    handleSuccess={this.removeHubEditorSystem}
                                                    handleClose={this.onHide} />)}
                                    {
                                        mode === "EDIT_HUBEDITOR" &&  <HubEditorModal handleClose={this.onHide} title={`Edit Hub`} show={true} mode={mode}
                                        handleSuccess={this.editHubEditorSystem} 
                                        id={currRow ? currRow.id : ``}
                                        hubName={currRow.hubName} description={currRow.description} successLabel={`Update`} hubData={currData}/>
                                    }   

                                </Row>
                                {currRow &&
                                <><Row style={{ borderTop: "1px solid #dee2e6" }}>
                                    </Row>
                                <Tabs defaultActiveKey="pools" id="pools-tabs"
                                        activeKey={tab}
                                        onSelect={this.handleTabChange} className="position-sticky"
                                        style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                        <Tab eventKey="pools" title="Pools"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            {currRow.pools &&
                                                <div className="ag-theme-alpine div-33vh" style={{ width: "100%" }}>
                                                    <AgGridReact
                                                        rowSelection='none'
                                                        defaultColDef={getDefaultColDef}
                                                        columnDefs={HUBEDITOR_POOLS_COLUMN_DEFS}
                                                        rowData={currRow.pools}
                                                        onGridReady={this.onHubPoolsGridReady}
                                                        getRowHeight={getGenericRowHeight}
                                                        suppressContextMenu={true}
                                                        onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                       
                                                    >
                                                    </AgGridReact>
                                                </div>
                                            }
                                          </Tab>
                                          <Tab eventKey="extraboards" title="Extraboards"
                                            style={{ marginRight: "-15px", marginLeft: "-15px" }}>
                                            {currRow.extraboards &&
                                                <div className="ag-theme-alpine div-33vh" style={{ width: "100%" }}>
                                                    <AgGridReact
                                                        rowSelection='none'
                                                        defaultColDef={getDefaultColDef}
                                                        columnDefs={HUBEDITOR_EXB_COLUMN_DEFS}
                                                        rowData={currRow.extraboards}
                                                        onGridReady={this.onHubPoolsGridReady}
                                                        getRowHeight={getGenericRowHeight}
                                                        suppressContextMenu={true}
                                                        onFirstDataRendered={(params) => params.columnApi.autoSizeAllColumns()}
                                                       
                                                    >
                                                    </AgGridReact>
                                                </div>
                                            }
                                          </Tab>
                                          </Tabs>
                                </>
                                }
                                <Modal.Footer style={{justifyContent: "center"}}>
                                    <Button variant="success" disabled={!(isEditEnabled && isUpdated)}
                                            onClick={this.save}>Save</Button>
                                    <Button variant="warning" disabled={!(isEditEnabled && isUpdated)}
                                            onClick={this.reset}>Reset</Button>
                                </Modal.Footer>
                                </>
                            }
                           
                        </Modal.Body>
                    </Modal.Dialog>
                </div>
            </>
        );
    }
}

function mapStateToProps(state) {
    const {
        hubEditor,
        currPlan
    } = state.plan || {}
    const {isEditEnabled} = state.app || {}
    return {
        hubEditor,
        currPlan,
        isEditEnabled
    }
}

export const mapDispatchToProps = {
    getHubEditorData,
    saveHubEditor,
    updateHubEditor,
    removeHubEditor,
    getHubDetails,
    changeHubDetailCurrTab
}
export default connect(mapStateToProps, mapDispatchToProps)(HubEditor);
export {HubEditor}