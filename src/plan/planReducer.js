import * as PlanActions from "./planActionTypes"
import * as LoginActionTypes from "../app/appActionTypes";
import { ACTIVE_INACTIVE_TYPES, COPY_PLAN_SUCCESS, CREW_PRO, ID_POOL_TYPES } from "../config/ccpconstants";
import { sortBy, compose, prop } from "ramda";


const initialReports = {
    extraboard: {
        distr: 'ALL',
        subDistr: 'ALL',
        data: undefined,
        detailData: undefined,
        tab: 'turns',
        filters: undefined,
        reportExpired: true,
        distrs: [],
        subDistrs: []
    },
    poolSetup: {
        distr: 'ALL',
        subDistr: 'ALL',
        pool: 'ALL',
        tab: 'terminals',
        data: undefined,
        detailData: undefined,
        reportExpired: true,
        filters: undefined,
        distrs: [],
        subDistrs: [],
        pools: []
    },
    workRestProfile: {
        distr: 'ALL',
        subDistr: 'ALL',
        pool: 'ALL',
        poolType: 'ALL',
        data: undefined,
        detailData: undefined,
        tab: 'turns',
        filters: undefined,
        distrs: [],
        subDistrs: [],
        pools: [],
        reportExpired: true
    },
    tieUpER: {
        distr: 'ALL',
        subDistr: 'ALL',
        data: undefined,
        detailData: undefined,
        searchPoolsData: undefined,
        subRuleId: undefined,
        filter: undefined,
        distrs: [],
        subDistrs: [],
        reportExpired: true
    },
    idPool: {
        distr: 'ALL',
        subDistr: "ALL",
        craft: "ALL",
        data: undefined,
        detailData: undefined,
        filters: undefined,
        distrs: [],
        subDistrs: [],
        reportExpired: true
    },
    cardedJob: {
        distr: 'ALL',
        subDistr: "ALL",
        pool: "ALL",
        filters: undefined,
        distrs: [],
        subDistrs: [],
        pools: [],
        data: undefined,
        detailData: undefined,
        reportExpired: true,
        currRow: undefined
    },
    crewProfile: {
        distr: 'ALL',
        subDistr: "ALL",
        pool: "ALL",
        filters: undefined,
        distrs: [],
        subDistrs: [],
        pools: [],
        reportExpired: true,
    },
    hubEditor:{
        data:undefined
    },
    
}
const initialState = {
    isLoading: false,
    plans: [],
    masterPlans: [],
    currView: '',
    currPlan: undefined,
    currPlanTreeIndex: undefined,
    ...initialReports,
    idPoolDistricts: undefined,
    feedback: '',
    refreshCounter: 0,
    hidePlanTree: false

}
const sortPlanByNameCaseSensitive = sortBy(compose(prop('name')));

const findYearFromName = (name) => {
    if (name === undefined) return undefined;
    if (name == null) return undefined;
    const sp = name.split("-");
    if (sp.length < 2) return undefined;
    const event = new Date(sp[1]);
    return event.getFullYear();
}

export default function plan(state = initialState, action) {
    switch (action.type) {
        //plan summary
        case PlanActions.GET_PLANS_REQUEST: {
            return {
                ...state, isLoading: true
            }
        }
        //Prepare plan hierarchy from master plans.
        case PlanActions.GET_PLANS_SUCCESS: {
            let plans = [];
            const masterPlans = state.masterPlans;
            const crewProLbl = 'CrewPro Plans';
            plans.push({
                planHierarchy: [crewProLbl],
                view: "crewproplans",
                id: 1,
                planId: `-1`,
                hierarchyName: crewProLbl
            });
            const years = new Set();
            masterPlans.forEach((item, run) => {
                const { name, planType } = item || {}
                const isCrewPro = planType === CREW_PRO;
                if (isCrewPro) {
                    const y = findYearFromName(name);
                    if (y !== undefined)
                        years.add(y);
                }
            });
            let year_index = 2;

            years.forEach((value) => {
                plans.push({
                    planHierarchy: [crewProLbl, value],
                    view: "crewproplans",
                    id: year_index,
                    planId: `-1`,
                    hierarchyName: value
                });
                year_index++;
            });

            const crewProHierarchy = [crewProLbl];
            masterPlans && sortPlanByNameCaseSensitive(masterPlans)
                .forEach((item, run) => {
                    const index = run * 100;
                    const { name, desc, status, id, createTs, userId, planType } = item || {}
                    const isCrewPro = planType === CREW_PRO;
                    const itemNameWithIndex = [`${name}${index}`];
                    let itemHierarchy = itemNameWithIndex;
                    if (isCrewPro) {
                        const y = findYearFromName(name);
                        if (y !== undefined) {
                            itemHierarchy = crewProHierarchy
                                .concat(["" + y])
                                .concat(itemNameWithIndex)
                        }
                    }
                    item.status !== 'COMPLETE' ?
                        plans.push(...[
                            {
                                planHierarchy: itemHierarchy,
                                view: "plansummary",
                                id: (index + 1),
                                planId: id,
                                createTs,
                                userId,
                                planName: name,
                                hierarchyName: name,
                                desc,
                                status,
                                planType
                            },
                        ])
                        : plans.push(...[
                            {
                                planHierarchy: itemHierarchy,
                                view: "plansummary",
                                id: (index + 1),
                                planId: id,
                                createTs,
                                userId,
                                planName: name,
                                hierarchyName: name,
                                desc,
                                status,
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Copy Plan'] : [`${name}${index}`, 'Copy Plan'],
                                planHierarchy: itemHierarchy.concat(['Copy Plan']),
                                view: "copyplan",
                                id: (index + 2),
                                planId: id,
                                desc,
                                planName: name,
                                hierarchyName: 'Copy Plan',
                                planType
                            },
                            // {
                            //     //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Assignment Profiles'] : [`${name}${index}`, 'Assignment Profiles'],
                            //     planHierarchy: itemHierarchy.concat(['Assignment Profiles']),
                            //     view: "assignmentprofiles",
                            //     id: (index + 3),
                            //     planId: id,
                            //     planName: name,
                            //     hierarchyName: 'Assignment Profiles',
                            //     planType
                            // },
                            
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Carded Jobs'] : [`${name}${index}`, 'Carded Jobs'],
                                planHierarchy: itemHierarchy.concat(['Carded Jobs']),
                                view: "cardedjobs",
                                id: (index + 4),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Carded Jobs',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Crew Profiles'] : [`${name}${index}`, 'Crew Profiles'],
                                planHierarchy: itemHierarchy.concat(['Crew Profiles']),
                                view: "crewprofiles",
                                id: (index + 5),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Crew Profiles',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Extra Board'] : [`${name}${index}`, 'Extra Board'],
                                planHierarchy: itemHierarchy.concat(['Extra Board']),
                                view: "extraboard",
                                id: (index + 6),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Extra Board',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Assignment Profiles'] : [`${name}${index}`, 'Assignment Profiles'],
                                planHierarchy: itemHierarchy.concat(['Hub Editor']),
                                view: "hubeditor",
                                id: (index + 3),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Hub Editor',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'ID Pools'] : [`${name}${index}`, 'ID Pools'],
                                planHierarchy: itemHierarchy.concat(['ID Pools']),
                                view: "idpools",
                                id: (index + 7),
                                planId: id,
                                planName: name,
                                hierarchyName: 'ID Pools',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Pool Setup'] : [`${name}${index}`, 'Pool Setup'],
                                planHierarchy: itemHierarchy.concat(['Pool Setup']),
                                view: "poolsetup",
                                id: (index + 8),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Pool Setup',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Tie-Up Exception Rules'] : [`${name}${index}`, 'Tie-Up Exception Rules'],
                                planHierarchy: itemHierarchy.concat(['Tie-Up Exception Rules']),
                                view: "tieupexceptionrules",
                                id: (index + 9),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Tie-Up Exception Rules',
                                planType
                            },
                            {
                                //planHierarchy: isCrewPro ? [crewProLbl, `${name}${index}`, 'Work Rest Profiles'] : [`${name}${index}`, 'Work Rest Profiles'],
                                planHierarchy: itemHierarchy.concat(['Work Rest Profiles']),
                                view: "workrestprofiles",
                                id: (index + 10),
                                planId: id,
                                planName: name,
                                hierarchyName: 'Work Rest Profiles',
                                planType
                            },
                        ])
                })
            return {
                ...state,
                refreshCounter: ++state.refreshCounter,
                plans,
                isLoading: false
            }
        }
        case PlanActions.GET_MASTER_PLANS_SUCCESS: {
            let masterPlans = action.payload;
            return {
                ...state,
                masterPlans,
            }
        }
        case PlanActions.TOGGLE_PLAN_TREE: {
            const { hidePlanTree } = action.payload;
            return {
                ...state,
                hidePlanTree,
            }
        }
        case PlanActions.DELETE_PLAN_SUCCESS: {
            let newPlans = state.plans
            newPlans = newPlans.filter(item => item.planId !== action.payload)
            return {
                ...state,
                feedback: "DELETE_SUCCESS",
                plans: [...newPlans],
                currView: '',
                currPlan: undefined
            }
        }
        case PlanActions.UPDATE_PLAN_SUCCESS: {
            let newPlans = state.plans
            const name = action.payload.name;
            let currPlan = { ...state.currPlan, name: action.payload.name, desc: action.payload.desc }
            newPlans.forEach(item => {
                if (item.planId === action.payload.planId) {
                    item.planHierarchy.splice(0, 1, name)
                }
            })
            let index = newPlans.findIndex(item => item.planId === action.payload.planId && item.view === "plansummary")
            newPlans[index] = { ...newPlans[index], name: action.payload.name }
            return {
                ...state,
                currPlan,
                feedback: "UPDATE_SUCCESS",
                update: 2
            }
        }

        case PlanActions.COPY_PLAN_SUCCESS: {
            return {
                ...state,
                feedback: { variant: "success", key: "COPY_PLAN_SUCCESS", message: COPY_PLAN_SUCCESS }
            }
        }
        case PlanActions.COPY_PLAN_FAIL: {
            return {
                ...state,
                feedback: { show: true, variant: 'danger', message: 'Failed to copy Plan' }
            }
        }
        case PlanActions.REFRESH_PLANS_FAILURE: {
            return {
                ...state,
                feedback: { show: true, variant: 'danger', message: 'Failed to refresh plans' }
            }
        }
        case PlanActions.CHANGE_CURRENT_PLAN_VIEW: {
            const { currPlan } = action.payload;
            let clearReports = currPlan && state.currPlan ? currPlan.id !== state.currPlan.id : true
            return clearReports ? {
                ...state,
                feedback: '',
                currPlan,
                ...initialReports
            } : {
                ...state,
                currPlan,
            }
        }


        //Assignment Profiles
        case PlanActions.GET_ASSIGNMENT_PROFILES_SUCCESS: {
            let data = action.payload;
            return {
                ...state, assignmentProfilesData: data, isLoading: false
            }
        }

        //ID Pools
        case PlanActions.GET_ID_POOLS_SUCCESS: {
            const items = action.payload || [];
            const data = items.map(item => {
                const { idivPoolType = ``, idivIdDropFlag = `I` } = item;
                const idivIdDropFlagL = ACTIVE_INACTIVE_TYPES.find(item => item.key === idivIdDropFlag)
                const { desc: idivIdDropFlagDesc } = idivIdDropFlagL || {}
                const idivPoolTypeL = ID_POOL_TYPES.find(item => item.key === idivPoolType);
                const { desc: idivPoolTypeDesc } = idivPoolTypeL || {}
                return { ...item, idivIdDropFlagDesc, idivPoolTypeDesc }
            });
            return {
                ...state, idPool: { ...state.idPool, data, reportExpired: false, detailData: undefined }
            }
        }
        case PlanActions.CHANGE_ID_POOL_DISTRICT: {
            const data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.idPool.filters.distrs.find(d => d.distr === data).subDistrs.sort();
            return {
                ...state,
                idPool: { ...state.idPool, distr: data, subDistrs, subDistr: 'ALL', reportExpired: true }
            }
        }
        case PlanActions.CHANGE_ID_POOL_DETAIL_DATA: {
            return {
                ...state,
                idPool: { ...state.idPool, detailData: action.payload }
            }
        }

        case PlanActions.CHANGE_ID_POOL_SUB_DISTRICT: {
            return {
                ...state,
                idPool: { ...state.idPool, subDistr: action.payload, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_ID_POOL_CRAFT: {
            return {
                ...state,
                idPool: { ...state.idPool, craft: action.payload, reportExpired: true }
            }
        }
        case PlanActions.GET_ID_POOL_DISTRICTS_SUCCESS: {
            let data = action.payload;
            return {
                ...state, idPoolDistricts: data, isLoading: false
            }
        }

        //Crew Profiles
        case PlanActions.GET_CREW_PROFILES_SUCCESS: {
            let data = action.payload;
           
            return {
                ...state, crewProfile: { ...state.crewProfile, data, reportExpired: false, detailData: undefined,isLoading:false }
            }
        }
        case PlanActions.CHANGE_CREW_PROFILES_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.crewProfile.filters.distrs.find(d => d.distr === data).subDistrs.map(sd => sd.subDistr).sort();
            return {
                ...state,
                crewProfile: {
                    ...state.crewProfile,
                    distr: data,
                    subDistrs,
                    subDistr: 'ALL',
                    pool: 'ALL',
                    pools: [],
                    reportExpired: true
                }
            }
        }
        case PlanActions.CHANGE_CREW_PROFILES_SUB_DISTRICT: {
            let data = action.payload;
            const { distr: currDistr } = state.crewProfile || {};
            let pools = data === 'ALL' ? [] : state.crewProfile.filters.distrs.find(d => d.distr === currDistr).subDistrs.find(sd => sd.subDistr === data).poolNames.sort();
            return {
                ...state, crewProfile: { ...state.crewProfile, subDistr: data, pools, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_CREW_PROFILES_POOL: {
            let data = action.payload;
            return {
                ...state, crewProfile: { ...state.crewProfile, pool: data, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_CREW_PROFILES_REPORT_EXPIRED: {
            let data = action.payload;
            return {
                ...state, crewProfile: { ...state.crewProfile, reportExpired: data }
            }
        }

        //Carded Jobs
        case PlanActions.GET_CARDED_JOBS_SUCCESS: {
            const data = action.payload;
            return {
                ...state, cardedJob: { ...state.cardedJob, data, reportExpired: false, currRow: undefined }
            }
        }
        case PlanActions.GET_CARDED_JOB_DETAILS_SUCCESS: {
            const detailData = action.payload;
            return {
                ...state, cardedJob: { ...state.cardedJob, detailData }
            }
        }
        case PlanActions.CHANGE_CARDED_JOB_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.cardedJob.filters.distrs.find(d => d.distr === data).subDistrs.map(sd => sd.subDistr).sort();
            return {
                ...state,
                cardedJob: {
                    ...state.cardedJob,
                    distr: data,
                    subDistrs,
                    subDistr: 'ALL',
                    pool: 'ALL',
                    pools: [],
                    reportExpired: true
                }
            }
        }
        case PlanActions.CHANGE_CARDED_JOB_SUB_DISTRICT: {
            let data = action.payload;
            const { distr: currDistr } = state.cardedJob || {};
            let pools = data === 'ALL' ? [] : state.cardedJob.filters.distrs.find(d => d.distr === currDistr).subDistrs.find(sd => sd.subDistr === data).poolNames.sort();
            return {
                ...state, cardedJob: { ...state.cardedJob, subDistr: data, pools, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_CARDED_JOB_POOL: {
            let data = action.payload;
            return {
                ...state, cardedJob: { ...state.cardedJob, pool: data, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_CARDED_JOB_CURR_ROW: {
            const data = action.payload;
            return {
                ...state, cardedJob: { ...state.cardedJob, currRow: data }
            }
        }
        case PlanActions.CHANGE_CARDED_JOB_REPORT_EXPIRED: {
            let data = action.payload;
            return {
                ...state, cardedJob: { ...state.cardedJob, reportExpired: data }
            }
        }

        case PlanActions.GET_CARDED_JOB_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, cardedJob: { ...state.cardedJob, filters: data, distrs }
            }
        }
        case PlanActions.GET_CREW_PROFILE_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, crewProfile: { ...state.crewProfile, filters: data, distrs }
            }
        }
        case PlanActions.GET_CREW_PROFILE_DETAILS: {
            
            let data=action.payload;
            return {
               ...state, crewProfile: { ...state.crewProfile, detailData:data}
            }
        }
        case PlanActions.GET_ID_POOLS_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, idPool: { ...state.idPool, filters: data, distrs }
            }
        }
        case PlanActions.GET_POOL_SETUPS_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, poolSetup: { ...state.poolSetup, filters: data, distrs }
            }
        }
        case PlanActions.GET_TIE_UP_EXCEPTION_RULES_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, tieUpER: { ...state.tieUpER, filters: data, distrs }
            }
        }
        case PlanActions.GET_WORK_REST_PROFILE_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();
            return {
                ...state, workRestProfile: { ...state.workRestProfile, filters: data, distrs }
            }
        }
        //Extraboard
        case PlanActions.GET_EXTRA_BOARD_SUCCESS: {
            let data = action.payload;
            data = data?.map((pool) => {
                let data1 = state.hubCodes.filter((item) => {
                    return pool.hubId === item.id
                })
                pool.hubName = data1[0]?.name || "";
                return pool
            });
            return {
                ...state, extraboard: { ...state.extraboard, data, reportExpired: false, detailData: undefined }
            }
        }
        case PlanActions.GET_EXTRA_BOARD_FILTERS_SUCCESS: {
            let data = action.payload;
            let distrs = data.distrs.map(d => d.distr).sort();

            return {
                ...state, extraboard: { ...state.extraboard, filters: data, distrs }
            }
        }
        case PlanActions.GET_EXTRA_BOARD_DETAILS_SUCCESS: {
            let data = action.payload;
            return {
                ...state, extraboard: { ...state.extraboard, detailData: data }
            }
        }
        // case PlanActions.GET_EXTRA_BOARD_TURNS_SUCCESS: {
        //     let data = action.payload;
        //     return {
        //         ...state, extraboardTurnsData: data, isLoading: false
        //     }
        // }
        // case PlanActions.GET_EXTRA_BOARD_SUPPORTED_POOLS_SUCCESS: {
        //     let data = action.payload;
        //     return {
        //         ...state, extraboardSupportedPoolsData: data, isLoading: false
        //     }
        // }
        case PlanActions.CHANGE_EXTRABOARD_DETAIL_TAB: {
            let data = action.payload;
            return {
                ...state, extraboard: { ...state.extraboard, tab: data }
            }
        }
        case PlanActions.CHANGE_HUB_DETAIL_TAB: {
            let data = action.payload;
            return {
                ...state, hubEditor: { ...state.hubEditor, tab: data }
            }
        }
        case PlanActions.CHANGE_EXTRABOARD_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? undefined : state.extraboard.filters.distrs.find(d => d.distr === data).subDistrs.sort();
            return {
                ...state,
                extraboard: { ...state.extraboard, distr: data, subDistrs, subDistr: 'ALL', reportExpired: true }
            }
        }
        case PlanActions.CHANGE_EXTRABOARD_SUB_DISTRICT: {
            let data = action.payload;
            return {
                ...state, extraboard: { ...state.extraboard, subDistr: data, reportExpired: true }
            }
        }

        case PlanActions.CLEAR_EXTRABOARD_DETAIL_DATA: {
            return {
                ...state, extraboard: { ...state.extraboard, detailData: undefined }
            }
        }
        //Pool Setup
        case PlanActions.GET_POOL_SETUP_SUCCESS: {
            let data = action.payload;
            data = data?.map((pool) => {
                let data1 = state.hubCodes.filter((item) => {
                    return pool.hubId === item.id
                })
                pool.hubName = data1[0]?.name || "";
                return pool
            });
            return {
                ...state, poolSetup: { ...state.poolSetup, data, reportExpired: false, detailData: undefined }
            }
        }
        case PlanActions.GET_POOL_SETUP_DETAIL_SUCCESS: {
            let data = action.payload;
            return {
                ...state, poolSetup: { ...state.poolSetup, detailData: data }
            }
        }
        // case PlanActions.GET_POOL_SETUP_TERMINALS_SUCCESS: {
        //     let data = action.payload;
        //     return {
        //         ...state, poolSetupTerminalsData: data, isLoading: false
        //     }
        // }
        // case PlanActions.GET_POOL_SETUP_OFF_DUTY_RULES_SUCCESS: {
        //     let data = action.payload;
        //     return {
        //         ...state, poolSetupOffDutyRulesData: data, isLoading: false
        //     }
        // }
        // case PlanActions.GET_POOL_SETUP_TURNS_SUCCESS: {
        //     let data = action.payload;
        //     return {
        //         ...state, poolSetupTurnsData: data, isLoading: false
        //     }
        // }
        case PlanActions.CHANGE_POOL_SETUP_DETAIL_TAB: {
            let data = action.payload;
            return {
                ...state, poolSetup: { ...state.poolSetup, tab: data }
            }
        }
        case PlanActions.CHANGE_POOL_SETUP_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.poolSetup.filters.distrs.find(d => d.distr === data).subDistrs.map(sd => sd.subDistr).sort();
            return {
                ...state,
                poolSetup: {
                    ...state.poolSetup,
                    distr: data,
                    subDistrs,
                    subDistr: 'ALL',
                    pool: 'ALL',
                    pools: [],
                    reportExpired: true
                }
            }
        }
        case PlanActions.CHANGE_POOL_SETUP_SUB_DISTRICT: {
            let data = action.payload;
            const { distr: currDistr } = state.poolSetup || {};
            let pools = data === 'ALL' ? [] : state.poolSetup.filters.distrs.find(d => d.distr === currDistr).subDistrs.find(sd => sd.subDistr === data).poolNames.sort();
            return {
                ...state, poolSetup: { ...state.poolSetup, subDistr: data, pools, pool: 'ALL', reportExpired: true }
            }
        }
        case PlanActions.CHANGE_POOL_SETUP_POOL: {
            let data = action.payload;
            return {
                ...state, poolSetup: { ...state.poolSetup, pool: data, reportExpired: true }
            }
        }
        case PlanActions.CLEAR_POOL_SETUP_DETAIL_DATA: {
            return {
                ...state, poolSetup: { ...state.poolSetup, detailData: undefined }
            }
        }
        //Tie Up Exception Rules
        case PlanActions.GET_TIE_UP_EXCEPTION_RULES_SUCCESS: {
            let data = action.payload;
            return {
                ...state,
                tieUpER: {
                    ...state.tieUpER,
                    data,
                    reportExpired: false,
                    detailData: undefined,
                    searchPoolsData: undefined,
                    subRuleId: undefined
                }
            }
        }
        case PlanActions.GET_TIE_UP_EXCEPTION_RULE_DETAILS_SUCCESS: {
            let data = action.payload;
            return {
                ...state,
                tieUpER: { ...state.tieUpER, detailData: data, searchPoolsData: undefined, subRuleId: undefined }
            }
        }
        case PlanActions.CHANGE_TIE_UP_EXCEPTION_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.tieUpER.filters.distrs.find(d => d.distr === data).subDistrs.sort();
            return {
                ...state, tieUpER: { ...state.tieUpER, distr: data, subDistrs, subDistr: 'ALL', reportExpired: true }
            }
        }
        case PlanActions.CHANGE_TIE_UP_EXCEPTION_SUB_DISTRICT: {
            let data = action.payload;
            return {
                ...state, tieUpER: { ...state.tieUpER, subDistr: data, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_TIE_UP_EXCEPTION_SUB_RULE: {
            const subRule = action.payload;
            const { searchPools = [], id: subRuleId } = subRule || {};
            return {
                ...state, tieUpER: { ...state.tieUpER, searchPoolsData: searchPools, subRuleId }
            }
        }

        /*case PlanActions.GET_TIE_UP_ER_SEARCH_POOLS_SUCCESS: {
            let data = action.payload;
            return {
                ...state, tieUpERSearchPoolsData: data, isLoading: false
            }
        }
        case PlanActions.GET_TIE_UP_ER_SUBRULES_SUCCESS: {
            let data = action.payload;
            return {
                ...state, tieUpERSubrulesData: data, isLoading: false
            }
        }*/
        //Work Rest Profiles
        case PlanActions.GET_WORK_REST_PROFILES_SUCCESS: {
            let data = action.payload;
            return {
                ...state, workRestProfile: { ...state.workRestProfile, data, reportExpired: false, detailData: false }
            }
        }
        case PlanActions.GET_WORK_REST_PROFILE_DETAILS_SUCCESS: {
            let data = action.payload;
            return {
                ...state, workRestProfile: { ...state.workRestProfile, detailData: data }
            }
        }

        case PlanActions.CHANGE_WORK_REST_PROFILE_TAB: {
            let data = action.payload;
            return {
                ...state, workRestProfile: { ...state.workRestProfile, tab: data }
            }
        }
        case PlanActions.CHANGE_WORK_REST_PROFILE_DISTRICT: {
            let data = action.payload;
            let subDistrs = data === 'ALL' ? [] : state.workRestProfile.filters.distrs.find(d => d.distr === data).subDistrs.map(sd => sd.subDistr).sort();
            return {
                ...state,
                workRestProfile: {
                    ...state.workRestProfile,
                    distr: data,
                    subDistrs,
                    subDistr: 'ALL',
                    pool: 'ALL',
                    pools: [],
                    reportExpired: true
                }
            }
        }
        case PlanActions.CHANGE_WORK_REST_PROFILE_SUB_DISTRICT: {
            let data = action.payload;
            const { distr: currDistr } = state.workRestProfile || {};
            let pools = data === 'ALL' ? [] : state.workRestProfile.filters.distrs.find(d => d.distr === currDistr).subDistrs.find(sd => sd.subDistr === data).poolNames.sort();
            return {
                ...state,
                workRestProfile: { ...state.workRestProfile, subDistr: data, pool: 'ALL', pools, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_WORK_REST_PROFILE_POOL: {
            let data = action.payload;
            return {
                ...state, workRestProfile: { ...state.workRestProfile, pool: data, reportExpired: true }
            }
        }
        case PlanActions.CHANGE_WORK_REST_PROFILE_POOL_TYPE: {
            let data = action.payload;
            return {
                ...state, workRestProfile: { ...state.workRestProfile, poolType: data, reportExpired: true }
            }
        }
        case PlanActions.PLAN_CLEAR_FEEDBACK: {
            return {
                ...state, feedback: undefined
            }
        }

        /* case PlanActions.IS_LOADED: {
             let data = action.payload;
             return {
                 ...state,isLoading: false
             }
         }*/
        case PlanActions.DELETE_PLAN_REQUEST:
        case PlanActions.UPDATE_PLAN_REQUEST:
        case PlanActions.GET_MASTER_PLANS_REQUEST:
        case PlanActions.GET_WORK_REST_PROFILES_REQUEST:
        case PlanActions.GET_TIE_UP_EXCEPTION_RULES_REQUEST:
        case PlanActions.GET_POOL_SETUP_REQUEST:
        case PlanActions.GET_CARDED_JOB_DETAILS_REQUEST:
        case PlanActions.GET_CARDED_JOBS_REQUEST:
        case PlanActions.GET_CREW_PROFILES_REQUEST:
        case PlanActions.GET_ASSIGNMENT_PROFILES_REQUEST:
        case PlanActions.GET_ID_POOL_DISTRICTS_REQUEST:
        case PlanActions.GET_ID_POOLS_REQUEST: {
            return {
                ...state, isLoading: action.payload
            }
        }

        case LoginActionTypes.LOGOUT_SUCCESS: {
            return {
                ...initialState
            }
        }
        case PlanActions.GET_OSCODES_SUCCESS: {
            let data = action.payload;
            let codes = [];
            data.map((item, index) => {
                codes.push({ label: item });
            })
            return {
                ...state,  osCodes: codes ? sortPlanByNameCaseSensitive(codes) : []
            }
        }
        case PlanActions.GET_POOLHUBCODES_SUCCESS: {
            let data = action.payload;
            let codes = [];
            data.map((item, index) => {
                codes.push({ label: `${item.id}-${item.hubName}`, id: item.id, name: item.hubName });
            })
            return {
                ...state, hubCodes: codes ? sortPlanByNameCaseSensitive(codes) : []
            }
        }
        case PlanActions.GET_HUBDATA_SUCCESS: {
            let data = action.payload;
            return {
                ...state, hubEditor: {...state.hubEditor,data:data || []}
            }
        }
        case PlanActions.GET_HUB_DETAILS_SUCCESS: {
            let data = action.payload;
            return {
                ...state, hubEditor: { ...state.hubEditor, detailData: data }
            }
        }

       
        default :
            return state
    }
}
