import * as ccpconstants from "../../../config/ccpconstants";

describe('CCP Constants Test Cases', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });


  it('Testing isInGroup method of STUDY_ALGORITHM_GROUPS', () => {
    const studyGrps={groups:[{id:1},{id:2}]}
    const reqObj=ccpconstants.STUDY_ALGORITHM_GROUPS;
    const respObj=reqObj.isInGroup(studyGrps,{id:1})
    expect(respObj).toEqual(true);

  });
  it('Testing isInGroup method of STUDY_ALGORITHM_GROUPS', () => {
    const studyGrps={groups:[{id:1},{id:2}]}
    const reqObj=ccpconstants.STUDY_ALGORITHM_GROUPS;
    const respObj=reqObj.isInGroup(studyGrps,{id:3})
    expect(respObj).toEqual(false);

  });
  it('Testing STUDY_OPD_PLAN_SELECTOR_COLDEFS', () => {
    const reqObj=ccpconstants.STUDY_OPD_PLAN_SELECTOR_COLDEFS;
    const respObj=reqObj[2].valueFormatter({value:'2023-05-16T12:34:56.789Z'})
    expect(respObj).toEqual('05/16/2023');
    const respObj1=reqObj[3].valueFormatter({value:'2023-05-15T12:34:56.789Z'})
    expect(respObj1).toEqual('05/15/2023');
    const respObj2=reqObj[8].valueFormatter({value:'2023-05-16T12:34:56.789Z'})
    expect(respObj2).toEqual('05/16/2023');
    const respObj3=reqObj[9].valueFormatter({value:'2023-05-16T12:34:56.789Z'})
    expect(respObj3).toEqual('05/16/2023');
    const respObj4=reqObj[10].valueFormatter({value:'2023-05-16T12:34:56.789Z'})
    expect(respObj4).toEqual('05/16/2023');
    const respObj5=reqObj[11].valueFormatter({value:'2023-05-16T12:34:56.789Z'})
    expect(respObj5).toEqual('05/16/2023');

  });
  it('Testing STUDY_OPD_SCENARIO_SELECTOR_COLUMNS_DEFS', () => {
    const reqObj=ccpconstants.STUDY_OPD_SCENARIO_SELECTOR_COLUMNS_DEFS;
    const respObj=reqObj[2].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023');
    const respObj1=reqObj[3].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023');
    const respObj2=reqObj[8].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023');
    const respObj3=reqObj[9].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj3).toEqual('04/16/2023');
    const respObj4=reqObj[10].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj4).toEqual('04/16/2023');
    const respObj5=reqObj[11].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj5).toEqual('04/16/2023');

  });
  it('Testing STUDY_OPD_PLAN_SELECTOR_COLUMNS_DEFS', () => {
    const reqObj=ccpconstants.STUDY_OPD_PLAN_SELECTOR_COLUMNS_DEFS;
    const respObj=reqObj[3].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('2023-04-16T12:34:56');
    const respObj1=reqObj[4].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023');
    const respObj2=reqObj[5].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023');

  });
  it('Testing TRAIN_VIEWER_DETAIL_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.TRAIN_VIEWER_DETAIL_COLUMN_DEFS;
    const respObj=reqObj[0].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023');
    const respObj1=reqObj[4].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023 08:34:56 AM');
    const respObj2=reqObj[5].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023 08:34:56 AM');
    const respObj3=reqObj[9].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj3).toEqual('04/16/2023');
    const respObj4=reqObj[10].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj4).toEqual('04/16/2023');

  });
  it('Testing TRAIN_VIEWER_DETAIL_EXPAND_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.TRAIN_VIEWER_DETAIL_EXPAND_COLUMN_DEFS;
    const respObj=reqObj[1].valueFormatter('2023-04-16T12:34:56.789Z')
    expect(respObj).toEqual('04/16/2023 08:34:56 AM');
    const respObj1=reqObj[2].valueFormatter('2023-04-16T12:34:56.789Z')
    expect(respObj1).toEqual('04/16/2023 08:34:56 AM');
    const respObj2=reqObj[13].valueFormatter('2023-04-16T12:34:56.789Z')
    expect(respObj2).toEqual('04/16/2023');
    const respObj3=reqObj[40].valueFormatter('2023-04-16T12:34:56.789Z')
    expect(respObj3).toEqual('04/16/2023');
    const respObj4=reqObj[41].valueFormatter('2023-04-16T12:34:56.789Z')
    expect(respObj4).toEqual('04/16/2023');

  });
  it('Testing SCENARIO_COMPARISON_SCENARIO_COLDEFS', () => {
    const reqObj=ccpconstants.SCENARIO_COMPARISON_SCENARIO_COLDEFS;
    const respObj=reqObj[5].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023');
    const respObj1=reqObj[6].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023');
    const respObj2=reqObj[7].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023 08:34:56 AM');
    
  });
  
  it('Testing RUN_SIMULATION_PLAN_COLDEFS', () => {
    const reqObj=ccpconstants.RUN_SIMULATION_PLAN_COLDEFS;
    const respObj=reqObj[4].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023 08:34:56 AM');

  });
  it('Testing RUN_SIMULATION_STUDY_COLDEFS', () => {
    const reqObj=ccpconstants.RUN_SIMULATION_STUDY_COLDEFS;
    const respObj=reqObj[3].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023');
    const respObj1=reqObj[4].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023');
    const respObj2=reqObj[6].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023 08:34:56 AM');
    
  });
  it('Testing SIMULATION_TRAIN_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.SIMULATION_TRAIN_COLUMN_DEFS;
    const respObj=reqObj[2].valueFormatter({value:'2023-04-16T12:34:56.789Z'});
    expect(respObj).toEqual('04/16/2023');
    expect(reqObj[2].valueGetter({data:{trnOrgnDt:'2023-04-16T12:34:56.789Z'}})).toEqual('2023-04-16T12:34:56');
    const respObj1=reqObj[8].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023 12:34:56 PM');
    const respObj2=reqObj[9].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj2).toEqual('04/16/2023 12:34:56 PM');
    expect(reqObj[8].valueGetter({data:{fromTs:'2023-04-16T12:34:56.789Z'}})).toEqual('2023-04-16T08:34:56');
    expect(reqObj[9].valueGetter({data:{toTs:'2023-04-16T12:34:56.789Z'}})).toEqual('2023-04-16T08:34:56');

  });

  it('Testing HIRE_GROUP_DETAIL_COLUMN_DEFS_EDIT', () => {
    const reqObj=ccpconstants.HIRE_GROUP_DETAIL_COLUMN_DEFS_EDIT;
    const respObj=reqObj[1].valueFormatter({value:22.888})
    expect(respObj).toEqual('2288.80%');
    const respObj2=reqObj[1].valueFormatter('')
    expect(respObj2).toEqual('');
    const respObj1=reqObj[3].valueFormatter('1.1234')
    expect(respObj1).toEqual('');
    const respObj3=reqObj[3].valueFormatter({value:1.123})
    expect(respObj3).toEqual('112.30%');

  });
  it('Testing HIRE_GROUP_DETAIL_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.HIRE_GROUP_DETAIL_COLUMN_DEFS;
    const respObj=reqObj[1].valueFormatter({value:22.888})
    expect(respObj).toEqual('2288.80%');
    const respObj2=reqObj[1].valueFormatter('')
    expect(respObj2).toEqual('');
    const respObj1=reqObj[3].valueFormatter('1.1234')
    expect(respObj1).toEqual('');
    const respObj3=reqObj[3].valueFormatter({value:1.123})
    expect(respObj3).toEqual('112.30%');
    
  });
  it('Testing LOOKAHEAD_RUN_SUMMARY_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.LOOKAHEAD_RUN_SUMMARY_COLUMN_DEFS;
    const respObj=reqObj[7].valueFormatter({value:22.888})
    expect(respObj).toEqual('2288.80%');
    const respObj2=reqObj[7].valueFormatter('')
    expect(respObj2).toEqual('');
    const respObj1=reqObj[9].valueFormatter('1.1234')
    expect(respObj1).toEqual('');
    const respObj3=reqObj[9].valueFormatter({value:1.123})
    expect(respObj3).toEqual('1.12');
    
  });
  it('Testing HIRE_GROUP_DETAIL_FILE_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.HIRE_GROUP_DETAIL_FILE_COLUMN_DEFS;
    const respObj=reqObj[1].valueFormatter({value:22.888})
    expect(respObj).toEqual('2288.80%');
    const respObj2=reqObj[1].valueFormatter('')
    expect(respObj2).toEqual('');
    const respObj1=reqObj[3].valueFormatter('1.1234')
    expect(respObj1).toEqual('');
    const respObj3=reqObj[3].valueFormatter({value:1.123})
    expect(respObj3).toEqual('112.30%');
    
  });
  it('Testing TE_HEADCOUNT_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.TE_HEADCOUNT_COLUMN_DEFS;
    const respObj=reqObj[0].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj).toEqual('04/16/2023');
    const respObj1=reqObj[7].valueFormatter({value:'2023-04-16T12:34:56.789Z'})
    expect(respObj1).toEqual('04/16/2023');

  });
  it('Testing TRAINEE_INFORMATION_COLUMN_DEFS', () => {
    const reqObj=ccpconstants.TRAINEE_INFORMATION_COLUMN_DEFS;
    reqObj.forEach((item)=>{
      if ( item.cellRenderer) {
        expect(item.cellRenderer({value:'Yes'})).toEqual('Yes');
        expect(item.cellRenderer({})).toEqual('No');
       }
       if ( item.valueFormatter) {
        expect(item.valueFormatter({value:'2023-04-16T12:34:56.789Z'})).toEqual('04/16/2023');
       
       }
    })
    
  });

  it('Testing LOOKAHEAD_RUN_SUMMARY_COLUMN_DEFS', () => {
     const reqObj = ccpconstants.LOOKAHEAD_RUN_SUMMARY_COLUMN_DEFS;
     const percentVal = reqObj.filter(item => item.headerName == 'PERCENT_VAL');
     const respObj1 = percentVal[0].valueFormatter({value: ''})
     expect(respObj1).toEqual('');
     const respObj2 = percentVal[0].valueFormatter({value: 1.0521})
     expect(respObj2).toEqual('105.21%');
     const respObj3 = percentVal[0].valueFormatter({value: 1})
     expect(respObj3).toEqual('100%');

     const ctMarkupRate = reqObj.filter(item => item.headerName == 'CT_MARKUP_RATE');
     const respObj4 = ctMarkupRate[0].valueFormatter({value: ''})
     expect(respObj4).toEqual('');
     const respObj5 = ctMarkupRate[0].valueFormatter({value: 0.6840})
     expect(respObj5).toEqual('68.40%');
     const respObj6 = ctMarkupRate[0].valueFormatter({value: 2})
     expect(respObj6).toEqual('200%');

     const ctMarkupDuration = reqObj.filter(item => item.headerName == 'CT_MARKUP_DURATION');
     const respObj7 = ctMarkupDuration[0].valueFormatter({value: ''})
     expect(respObj7).toEqual('');
     const respObj8 = ctMarkupDuration[0].valueFormatter({value: 16.841})
     expect(respObj8).toEqual('16.84');
     const respObj9 = ctMarkupDuration[0].valueFormatter({value: 125})
     expect(respObj9).toEqual(125);

     const targetHeadcount = reqObj.filter(item => item.headerName == 'TARGET_HEADCOUNT');
     const respObj10 = targetHeadcount[0].valueFormatter({value: ''})
     expect(respObj10).toEqual('');
     const respObj11 = targetHeadcount[0].valueFormatter({value: 16.841})
     expect(respObj11).toEqual('16.84');
     const respObj12 = targetHeadcount[0].valueFormatter({value: 125})
     expect(respObj12).toEqual(125);

     const maxActiveCt = reqObj.filter(item => item.headerName == 'MAX_ACTIVE_CT');
     const respObj13 = maxActiveCt[0].valueFormatter({value: ''})
     expect(respObj13).toEqual('');
     const respObj14 = maxActiveCt[0].valueFormatter({value: 16.841})
     expect(respObj14).toEqual('16.84');
     const respObj15 = maxActiveCt[0].valueFormatter({value: 125})
     expect(respObj15).toEqual(125);

     const maxCtPerMonth = reqObj.filter(item => item.headerName == 'MAX_CT_PER_MONTH');
     const respObj16 = maxCtPerMonth[0].valueFormatter({value: ''})
     expect(respObj16).toEqual('');
     const respObj17 = maxCtPerMonth[0].valueFormatter({value: 16.841})
     expect(respObj17).toEqual('16.84');
     const respObj18 = maxCtPerMonth[0].valueFormatter({value: 125})
     expect(respObj18).toEqual(125);

     const teCount = reqObj.filter(item => item.headerName == 'TE_COUNT');
     const respObj19 = teCount[0].valueFormatter({value: ''})
     expect(respObj19).toEqual('');
     const respObj20 = teCount[0].valueFormatter({value: 16.841})
     expect(respObj20).toEqual('16.84');
     const respObj21 = teCount[0].valueFormatter({value: 125})
     expect(respObj21).toEqual(125);

     
     const goCount = reqObj.filter(item => item.headerName == 'GO_COUNT');
     const respObj22 = goCount[0].valueFormatter({value: ''})
     expect(respObj22).toEqual('');
     const respObj23 = goCount[0].valueFormatter({value: 16.841})
     expect(respObj23).toEqual('16.84');
     const respObj24 = goCount[0].valueFormatter({value: 125})
     expect(respObj24).toEqual(125);

     const tempCount = reqObj.filter(item => item.headerName == 'TEMP_COUNT');
     const respObj25 = tempCount[0].valueFormatter({value: ''})
     expect(respObj25).toEqual('');
     const respObj26 = tempCount[0].valueFormatter({value: 16.841})
     expect(respObj26).toEqual('16.84');
     const respObj27 = tempCount[0].valueFormatter({value: 125})
     expect(respObj27).toEqual(125);

     const effectiveTeCount = reqObj.filter(item => item.headerName == 'EFFECTIVE_TE_COUNT');
     const respObj28 = effectiveTeCount[0].valueFormatter({value: ''})
     expect(respObj28).toEqual('');
     const respObj29 = effectiveTeCount[0].valueFormatter({value: 16.841})
     expect(respObj29).toEqual('16.84');
     const respObj30 = effectiveTeCount[0].valueFormatter({value: 125})
     expect(respObj30).toEqual(125);

     const effectiveTeSep = reqObj.filter(item => item.headerName == 'EFFECTIVE_TE_SEP');
     const respObj31 = effectiveTeSep[0].valueFormatter({value: ''})
     expect(respObj31).toEqual('');
     const respObj32 = effectiveTeSep[0].valueFormatter({value: 16.841})
     expect(respObj32).toEqual('16.84');
     const respObj33 = effectiveTeSep[0].valueFormatter({value: 125})
     expect(respObj33).toEqual(125);

     const teSepCount = reqObj.filter(item => item.headerName == 'TE_SEP_COUNT');
     const respObj34 = teSepCount[0].valueFormatter({value: ''})
     expect(respObj34).toEqual('');
     const respObj35 = teSepCount[0].valueFormatter({value: 16.841})
     expect(respObj35).toEqual('16.84');
     const respObj36 = teSepCount[0].valueFormatter({value: 125})
     expect(respObj36).toEqual(125);

     const lagCumTeSepCount = reqObj.filter(item => item.headerName == 'LAG_CUM_TE_SEP_COUNT');
     const respObj37 = lagCumTeSepCount[0].valueFormatter({value: ''})
     expect(respObj37).toEqual('');
     const respObj38 = lagCumTeSepCount[0].valueFormatter({value: 16.841})
     expect(respObj38).toEqual('16.84');
     const respObj39 = lagCumTeSepCount[0].valueFormatter({value: 125})
     expect(respObj39).toEqual(125);

     const activeCt = reqObj.filter(item => item.headerName == 'ACTIVE_CT');
     const respObj40 = activeCt[0].valueFormatter({value: ''})
     expect(respObj40).toEqual('');
     const respObj41 = activeCt[0].valueFormatter({value: 16.841})
     expect(respObj41).toEqual('16.84');
     const respObj42 = activeCt[0].valueFormatter({value: 125})
     expect(respObj42).toEqual(125);

     const ctRec = reqObj.filter(item => item.headerName == 'CT_REC');
     const respObj43 = ctRec[0].valueFormatter({value: ''})
     expect(respObj43).toEqual('');
     const respObj44 = ctRec[0].valueFormatter({value: 16.841})
     expect(respObj44).toEqual('16.84');
     const respObj45 = ctRec[0].valueFormatter({value: 125})
     expect(respObj45).toEqual(125);

     const loseSpot = reqObj.filter(item => item.headerName == 'LOSE_SPOT');
     const respObj46 = loseSpot[0].valueFormatter({value: ''})
     expect(respObj46).toEqual('');
     const respObj47 = loseSpot[0].valueFormatter({value: 16.841})
     expect(respObj47).toEqual('16.84');
     const respObj48 = loseSpot[0].valueFormatter({value: 125})
     expect(respObj48).toEqual(125);

     const remainingSlot = reqObj.filter(item => item.headerName == 'REMAINING_SLOT');
     const respObj49 = remainingSlot[0].valueFormatter({value: ''})
     expect(respObj49).toEqual('');
     const respObj50 = remainingSlot[0].valueFormatter({value: 16.841})
     expect(respObj50).toEqual('16.84');
     const respObj51 = remainingSlot[0].valueFormatter({value: 125})
     expect(respObj51).toEqual(125);

     const ctGraduate = reqObj.filter(item => item.headerName == 'CT_GRADUATE');
     const respObj52 = ctGraduate[0].valueFormatter({value: ''})
     expect(respObj52).toEqual('');
     const respObj53 = ctGraduate[0].valueFormatter({value: 16.841})
     expect(respObj53).toEqual('16.84');
     const respObj54 = ctGraduate[0].valueFormatter({value: 125})
     expect(respObj54).toEqual(125);

     const cumCtGraduate = reqObj.filter(item => item.headerName == 'CUM_CT_GRADUATE');
     const respObj55 = cumCtGraduate[0].valueFormatter({value: ''})
     expect(respObj55).toEqual('');
     const respObj56 = cumCtGraduate[0].valueFormatter({value: 16.841})
     expect(respObj56).toEqual('16.84');
     const respObj57 = cumCtGraduate[0].valueFormatter({value: 125})
     expect(respObj57).toEqual(125);

     const ctSepCount = reqObj.filter(item => item.headerName == 'CT_SEP_COUNT');
     const respObj58 = ctSepCount[0].valueFormatter({value: ''})
     expect(respObj58).toEqual('');
     const respObj59 = ctSepCount[0].valueFormatter({value: 16.841})
     expect(respObj59).toEqual('16.84');
     const respObj60 = ctSepCount[0].valueFormatter({value: 125})
     expect(respObj60).toEqual(125);

     const lagCtCumSepCount = reqObj.filter(item => item.headerName == 'LAG_CT_CUM_SEP_COUNT');
     const respObj61 = lagCtCumSepCount[0].valueFormatter({value: ''})
     expect(respObj61).toEqual('');
     const respObj62 = lagCtCumSepCount[0].valueFormatter({value: 16.841})
     expect(respObj62).toEqual('16.84');
     const respObj63 = lagCtCumSepCount[0].valueFormatter({value: 125})
     expect(respObj63).toEqual(125);

     const effectiveTeCountCtSep = reqObj.filter(item => item.headerName == 'EFFECTIVE_TE_COUNT_CT_SEP');
     const respObj64 = effectiveTeCountCtSep[0].valueFormatter({value: ''})
     expect(respObj64).toEqual('');
     const respObj65 = effectiveTeCountCtSep[0].valueFormatter({value: 16.841})
     expect(respObj65).toEqual('16.84');
     const respObj66 = effectiveTeCountCtSep[0].valueFormatter({value: 125})
     expect(respObj66).toEqual(125);

     const gap = reqObj.filter(item => item.headerName == 'GAP');
     const respObj67 = gap[0].valueFormatter({value: ''})
     expect(respObj67).toEqual('');
     const respObj68 = gap[0].valueFormatter({value: 16.841})
     expect(respObj68).toEqual('16.84');
     const respObj69 = gap[0].valueFormatter({value: 125})
     expect(respObj69).toEqual(125);

     const noRecActiveCt = reqObj.filter(item => item.headerName == 'NO_REC_ACTIVE_CT');
     const respObj70 = noRecActiveCt[0].valueFormatter({value: ''})
     expect(respObj70).toEqual('');
     const respObj71 = noRecActiveCt[0].valueFormatter({value: 16.841})
     expect(respObj71).toEqual('16.84');
     const respObj72 = noRecActiveCt[0].valueFormatter({value: 125})
     expect(respObj72).toEqual(125);

     const noRecCtGraduate = reqObj.filter(item => item.headerName == 'NO_REC_CT_GRADUATE');
     const respObj73 = noRecCtGraduate[0].valueFormatter({value: ''})
     expect(respObj73).toEqual('');
     const respObj74 = noRecCtGraduate[0].valueFormatter({value: 16.841})
     expect(respObj74).toEqual('16.84');
     const respObj75 = noRecCtGraduate[0].valueFormatter({value: 125})
     expect(respObj75).toEqual(125);

     const noRecCumCtGraduate = reqObj.filter(item => item.headerName == 'NO_REC_CUM_CT_GRADUATE');
     const respObj76 = noRecCumCtGraduate[0].valueFormatter({value: ''})
     expect(respObj76).toEqual('');
     const respObj77 = noRecCumCtGraduate[0].valueFormatter({value: 16.841})
     expect(respObj77).toEqual('16.84');
     const respObj78 = noRecCumCtGraduate[0].valueFormatter({value: 125})
     expect(respObj78).toEqual(125);

     const noRecCtSepCount = reqObj.filter(item => item.headerName == 'NO_REC_CT_SEP_COUNT');
     const respObj79 = noRecCtSepCount[0].valueFormatter({value: ''})
     expect(respObj79).toEqual('');
     const respObj80 = noRecCtSepCount[0].valueFormatter({value: 16.841})
     expect(respObj80).toEqual('16.84');
     const respObj81 = noRecCtSepCount[0].valueFormatter({value: 125})
     expect(respObj81).toEqual(125);

     const lagNoRecCtCumSepCount = reqObj.filter(item => item.headerName == 'LAG_NO_REC_CT_CUM_SEP_COUNT');
     const respObj82 = lagNoRecCtCumSepCount[0].valueFormatter({value: ''})
     expect(respObj82).toEqual('');
     const respObj83 = lagNoRecCtCumSepCount[0].valueFormatter({value: 16.841})
     expect(respObj83).toEqual('16.84');
     const respObj84 = lagNoRecCtCumSepCount[0].valueFormatter({value: 125})
     expect(respObj84).toEqual(125);

     const effectiveTeCountNoRecCtSep = reqObj.filter(item => item.headerName == 'EFFECTIVE_TE_COUNT_NO_REC_CT_SEP');
     const respObj85 = effectiveTeCountNoRecCtSep[0].valueFormatter({value: ''})
     expect(respObj85).toEqual('');
     const respObj86 = effectiveTeCountNoRecCtSep[0].valueFormatter({value: 16.841})
     expect(respObj86).toEqual('16.84');
     const respObj87 = effectiveTeCountNoRecCtSep[0].valueFormatter({value: 125})
     expect(respObj87).toEqual(125);

     const noRecGap = reqObj.filter(item => item.headerName == 'NO_REC_GAP');
     const respObj88 = noRecGap[0].valueFormatter({value: ''})
     expect(respObj88).toEqual('');
     const respObj89 = noRecGap[0].valueFormatter({value: 16.841})
     expect(respObj89).toEqual('16.84');
     const respObj90 = noRecGap[0].valueFormatter({value: 125})
     expect(respObj90).toEqual(125);
  });
  
  it('Testing LOOKAHEAD_RUN_CHART_COLUMN_DEFS', () => {
     const reqObj = ccpconstants.LOOKAHEAD_RUN_CHART_COLUMN_DEFS;
     const staffingPlan = reqObj.filter(item => item.headerName == 'STAFFING PLAN');
     const respObj1 = staffingPlan[0].valueFormatter({value: ''})
     expect(respObj1).toEqual('');
     const respObj2 = staffingPlan[0].valueFormatter({value: 16.841})
     expect(respObj2).toEqual('16.84');
     const respObj3 = staffingPlan[0].valueFormatter({value: 125})
     expect(respObj3).toEqual(125);

     const actualForecast = reqObj.filter(item => item.headerName == 'ACTUAL/FORECAST');
     const respObj4 = actualForecast[0].valueFormatter({value: ''})
     expect(respObj4).toEqual('');
     const respObj5 = actualForecast[0].valueFormatter({value: 16.841})
     expect(respObj5).toEqual('16.84');
     const respObj6 = actualForecast[0].valueFormatter({value: 125})
     expect(respObj6).toEqual(125);

     const threshold = reqObj.filter(item => item.headerName == '95% THRESHOLD');
     const respObj7 = threshold[0].valueFormatter({value: ''})
     expect(respObj7).toEqual('');
     const respObj8 = threshold[0].valueFormatter({value: 16.841})
     expect(respObj8).toEqual('16.84');
     const respObj9 = threshold[0].valueFormatter({value: 125})
     expect(respObj9).toEqual(125);

     const ctStarts = reqObj.filter(item => item.headerName == 'CT STARTS');
     const respObj10 = ctStarts[0].valueFormatter({value: ''})
     expect(respObj10).toEqual('');
     const respObj11 = ctStarts[0].valueFormatter({value: 16.841})
     expect(respObj11).toEqual('16.84');
     const respObj12 = ctStarts[0].valueFormatter({value: 125})
     expect(respObj12).toEqual(125);

     const ctMarkups = reqObj.filter(item => item.headerName == 'CT MARKUPS');
     const respObj13 = ctMarkups[0].valueFormatter({value: ''})
     expect(respObj13).toEqual('');
     const respObj14 = ctMarkups[0].valueFormatter({value: 16.841})
     expect(respObj14).toEqual('16.84');
     const respObj15 = ctMarkups[0].valueFormatter({value: 125})
     expect(respObj15).toEqual(125);
  });

});