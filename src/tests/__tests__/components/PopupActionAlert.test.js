import React from 'react';
import { render, screen, fireEvent,cleanup } from '@testing-library/react';
import PopupActionAlert from '../../../components/PopupActionAlert';

describe('Testing PopupActionAlert', () => {
  afterEach(cleanup);

  it('renders alert message and buttons correctly', () => {
    const handleClose = jest.fn();
    const handleSuccess = jest.fn();
    const successLabel = 'Confirm';
    const closeLabel = 'Cancel';
    const message = 'Are you sure you want to delete this item?';
    const variant = 'danger';
    const showSaveButton = true;
    const showCloseButton = true;

    render(
      <PopupActionAlert
        hide={true}
        handleClose={handleClose}
        handleSuccess={handleSuccess}
        successLabel={successLabel}
        closeLabel={closeLabel}
        message={message}
        variant={variant}
        showSaveButton={showSaveButton}
        showCloseButton={showCloseButton}
      />
    );

    const messageElement = screen.getByText(message);
    const closeButtonElement = screen.getByText(closeLabel);
    const successButtonElement = screen.getByText(successLabel);

    expect(messageElement).toBeInTheDocument();
    expect(closeButtonElement).toBeInTheDocument();
    expect(successButtonElement).toBeInTheDocument();
    
    fireEvent.click(closeButtonElement);
    expect(handleClose).toHaveBeenCalledTimes(1);

    fireEvent.click(successButtonElement);
    expect(handleSuccess).toHaveBeenCalledTimes(1);
  });

  it('renders alert message and buttons correctly with success variant', () => {
    const handleClose = jest.fn();
    const handleSuccess = jest.fn();
    const successLabel = 'Confirm';
    const closeLabel = 'Cancel';
    const message = 'Are you sure you want to delete this item?';
    const variant = 'success';
   
    render(
      <PopupActionAlert
        hide={true}
        handleClose={handleClose}
        handleSuccess={handleSuccess}
        successLabel={successLabel}
        closeLabel={closeLabel}
        message={message}
        variant={variant}
      />
    );

    const messageElement = screen.getByText(message);
    const closeButtonElement = screen.getByText(closeLabel);
    const successButtonElement = screen.getByText(successLabel);

    expect(messageElement).toBeInTheDocument();
    expect(closeButtonElement).toBeInTheDocument();
    expect(successButtonElement).toBeInTheDocument();
   
  });
   it('renders alert message and buttons correctly with warning variant', () => {
    const handleClose = jest.fn();
    const handleSuccess = jest.fn();
    const successLabel = 'Confirm';
    const closeLabel = 'Cancel';
    const message = 'Are you sure you want to delete this item?';
    const variant = 'warning';
   
    render(
      <PopupActionAlert
        hide={true}
        handleClose={handleClose}
        handleSuccess={handleSuccess}
        successLabel={successLabel}
        closeLabel={closeLabel}
        message={message}
        variant={variant}
      />
    );

    const messageElement = screen.getByText(message);
    const closeButtonElement = screen.getByText(closeLabel);
    const successButtonElement = screen.getByText(successLabel);

    expect(messageElement).toBeInTheDocument();
    expect(closeButtonElement).toBeInTheDocument();
    expect(successButtonElement).toBeInTheDocument();
   
  });
  it('renders alert message and buttons correctly with other variant', () => {
    const handleClose = jest.fn();
    const handleSuccess = jest.fn();
    const successLabel = 'Confirm';
    const closeLabel = 'Cancel';
    const message = 'Are you sure you want to delete this item?';
    const variant = 'other';
   
    render(
      <PopupActionAlert
        hide={true}
        handleClose={handleClose}
        handleSuccess={handleSuccess}
        successLabel={successLabel}
        closeLabel={closeLabel}
        message={message}
        variant={variant}
      />
    );

    const messageElement = screen.getByText(message);
    const closeButtonElement = screen.getByText(closeLabel);
    const successButtonElement = screen.getByText(successLabel);

    expect(messageElement).toBeInTheDocument();
    expect(closeButtonElement).toBeInTheDocument();
    expect(successButtonElement).toBeInTheDocument();
   
  });

});
