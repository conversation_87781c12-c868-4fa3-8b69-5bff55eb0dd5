import React from 'react';
import { render, screen ,cleanup} from '@testing-library/react';
import TrainDelayMapPanel from '../../../components/TrainDelayMapPanel';
import {debounce} from "lodash";

jest.mock('lodash');

jest.mock('react-svg-pan-zoom/build-es', () => {
    const UncontrolledReactSVGPanZoom = jest.fn().mockImplementation(({ children }) => {
      return <div data-testid="svgPanZoomViewer">{children}</div>;
    });
    const INITIAL_VALUE = { value: 'mock initial value' };
    const TOOL_NONE = 'mock tool none';
  
    return { UncontrolledReactSVGPanZoom, INITIAL_VALUE, TOOL_NONE };
  });
describe('Testing TrainDelayMapPanel component', () => {
    debounce.mockImplementation((fn) => fn);
    afterEach(cleanup);

  test('renders correctly', () => {
    const nodesMap = new Map();
    nodesMap.set(1, {ID: 1, X: 100, Y: 200, MAJOR: true});
    nodesMap.set(2, {ID: 2, X: 150, Y: 250, MAJOR: false});
    const props = {
      nodes: [{ id: 1, X: 100, Y: 200 }, { id: 2, X: 300, Y: 400 }],
      lines: [{ ORIG_NODE_ID: 1, DEST_NODE_ID: 2 }],
      groupBy: 'DISTRICT',
      statsType: 'RATE',
      data: [{ id: 'District1', value: 75 }, { id: 'District2', value: 30 }],
      showValues: true,
      showOS: true,
      districtsMap:{
        District1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
      corridorsMap:{
        corridor1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
        nodesMap:nodesMap
        
    };
    const {container}=render(<TrainDelayMapPanel {...props} />);
    const panelContainer=container.querySelector('.map-panel');
    expect(panelContainer).toBeInTheDocument();
  });

  test('renders Districts correctly', () => {
    const nodesMap = new Map();
    nodesMap.set(1, {ID: 1, X: 100, Y: 200, MAJOR: true});
    nodesMap.set(2, {ID: 2, X: 150, Y: 250, MAJOR: false});
    const props = {
      nodes: [{ id: 1, X: 100, Y: 200 }, { id: 2, X: 300, Y: 400 }],
      lines: [{ ORIG_NODE_ID: 1, DEST_NODE_ID: 2 }],
      groupBy: 'DISTRICT',
      statsType: 'RATE',
      data: [{ id: 'District1', value: 15 }, { id: 'District 2', value: 30 }],
      showValues: true,
      showOS: true,
      districtsMap:{
        District1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
      corridorsMap:{
        corridor1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
        nodesMap:nodesMap
    };
    const {container}=render(<TrainDelayMapPanel {...props} />);
    const panelContainer=screen.getByText('District1 : 15%');
    expect(panelContainer).toBeInTheDocument();
    const testElement=container.querySelector('#District1');
    expect(testElement).toHaveAttribute("d", "M 100 200  L 150 250 ");
  });

test('renders path d as blank if orig node is undefined', () => {
    const nodesMap = new Map();
    nodesMap.set(1, {ID: 1, X: 100, Y: 200, MAJOR: true});
    nodesMap.set(2, {ID: 2, X: 150, Y: 250, MAJOR: false});
    const nodeData=[{ID:1,X:100,Y:200,MAJOR:true}];
    const corridors=[{id:'corridor1',ORIG_NODE_CITY:'orig',DEST_NODE_CITY:'dest',selected:true}];

    const props = {
      nodes: [{ id: 1, X: 100, Y: 200 }, { id: 2, X: 300, Y: 400 }],
      lines: [{ ORIG_NODE_ID: 1, DEST_NODE_ID: 2 }],
      groupBy: 'DISTRICT',
      statsType: 'RATE',
      data: [{ id: 'District1', value: undefined }, { id: 'District2', value: 30 }],
      showValues: true,
      showOS: true,
      districtsMap:{
        District1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ],District2: [
            {ORIG_NODE_ID: undefined, DEST_NODE_ID: 2},
          ]},
      corridorsMap:{
        corridor1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
        nodesMap:nodesMap,
        nodeData:nodeData,
        corridors:corridors
    };
    const {container}=render(<TrainDelayMapPanel {...props} />);
    const panelContainer=container.querySelector('.map-panel');
    expect(panelContainer).toBeInTheDocument();
    const testElement=container.querySelector('#District2');
    expect(testElement).toHaveAttribute("d", "");
  });

  test('renders values using NONRATE as stats to display values in hrs correctly', () => {
    const nodesMap = new Map();
    nodesMap.set(1, {ID: 1, X: 100, Y: 200, MAJOR: true});
    nodesMap.set(2, {ID: 2, X: 150, Y: 250, MAJOR: false});
    const nodeData=[{ID:4,X:100,Y:200,MAJOR:true}];
    const corridors=[{id:'corridor',ORIG_NODE_CITY:'orig',DEST_NODE_CITY:'dest',selected:false},
    {id:'-',ORIG_NODE_CITY:'orig',DEST_NODE_CITY:'dest',selected:false}];

    const props = {
      nodes: [{ id: 3, X: 100, Y: 200 }, { id: 2, X: 300, Y: 400 }],
      lines: [{ ORIG_NODE_ID: 1, DEST_NODE_ID: 2 }],
      groupBy: 'Corridor',
      statsType: 'NONRATE',
      data: [{ id: '-', value: undefined }, { id: 'District2', value: 30 }],
      showValues: true,
      showOS: true,
      districtsMap:{
        District1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ],District2: [
            {ORIG_NODE_ID: undefined, DEST_NODE_ID: 2},
          ]},
      corridorsMap:{
        corridor1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
        nodesMap:nodesMap,
        nodeData:nodeData,
        corridors:corridors,
        handleToggleCorridor:jest.fn(),
        labelData:[{X:100,Y:200,TEXT:'TestLabel'}]

    };
    const instance = new TrainDelayMapPanel(props);
    instance.changeValue('new value');
    instance._clickHandler({stopPropagation:jest.fn()},'corridor1')
    instance.Viewer={fitToViewer:jest.fn()};
    render(<TrainDelayMapPanel {...props} />);
    const panelContainer=screen.getByText('District2 : 30 hrs');
    expect(panelContainer).toBeInTheDocument();
    
  });

  test('shoould not display data if reportExpired property is true', () => {
    const nodesMap = new Map();
    nodesMap.set(1, {ID: 1, X: 100, Y: 200, MAJOR: true});
    nodesMap.set(2, {ID: 2, X: 150, Y: 250, MAJOR: false});
    const nodeData=[{ID:4,X:100,Y:200,MAJOR:true}];
    const corridors=[{id:'corridor',ORIG_NODE_CITY:'orig',DEST_NODE_CITY:'dest',selected:false},
    {id:'-',ORIG_NODE_CITY:'orig',DEST_NODE_CITY:'dest',selected:false}];

    const props = {
      nodes: [{ id: 3, X: 100, Y: 200 }, { id: 2, X: 300, Y: 400 }],
      lines: [{ ORIG_NODE_ID: 1, DEST_NODE_ID: 2 }],
      groupBy: 'Corridor',
      statsType: 'NONRATE',
      data: [{ id: '-', value: undefined }, { id: 'District2', value: 30 }],
      showValues: true,
      showOS: true,
      districtsMap:{
        District1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ],District2: [
            {ORIG_NODE_ID: undefined, DEST_NODE_ID: 2},
          ]},
      corridorsMap:{
        corridor1: [
          {ORIG_NODE_ID: 1, DEST_NODE_ID: 2},
        ]},
        nodesMap:nodesMap,
        nodeData:nodeData,
        corridors:corridors,
        handleToggleCorridor:jest.fn(),
        labelData:[{X:100,Y:200,TEXT:'TestLabel'}],
        reportExpired:true

    };
    render(<TrainDelayMapPanel {...props} />);
    const panelContainer=screen.queryByText('District2 : 30 hrs');
    expect(panelContainer).not.toBeInTheDocument();
  });

   
})
