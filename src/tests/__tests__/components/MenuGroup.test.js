import React from 'react';
import { render, fireEvent ,cleanup} from '@testing-library/react';
import MenuGroup from '../../../components/MenuGroup';

describe('Testing MenuGroup', () => {
  afterEach(cleanup);

  const buttons = [
    { value: 'home', description: 'Home' },
    { value: 'about', description: 'About' },
    { value: 'contact', description: 'Contact Us' },
  ];

  it('testing if menu buttons renderes swith correct labels', () => {
    const { getByText } = render(<MenuGroup buttons={buttons} />);
    buttons.forEach(button => {
      const label = getByText(button.description);
      expect(label).toBeInTheDocument();
    });
  });

  it('testing menu button click', () => {
    const setSelectedOptionMock = jest.fn();
    const { getByText } = render(<MenuGroup buttons={buttons} setSelectedOption={setSelectedOptionMock} />);

    const homeButton = getByText('Home');
    fireEvent.click(homeButton);

    expect(setSelectedOptionMock).toHaveBeenCalledTimes(1);
  });

  it('testing the active button', () => {
    const selectedOption = 'about';
    const { getByText } = render(<MenuGroup buttons={buttons} selectedOption={selectedOption} />);
    const activeButton = getByText('About');
    expect(activeButton).toHaveClass('menu-button-selected');
  });
});
