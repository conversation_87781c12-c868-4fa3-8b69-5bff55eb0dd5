import React from 'react';
import { render, screen, fireEvent, act, cleanup } from '@testing-library/react';
import {
  excelExportDetails,
  excelExportDetailsWithCallback,
} from '../../../util/ExcelExportUtils'; // Replace with your actual import path



jest.mock('exceljs/dist/exceljs.bare.min.js', () => {
  const ExcelJSRow = {
    values: [],
    addRow: jest.fn().mockReturnThis(),
  };

  const ExcelJSSheet = {
    views: [{ showGridLines: true }],
    addWorksheet: jest.fn().mockReturnThis(),
    addRows: jest.fn().mockReturnThis(),
    getRow: jest.fn(() => ExcelJSRow),
  };

  const ExcelJSWorkbook = {
    xlsx: {
      writeBuffer: jest.fn(() => ({
        then: jest.fn((saveAs) => {

          try {
            saveAs();
          } catch (error) {

          }

        })
      })),
    },
    addWorksheet: jest.fn(() => ExcelJSSheet),
  };

  return {
    Workbook: jest.fn(() => ExcelJSWorkbook),
  };
});



describe('Testing ExcelExportUtils', () => {
  afterEach(cleanup);
  it('Testing exports Excel without crashing', async () => {
    const fileName = 'test';
    const sheets = [
      {
        name: 'Sheet 1',
        headers: [{ headerName: 'Header 1', field: 'field1', style: 'style1' }],
        rows: [{ field1: 'Row 1' }],
      },
      {

      }
    ];
    render(<button onClick={() => excelExportDetails({ fileName, sheets })}>Export Excel</button>);
    const exportButton = screen.getByText('Export Excel');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel')).toBeInTheDocument();

  });
  it('Testing exports Excel  with blank sheets', async () => {
    const fileName = 'test';
    const sheets = [null];
    render(<button onClick={() => excelExportDetails({ fileName, sheets })}>Export Excel</button>);
    const exportButton = screen.getByText('Export Excel');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel')).toBeInTheDocument();

  });
  it('Testing exports Excel with blank props', async () => {

    render(<button onClick={() => excelExportDetails()}>Export Excel2</button>);
    const exportButton = screen.getByText('Export Excel2');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel2')).toBeInTheDocument();

  });
  it('Testing exports Excel with a callback without crashing', async () => {
    const fileName = 'test';
    const sheets = [
      {
        name: 'Sheet 1',
        headers: [{ headerName: 'Header 1', field: 'field1', style: 'style1' }],
        rows: [{ field1: 'Row 1' }],
      },
      {

      }
    ];
    const callback = jest.fn(() => {
      return jest.fn(() => {
        // Custom behavior of the inner function
      });
    });
    render(<button onClick={() => excelExportDetailsWithCallback({ fileName, sheets }, callback)}>Export Excel1</button>);
    const exportButton = screen.getByText('Export Excel1');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel1')).toBeInTheDocument();
  });
  it('Testing exports Excel with a callback blank props', async () => {

    render(<button onClick={() => excelExportDetailsWithCallback()}>Export Excel3</button>);
    const exportButton = screen.getByText('Export Excel3');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel3')).toBeInTheDocument();

  });
  it('Testing exports Excel with a callback blank sheets', async () => {
    const fileName = 'test';
    const sheets = [null];
    render(<button onClick={() => excelExportDetailsWithCallback({ fileName, sheets })}>Export Excel4</button>);
    const exportButton = screen.getByText('Export Excel4');
    await act(async () => {
      fireEvent.click(exportButton);
    })
    expect(screen.queryByText('Export Excel4')).toBeInTheDocument();
  });
});

