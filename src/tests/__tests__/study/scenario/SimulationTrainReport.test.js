
import { render, screen, fireEvent, act, cleanup, } from '@testing-library/react';
import SimulationTrainReport from '../../../../study/scenario/SimulationTrainReport';

import React from 'react';
import { Provider } from 'react-redux';
import { store } from "../../../../app/store";
import { getSimulationTrainFilters } from '../../../../study/studyActions';
jest.useFakeTimers();
jest.mock('../../../../config/API.js', () => require('../../../mocks/API.mock.js'));
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >{params.onGridReady && params.onGridReady({
    api: {
      deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
      exportDataAsExcel: jest.fn(),
      forEachNode: jest.fn((callback) => {
        const mockNode = {
          data: { id: 2 },
          setSelected: jest.fn(),
        }
        callback(mockNode); callback({ data: { id: 1 } })
      }),
      getSelectedNodes: jest.fn(),
      forEachLeafNode: jest.fn((callback) => {
        params.rowData && params.rowData.forEach((item) => {
          callback({
            data: item,
            setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
          })
        })

      }),
      setQuickFilter: jest.fn(() => { }),
      ensureNodeVisible: jest.fn()
    },
    columnApi: { autoSizeAllColumns: jest.fn() }
  })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}

    {params.rowData && params.rowData.map((item,index) => <div key={item.id?item.id:index+1} data-testid={`aggridid-${item.id}`}
      onClick={() => {
        const selectedData = {
          api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        };
        params.onRowSelected && params.onRowSelected(selectedData);
        params.onRowSelected && params.onRowSelected({ api: { getSelectedRows: (() => []) } });
        params.onSelectionChanged && params.onSelectionChanged({ api: { getSelectedRows: (() => []) } });
        params.onSelectionChanged && params.onSelectionChanged(selectedData);
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
      }} >
      <span >{item.id}</span>
      <span >{item.name}</span><span >{item.desc}</span></div>)
    }
  </div>

}));
jest.mock('exceljs/dist/exceljs.bare.min.js', () => {
  const ExcelJSRow = {
    values: [],
    addRow: jest.fn().mockReturnThis(),
  };

  const ExcelJSSheet = {
    views: [{ showGridLines: true }],
    addWorksheet: jest.fn().mockReturnThis(),
    addRows: jest.fn().mockReturnThis(),
    getRow: jest.fn(() => ExcelJSRow),
  };

  const ExcelJSWorkbook = {
    xlsx: {
      writeBuffer: jest.fn(() => ({
        then: jest.fn()
      })),
    },
    addWorksheet: jest.fn(() => ExcelJSSheet),
  };

  return {
    Workbook: jest.fn(() => ExcelJSWorkbook),
  };
});
jest.mock("file-saver", () => ({
  saveAs: jest.fn(),
}));

describe('Test SimulationTrainReport Component', () => {
  store.dispatch(getSimulationTrainFilters({ scenarioId: 1 }));
  beforeEach(() => {
  });
  afterEach(cleanup);


  it('Testing SimulationTrainReport component export functionality', async () => {
    render(<Provider store={store}><SimulationTrainReport /></Provider>);
    const selectAggrType = screen.getAllByRole('combobox');
    fireEvent.change(selectAggrType[0], { target: { value: 'trnType1' } })
    fireEvent.change(selectAggrType[1], { target: { value: 'AA' } })
    fireEvent.change(selectAggrType[2], { target: { value: 'lineSegment1' } })
    fireEvent.change(selectAggrType[3], { target: { value: 'No Delay' } })
    fireEvent.change(selectAggrType[4], { target: { value: 'trnSymb1' } })
    fireEvent.change(selectAggrType[5], { target: { value: 'fromOs1' } })

    const getReportBtn = screen.getByText('Get Report')
    await act(async () => {
      fireEvent.click(getReportBtn)
    })
    const getGridElement = screen.getByTestId('aggridid-field1');
    fireEvent.click(getGridElement);
    jest.advanceTimersByTime(100);
    await act(async () => {
      const exportBtn = screen.getByText('Export')
      fireEvent.click(exportBtn);
    })
    expect(screen.getByText('fromOs1')).toBeInTheDocument();

  });

  it('Testing SimulationTrainReport component export details functionality', async () => {
    render(<Provider store={store}><SimulationTrainReport /></Provider>);
    const selectAggrType = screen.getAllByRole('combobox');
    fireEvent.change(selectAggrType[0], { target: { value: 'trnType1' } })
    fireEvent.change(selectAggrType[1], { target: { value: 'AA' } })
    fireEvent.change(selectAggrType[2], { target: { value: 'lineSegment1' } })
    fireEvent.change(selectAggrType[3], { target: { value: 'No Delay' } })
    fireEvent.change(selectAggrType[4], { target: { value: 'trnSymb1' } })
    fireEvent.change(selectAggrType[5], { target: { value: 'fromOs1' } })
    const getReportBtn = screen.getByText('Get Report')
    await act(async () => {
      fireEvent.click(getReportBtn)
    })
    const getGridElement = screen.getByTestId('aggridid-field1');
    fireEvent.click(getGridElement);
    const exportBtn = screen.getByText('Export details')
    await act(async () => {
      fireEvent.click(exportBtn)
    })
    expect(selectAggrType[4].value).toEqual('trnSymb1');
  });

  it('Testing unused methods', () => {
    const exportDataAsExcelM = jest.fn(), exportSimulationTrainReportM = jest.fn();
    const props = { exportSimulationTrainReport: exportSimulationTrainReportM }
    const unconnectedTrainReport = (new SimulationTrainReport.WrappedComponent(props));;
    unconnectedTrainReport.onGridReady({ api: { exportDataAsExcel: exportDataAsExcelM }, columnApi: { autoSizeAllColumns: jest.fn() } });
    unconnectedTrainReport.onDetailBtnExport();
    expect(exportSimulationTrainReportM).toHaveBeenCalled();
    unconnectedTrainReport.onBtnExport();
    expect(exportDataAsExcelM).toHaveBeenCalled();
  });
  it('Testing SimulationTrainReport component clear functionality', async () => {
    render(<Provider store={store}><SimulationTrainReport /></Provider>);
    const selectAggrType = screen.getAllByRole('combobox');
    fireEvent.change(selectAggrType[0], { target: { value: 'trnType1' } })
    fireEvent.change(selectAggrType[1], { target: { value: 'AA' } })
    fireEvent.change(selectAggrType[2], { target: { value: 'lineSegment1' } })
    fireEvent.change(selectAggrType[3], { target: { value: 'No Delay' } })
    fireEvent.change(selectAggrType[4], { target: { value: 'trnSymb1' } })
    fireEvent.change(selectAggrType[5], { target: { value: 'fromOs1' } })
    const clearBtn = screen.getByText('Clear')
    fireEvent.click(clearBtn);
    expect(selectAggrType[0].value).toEqual('All');
  });


});
