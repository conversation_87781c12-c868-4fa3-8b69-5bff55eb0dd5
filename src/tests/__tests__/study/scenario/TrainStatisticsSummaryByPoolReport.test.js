
import { render, screen, fireEvent, act, cleanup, } from '@testing-library/react';
import TrainStatisticsSummaryByPoolReport from '../../../../study/scenario/TrainStatisticsSummaryByPoolReport';

import React from 'react';
import { Provider } from 'react-redux';
import { store } from "../../../../app/store";


jest.mock('../../../../config/API.js', () => require('../../../mocks/API.mock.js'));
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >{params.onGridReady && params.onGridReady({
    api: {
      deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
      exportDataAsExcel: jest.fn(),
      forEachNode: jest.fn((callback) => {
        const mockNode = {
          data: { id: 'field1' },
          setSelected: jest.fn(),
        }
        callback(mockNode); callback({ data: { id: 1 } })
      }),
      getSelectedNodes: jest.fn(),
      forEachLeafNode: jest.fn((callback) => {
        params.rowData && params.rowData.forEach((item) => {
          callback({
            data: item,
            setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
          })
        })

      }),
      setQuickFilter: jest.fn(() => { }),
      ensureNodeVisible: jest.fn()
    },
    columnApi: { autoSizeAllColumns: jest.fn() }
  })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}

    {params.rowData && params.rowData.map((item) => <div key={item.id} data-testid={`aggridid-${item.id}`}
      onClick={() => {
        const selectedData = {
          api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        };
        params.onRowSelected && params.onRowSelected(selectedData);
        params.onRowSelected && params.onRowSelected({
          api: { getSelectedRows: (() => []) }, node: {
            selected: false
          }
        });
        params.onSelectionChanged && params.onSelectionChanged({ api: { getSelectedRows: (() => []) } });
        params.onSelectionChanged && params.onSelectionChanged(selectedData);
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
      }} >
      <span >{item.id}</span><span >{item.scheduledTrn}</span><span >{item.recrewTrn}</span>
      <span >{item.name}</span><span >{item.desc}</span></div>)
    }
  </div>

}));

describe('Test TrainStatisticsSummaryByPoolReport Component', () => {

  beforeEach(() => {
  });
  afterEach(cleanup);


  it('Testing Export button functionality', async () => {
    render(<Provider store={store}><TrainStatisticsSummaryByPoolReport /></Provider>);

    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'Weekly Average' } })
    fireEvent.change(filters[1], { target: { value: 'System' } })
    fireEvent.change(filters[2], { target: { value: 'Engineer' } })
    const getReportBtn = screen.getByText('Get Report')
    await act(async () => {
      fireEvent.click(getReportBtn);
    })
    const exportBtn = screen.getByText('Export')
    await act(async () => {
      fireEvent.click(exportBtn);
    })
    expect(screen.getByText('ScheduledTrain1')).toBeInTheDocument();
    expect(screen.getByText('RecrewTrain1')).toBeInTheDocument();
  });

  it('Testing conditions with blank state', () => {
    const getTrainStatisticsSummaryReportM = jest.fn(), exportDataAsExcelM = jest.fn();
    const props = { getTrainStatisticsSummaryReport: getTrainStatisticsSummaryReportM }
    const trainStatisticsSummaryByPoolReport = (new TrainStatisticsSummaryByPoolReport.WrappedComponent(props));;
    trainStatisticsSummaryByPoolReport.getReport();
    expect(getTrainStatisticsSummaryReportM).toHaveBeenCalled();
    trainStatisticsSummaryByPoolReport.onGridReady({ columnApi: { autoSizeAllColumns: jest.fn() }, api: { exportDataAsExcel: exportDataAsExcelM } });
    trainStatisticsSummaryByPoolReport.onBtnExport();
    expect(exportDataAsExcelM).toHaveBeenCalled();
  });


});
