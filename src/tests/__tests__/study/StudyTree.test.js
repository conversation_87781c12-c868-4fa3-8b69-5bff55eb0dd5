
import { render, screen, fireEvent, act, cleanup, } from '@testing-library/react';
import StudyTree from '../../../study/StudyTree';
import React from 'react';
import { Provider } from 'react-redux';
import { store } from "../../../app/store";

jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));

//autoGroupColumnDef

jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >{params.onGridReady({
    api: {
      setQuickFilter: jest.fn()
    },
    columnApi: { autoSizeAllColumns: jest.fn() }
  })}
    {params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({
      node: {
        data: { hierarchyName: 'Name' }
      }
    })}
    {params.getRowHeight && params.getRowHeight()}
    {params.getRowHeight && params.getDataPath({ studyHierarchy: 'Name' })}

    {
      params.rowData && params.rowData.map((item,index) => <div key={index+1} data-testid={`aggrid-${item.view}-${item.studyId}`}
        onClick={() => {
          params.onRowSelected && params.onRowSelected({
            api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
              selected: true
            }
          });
          params.components && params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: item })
          params.components && params.components.fileCellRenderer.prototype.init({ value: 'New Val' })
          params.onRowSelected && params.onRowSelected({ node: { selected: false } });
          params.onSelectionChanged && params.onSelectionChanged({
            api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]), forEachLeafNode: (() => [{ data: item }]) }, node: {
              selected: true
            }
          });
          params.components && params.components.fileCellRenderer && params.components.fileCellRenderer();

        }}>

        <span >{item.id}</span>
        <span >{item.name}</span><span >{item.desc}</span></div>)}
  </div>
}));

describe('Test StudyTree Component', () => {
  beforeEach(() => {
  });
  afterEach(cleanup);


  it('Testing the Copy Study functionality', async () => {
    render(<Provider store={store}><StudyTree /></Provider>);
    const clearFilter = screen.getAllByRole('img')[2];
    fireEvent.click(clearFilter);
    expect(screen.getByPlaceholderText('Filter studies...').value).toEqual('');
  });

});
