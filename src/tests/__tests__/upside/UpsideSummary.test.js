import React from 'react';
import { render, fireEvent, screen, cleanup, act } from '@testing-library/react';
import UpsideSummary from '../../../upside/UpsideSummary';
import { Provider } from 'react-redux';
import { store } from "../../../app/store";
jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >
    {params.onGridReady && params.onGridReady({
      api: {
        deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
        forEachNode: jest.fn((callback) => {
          const mockNode = {
            data: { id: 2 },
            setSelected: jest.fn(),
          }
          callback(mockNode); callback({ data: { id: 1 } })
        }),
        getSelectedNodes: jest.fn(),
        forEachLeafNode: jest.fn((callback) => {
          params.rowData && params.rowData.forEach((item) => {
            callback({
              data: item,
              setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
            })
          })

        }),
        setQuickFilter: jest.fn(() => { }),
        ensureNodeVisible: jest.fn()
      },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}
    {params.rowData && params.rowData.map((item) => <div key={item.id} data-testid={`aggridid-upside${item.upsideId}`}
      onClick={() => {
        params.onRowSelected && params.onRowSelected({ node: { selected: false } });
        params.onRowSelected && params.onRowSelected({
          api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        });
        params.onSelectionChanged && params.onSelectionChanged({
          api: { getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }
        });
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
        params.getDataPath && params.getDataPath({ upsideHierarchy: 'upsidesummary' });
        params.components.fileCellRenderer.prototype.init({ value: 'New Val' })
        params.components.fileCellRenderer.prototype.getGui({ value: 'New Val' })
        params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: { view: 'newupside' } })
        params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: { view: 'upsidesummary' } })
      }}>
      <span >{item.upsideId}</span>
      <span >{item.upsideName}</span><span >{item.upsideHierarchy}</span></div>)}
  </div>
}));
// Mock your Redux store object
const mockStore = {
  getState: () => { return initialState },
  dispatch: jest.fn(),
  subscribe: jest.fn(),
};

const mockCurrUpside = {
  upsideId: 1,
  name: 'Test Upside',
  desc: 'Test Description',
  loadTs: new Date().toISOString(),
  userId: '11',
};

const mockUpsideTrains = [
  // Mock upsideTrains data here
];

const mockRole = 'DEVELOPER';
const mockId = '11';

const initialState = {
  upside: {
    currUpside: mockCurrUpside,
    feedback: [],
    upsideTrains: mockUpsideTrains,
  },
  app: {
    id: mockId,
    role: mockRole,
  },
};

const mockDeleteUpside = jest.fn();
const mockClearFeedback = jest.fn();

const mockAllowedToRemove = jest.fn().mockReturnValue(true);

describe('UpsideTree Component', () => {
  afterEach(() => {
    cleanup();
  });

  it('renders UpsideSummary  render component', () => {
    const initialState1 = {
      upside: {
        currUpside: {
          upsideId: 1,
          name: 'Test Upside',
        }
      },
      app: {
        id: mockId,
        role: mockRole,
      },
    };
    const mockStore = {
      getState: () => { return initialState1 },
      dispatch: jest.fn(),
      subscribe: jest.fn(),
    };
    render(<Provider store={mockStore}><UpsideSummary delete={mockDeleteUpside}
      clearFeedback={mockClearFeedback} /></Provider>);
    expect(screen.getByText('Upside Summary: 1')).toBeInTheDocument();
  });

  it('renders UpsideSummary Remove functionality', async () => {
    const props = { currUpside: { name: 'upside1', desc: 'Upside Description 1' } };
    store.upside = { currUpside: props }
    render(<Provider store={mockStore}><UpsideSummary delete={mockDeleteUpside}
      clearFeedback={mockClearFeedback} /></Provider>);
    const removeBtn = screen.getByText('Remove');
    await act(async () => {
      fireEvent.click(removeBtn);
    })
    expect(screen.queryByText('Remove Upside : Test Upside ?')).toBeInTheDocument();
    const cancelBtn = screen.getByText('Cancel');
    await act(async () => {
      fireEvent.click(cancelBtn);
    })
    expect(screen.getByText('Remove')).toBeInTheDocument();
  });
  it('renders UpsideTree Remove Confirm ', async () => {
    const props = { currUpside: { name: 'upside1', desc: 'Upside Description 1' } };
    store.upside = { currUpside: props }
    render(<Provider store={mockStore}><UpsideSummary /></Provider>);
    const removeBtn = screen.getByText('Remove');
    fireEvent.click(removeBtn);
    await act(async () => {
      expect(screen.queryByText('Remove Upside : Test Upside ?')).toBeInTheDocument();
      const removeConfirmBtn = screen.getByText('Ok');
      fireEvent.click(removeConfirmBtn);
    })
    expect(screen.getByText('Remove')).toBeInTheDocument();
    expect(mockStore.dispatch).toHaveBeenCalled();
  });
});
