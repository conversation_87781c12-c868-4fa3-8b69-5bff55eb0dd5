
import { act,cleanup } from '@testing-library/react';
Object.defineProperty(global, 'navigator', {
    value: {
        serviceWorker: {
            ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
            register: jest.fn(() => Promise.resolve({})),
            controller: {}
        },
    },
    writable: true,
});

const regObj = {}
Object.defineProperty(global, 'window', {
    value: {
        location: {
            hostname: 'http://example.com',
            origin: 'http://example.com',
            href: 'http://example.com',
            reload: jest.fn()
        },
        addEventListener: jest.fn((type, funImp) => {
            funImp();
        }),
    },
    writable: true,
});

describe('Testing serviceWorker', () => {
    const originalConsoleWarn = console.error;
    const originalConsoleLog = console.log;

    afterEach(()=>{
        console.error=originalConsoleWarn;
        console.log=originalConsoleLog;
        jest.resetModules();
        cleanup();
    })
   
    beforeEach(()=>{
        console.error=jest.fn();
        console.log=jest.fn();
    })

        it('Checking unregister service worker if unavailable', async () => {
            const { unregister } = await import('../../../serviceWorker');
            Object.defineProperty(global, 'navigator', {
                value: {
                },
                writable: true,
                
            });
            await act(async () => {
                unregister();
            })    
            expect(navigator).toEqual({});
        });
        it('Checking to  unregister service worker if available', async () => {
            const { unregister } = await import('../../../serviceWorker');
            // Mocking necessary properties and methods
            const unregisterMock = jest.fn(() => Promise.resolve());
            Object.defineProperty(global, 'navigator', {
                value: {
                    serviceWorker: {
                        controller: {},
                        ready: Promise.resolve({
                            unregister: unregisterMock,
                        }),
                    },
                },
                writable: true,
            });
            await act(async () => {
                unregister();
            })
            expect(unregisterMock).toHaveBeenCalled();
        });
        it('should handle unregister error', async () => {
            const { unregister } = await import('../../../serviceWorker');

            console.error = jest.fn();
            Object.defineProperty(global, 'navigator', {
                value: {
                    serviceWorker: {
                        controller: {},
                        ready: Promise.reject(new Error('Unregister error')),
                    },
                },
                writable: true,
            });
            await act(async () => {
                unregister();
            })
            expect(console.error).toHaveBeenCalled();
            console.error = originalConsoleWarn;
        });
        it('Testing register of serviceWorker', async () => {
            const { register } = await import('../../../serviceWorker');
            const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
                status: 404,
                headers: {
                    get: jest.fn(),
                },
            });
            process.env.NODE_ENV = 'production';
            process.env.PUBLIC_URL = 'http://example.com';
            window.location.hostname = 'example.com';
            regObj.installing = { state: 'installed' };
            Object.defineProperty(global, 'navigator', {
                value: {
                    serviceWorker: {
                        controller: {},
                        ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                        register: jest.fn(() => Promise.resolve(regObj)),
                    },
                },
                writable: true,
            });
            await act(async () => {

                register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
            })
            await act(async () => {
                regObj.onupdatefound();
            })
            regObj.installing.onstatechange();
            expect(navigator.serviceWorker.register).toHaveBeenCalled();
        });
    it('Testing register of serviceWorker', async () => {
       
        const { register } = await import('../../../serviceWorker');

        const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
            status: 404,
            headers: {
                get: jest.fn(),
            },
        });
        process.env.NODE_ENV = 'production';
        process.env.PUBLIC_URL = 'http://example.com';
        window.location.hostname = 'example.com';
        regObj.installing = { state: 'installed' };
        Object.defineProperty(global, 'navigator', {
            value: {
                serviceWorker: {

                    ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                    register: jest.fn(() => Promise.resolve(regObj)),
                },
            },
            writable: true,
        });
        await act(async () => {

            register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
        })
        await act(async () => {
            regObj.onupdatefound();
        })
        regObj.installing.onstatechange();
        expect(navigator.serviceWorker.register).toHaveBeenCalled();
        
    });


it('Testing register of serviceWorker', async () => {
    const { register } = await import('../../../serviceWorker');
    const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
        status: 404,
        headers: {
            get: jest.fn(),
        },
    });
    process.env.NODE_ENV = 'production';
    process.env.PUBLIC_URL = 'http://example.com';
    window.location.hostname = 'example.com';
    regObj.installing = { state: '' };
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.resolve(regObj)),
            },
        },
        writable: true,
    });
    await act(async () => {

        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    await act(async () => {
        regObj.onupdatefound();
    })
    regObj.installing.onstatechange();
    expect(navigator.serviceWorker.register).toHaveBeenCalled();
});

it('Testing register of serviceWorker', async () => {
    const { register } = await import('../../../serviceWorker');
    const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
        status: 404,
        headers: {
            get: jest.fn(),
        },
    });
    regObj.installing = { state: 'installed' };
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.resolve(regObj)),
            },
        },
        writable: true,
    });
    await act(async () => {

        register({});
    })
    await act(async () => {
        regObj.onupdatefound();
    })
    regObj.installing.onstatechange();
    expect(navigator.serviceWorker.register).toHaveBeenCalled();
});

it('Testing register of serviceWorker', async () => {
    const { register } = await import('../../../serviceWorker');
    const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
        status: 404,
        headers: {
            get: jest.fn(),
        },
    });
    regObj.installing = null;
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.resolve(regObj)),
            },
        },
        writable: true,
    });
    const onUpdateMock=jest.fn();
    await act(async () => {

        register({ onUpdate: onUpdateMock, onSuccess: onUpdateMock });
    })
    await act(async () => {
        regObj.onupdatefound();
    })
    expect(navigator.serviceWorker.register).toHaveBeenCalled();
});

it('Testing register of serviceWorker', async () => {
    const { register } = await import('../../../serviceWorker');
    process.env.NODE_ENV = 'production';
    process.env.PUBLIC_URL = 'http://localhost.com';
    window.location.hostname = 'localhost.com';
    await act(async () => {

        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    expect(navigator.serviceWorker.register).toHaveBeenCalled();  
});

it('Testing register of serviceWorker', async () => {
    
    const { register } = await import('../../../serviceWorker');

    process.env.NODE_ENV = 'local';
    process.env.PUBLIC_URL = 'http://localhost.com';
    window.location.hostname = 'localhost.com';
   
    await act(async () => {

        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    expect(navigator.serviceWorker.register).toHaveBeenCalled();
});

it('Testing register of serviceWorker', async () => {
    Object.defineProperty(global, 'window', {
        value: {
            location: {
                hostname: 'localhost',
                origin: 'http://localhost.com',
                href: 'http://localhost.com',
                reload: jest.fn()
            },
            addEventListener: jest.fn((type, funImp) => {
                funImp();
            }),
        },
        writable: true,
    });
    
    const { register } = await import('../../../serviceWorker');
    console.error=jest.fn()
    const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue({
        status: 404,
        headers: {
            get: jest.fn(),
        },
    });
    process.env.NODE_ENV = 'production';
    process.env.PUBLIC_URL = 'http://localhost.com';
    window.location.hostname = 'example.com';
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.reject(regObj)),
            },
        },
        writable: true,
    });
    await act(async () => {

        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    console.error=originalConsoleWarn;
    expect(navigator.serviceWorker.register).not.toHaveBeenCalled();
});
it('Testing service worker on fetch url success', async () => {
    Object.defineProperty(global, 'window', {
        value: {
            location: {
                hostname: 'localhost',
                origin: 'http://localhost.com',
                href: 'http://localhost.com',
                reload: jest.fn()
            },
            addEventListener: jest.fn((type, funImp) => {
                funImp();
            }),
        },
        writable: true,
    });
    
    const { register } = await import('../../../serviceWorker');

    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
        status: 200,
        headers: {
            get: jest.fn(),
        },
    });
    process.env.NODE_ENV = 'production';
    process.env.PUBLIC_URL = 'http://localhost.com';
    window.location.hostname = 'example.com';
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.reject(regObj)),
            },
        },
        writable: true,
    });
    await act(async () => {

        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    expect(navigator.serviceWorker.register).toHaveBeenCalled();
});

it('Testing service worker 404 error on fetch url ', async () => {
    Object.defineProperty(global, 'window', {
        value: {
            location: {
                hostname: 'localhost',
                origin: 'http://localhost.com',
                href: 'http://localhost.com',
                reload: jest.fn()
            },
            addEventListener: jest.fn((type, funImp) => {
                funImp();
            }),
        },
        writable: true,
    });
    
    const { register } = await import('../../../serviceWorker');

    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
        status: 404,
        headers: {
            get: jest.fn(),
        },
    });
    process.env.NODE_ENV = 'production';
    process.env.PUBLIC_URL = 'http://localhost.com';
    window.location.hostname = 'example.com';
    Object.defineProperty(global, 'navigator', {
        value: {
            serviceWorker: {
                controller: {},
                ready: Promise.resolve({ unregister: jest.fn(() => Promise.resolve({})) }),
                register: jest.fn(() => Promise.reject(regObj)),
            },
        },
        writable: true,
    });
    await act(async () => {
        register({ onUpdate: jest.fn(), onSuccess: jest.fn() });
    })
    expect(navigator.serviceWorker.register).not.toHaveBeenCalled();
});

});