import { render, screen, fireEvent, act, within, cleanup } from '@testing-library/react';
import PoolSetup from '../../../plan/PoolSetup';
import { changeCurrentPlanViewData, getHubEditorData, getPoolSetupsFilters } from '../../../plan/planActions';
import React from 'react';
import { Provider } from 'react-redux';
import { store } from "../../../app/store";
jest.mock('popper.js');
jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));
jest.mock('exceljs/dist/exceljs.bare.min.js', () => {
  const ExcelJSRow = {
    values: [],
    addRow: jest.fn().mockReturnThis(),
  };
  const ExcelJSSheet = {
    views: [{ showGridLines: true }],
    addWorksheet: jest.fn().mockReturnThis(),
    addRows: jest.fn().mockReturnThis(),
    getRow: jest.fn(() => ExcelJSRow),
  };
  const ExcelJSWorkbook = {
    xlsx: {
      writeBuffer: jest.fn(() => ({
        then: jest.fn((callFn) => { callFn() })
      })),
    },
    addWorksheet: jest.fn(() => ExcelJSSheet),
  };
  return {
    Workbook: jest.fn(() => ExcelJSWorkbook),
  };
});
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >
    {
      params.onGridReady && params.onGridReady({
        api: {
          deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
          forEachNode: jest.fn((callback) => {
            const mockNode = {
              data: { id: '', currRecord: true },
              setSelected: jest.fn(),
            }
            callback(mockNode); callback({ data: { id: 'P1' }, setSelected: jest.fn() })
          }),
          getSelectedRows: () => [],
          getSelectedNodes: jest.fn(),
          sizeColumnsToFit: jest.fn(),
          forEachLeafNode: jest.fn((callback) => {
            params.rowData && params.rowData.forEach((item) => {
              callback({
                data: item,
                setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
              })
            })

          }),
          setQuickFilter: jest.fn(() => { }),
          ensureNodeVisible: jest.fn(),
          exportDataAsExcel: jest.fn(() => {

          }),
          setRowData: jest.fn()
        },
        columnApi: { autoSizeAllColumns: jest.fn(), ensureIndexVisible: jest.fn() }
      })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}
    {params.onPaginationChanged && params.onPaginationChanged()}
    {params.rowData && params.rowData.map((item, index) => <div key={index} data-testid={`aggridid-${item.id}`}
      onClick={() => {
        const selectedData = {
          api: { getDisplayedRowAtIndex: jest.fn(() => { return { setSelected: jest.fn() } }), getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        };
        params.onRowSelected && params.onRowSelected(selectedData);
        params.onSelectionChanged && params.onSelectionChanged(selectedData);
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
      }} >
      <span >{item.distr}</span> <span >{item.crewOrgnOs}</span>
      <span >{item.subdistr}</span><span >{item.pool}</span></div>)
    }
  </div>

}));

describe('Test PoolSetup Component', () => {
  store.dispatch({ type: 'GET_SYSTEM_CONFIGURATION', payload: { isEditEnabled: true } })
  store.dispatch(changeCurrentPlanViewData({ currPlan: { planId: 'plan1', id: 'plan1' } }));
  store.dispatch(getHubEditorData({ id: '1' }));
  ;
  store.dispatch(getPoolSetupsFilters({ planId: '1' }));
  beforeEach(() => {
  });
  afterEach(cleanup);
  it('Testing PoolSetup Add Terminal functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Terminals'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Add')[0])
    })
    const pool = screen.getByPlaceholderText('Terminal');
    fireEvent.change(pool, { target: { value: '5' } });
    const os = screen.getByPlaceholderText('Select OS');
    fireEvent.change(os, { target: { value: 'OS1' } });
    const leadTime = screen.getByPlaceholderText('Lead Time in Mins');
    fireEvent.change(leadTime, { target: { value: '11' } });
    const resthrs = screen.getByPlaceholderText('Rest (Hrs)');
    fireEvent.change(resthrs, { target: { value: '0.5' } });
    const undist = screen.getByPlaceholderText('Undisturb Rest');
    fireEvent.change(undist, { target: { value: 'Yes' } });
    const engti0 = screen.getAllByPlaceholderText('Eng. Tie-Up Short')[0];
    fireEvent.change(engti0, { target: { value: '4 - Tie-Up Time' } });
    const engti1 = screen.getAllByPlaceholderText('Eng. Tie-Up Short')[1];
    fireEvent.change(engti1, { target: { value: '4 - Tie-Up Time' } });
    const engti2 = screen.getAllByPlaceholderText('Eng. Tie-Up Short')[2];
    fireEvent.change(engti2, { target: { value: '4 - Tie-Up Time' } });
    const engti3 = screen.getAllByPlaceholderText('Eng. Tie-Up Short')[3];
    fireEvent.change(engti3, { target: { value: '4 - Tie-Up Time' } });
    await act(async () => {
      fireEvent.click(screen.getAllByText('Add')[2])
    })
    expect(screen.getAllByText('Add').length).toEqual(2);
  });


  it('Testing PoolSetup Remove functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Remove pool'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Remove')[2])
    })
    //expect(screen.queryByTestId('aggridid-P1')).not.toBeInTheDocument();
  });

  it('Testing PoolSetup Export functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Export'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Export details'))
    })
  });

  it('Testing CrewProPlansSummary rendered correctly', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-1'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Remove')[0])
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Remove')[2])
    })
    expect(screen.getAllByText('Remove').length).toEqual(2);
    expect(screen.queryByTestId('aggridid-1')).not.toBeInTheDocument();

  });

  it('Testing PoolSetup update functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-1'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Edit')[0])
    })
    const terminal = screen.getByPlaceholderText('Terminal');
    fireEvent.change(terminal, { target: { value: '4' } });
    expect(screen.getAllByText('Update')[0]).not.toBeDisabled();
    await act(async () => {
      fireEvent.click(screen.getAllByText('Update')[0])
    })

    expect(screen.queryByText('Update')).not.toBeInTheDocument();
  });
  it('Testing PoolSetup Add Turns functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Turns'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Add')[1])
    })
    const terminal = screen.getByPlaceholderText('Terminal');
    fireEvent.change(terminal, { target: { value: '5' } });

    const pool = screen.getByPlaceholderText('Turn ID');
    fireEvent.change(pool, { target: { value: '1234' } });
    const craft = screen.getAllByRole('combobox')[1];
    fireEvent.change(craft, { target: { value: 'Engineer' } });

    await act(async () => {
      fireEvent.click(screen.getAllByText('Add')[2])
    })

    expect(screen.queryAllByText('Add').length).toEqual(2);
  });

  it('Testing PoolSetup Remove Turn Functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Turns'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-turn1'))
    })

    await act(async () => {
      fireEvent.click(screen.getAllByText('Remove')[1])
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Remove')[2])
    })
    expect(screen.getByTestId('aggridid-P1')).toBeInTheDocument();
    expect(screen.getByTestId('aggridid-P6')).toBeInTheDocument();

  });
  it('Testing PoolSetup Copy pool functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Copy pool'))
    })
    const pool = screen.getByPlaceholderText('Pool');
    fireEvent.change(pool, { target: { value: 'SD' } });
    await act(async () => {
      fireEvent.click(screen.getByText('Copy'))
    })
    expect(screen.queryByText('Copy')).not.toBeInTheDocument();
  });

  it('Testing PoolSetup Add new pool functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('Add new pool'))
    })
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    const pool = screen.getByPlaceholderText('Pool');
    fireEvent.change(pool, { target: { value: 'SD' } })
    fireEvent.click(screen.getByText('Cancel'))
    expect(screen.queryByText('Cancel')).not.toBeInTheDocument();

  });


  it('Testing Add new Pool Functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getByText('Add new pool'))
    })
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    const pool = screen.getByPlaceholderText('Pool');
    fireEvent.change(pool, { target: { value: 'SD' } });
    expect(screen.getByText('Add')).not.toBeDisabled();
    fireEvent.click(screen.getByText('Add'))
    await act(async () => {
      fireEvent.click(screen.getByText('Save'))
    })
    expect(screen.queryByText('Save')).not.toBeInTheDocument();

  });

  it('Testing Add new Extraboard Functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getByText('Add new pool'))
    })
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    const pool = screen.getByPlaceholderText('Pool');
    fireEvent.change(pool, { target: { value: 'SD' } });
    expect(screen.getByText('Add')).not.toBeDisabled();
    fireEvent.click(screen.getByText('Add'))
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P5'))
    })
    expect(screen.queryAllByText('SAAA')[0]).toBeInTheDocument();

  });
  it('Testing PoolSetup change value of fields', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P2'))
    })
    await act(async () => {

      const subDistr = screen.getByPlaceholderText('Select Hub');
      fireEvent.change(subDistr, { target: { value: 'HUB12' } });


    })

    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "12-HUB12" });
    fireEvent.click(optionItem1);
    await act(async () => {
      fireEvent.change(screen.getByDisplayValue('91.1'), { target: { value: '91.11' } })
      fireEvent.change(screen.getByDisplayValue('91.2'), { target: { value: '91.21' } })
      fireEvent.change(screen.getByDisplayValue('91.3'), { target: { value: '91.31' } })
      fireEvent.change(screen.getByDisplayValue('91.4'), { target: { value: '91.41' } })
      fireEvent.change(screen.getByDisplayValue('91.5'), { target: { value: '91.51' } })
      fireEvent.change(screen.getByDisplayValue('91.6'), { target: { value: '91.61' } })
      fireEvent.change(screen.getByDisplayValue('91.7'), { target: { value: '91.71' } })
      fireEvent.change(screen.getByDisplayValue('91.8'), { target: { value: '91.81' } })
      fireEvent.change(screen.getByDisplayValue('11.11'), { target: { value: '11.22' } })
      fireEvent.change(screen.getByDisplayValue('22.11'), { target: { value: '22.22' } })
      fireEvent.change(screen.getByDisplayValue('33.11'), { target: { value: '33.22' } })
      fireEvent.change(screen.getByDisplayValue('44.11'), { target: { value: '44.22' } })
      fireEvent.change(screen.getByDisplayValue('55.11'), { target: { value: '55.22' } })
      fireEvent.change(screen.getByDisplayValue('66.11'), { target: { value: '66.22' } })
      fireEvent.change(screen.getByDisplayValue('77.11'), { target: { value: '77.22' } })
      fireEvent.change(screen.getByDisplayValue('81.1'), { target: { value: '81.11' } })
      fireEvent.change(screen.getByDisplayValue('81.2'), { target: { value: '81.21' } })
      fireEvent.change(screen.getByDisplayValue('81.3'), { target: { value: '81.31' } })
      fireEvent.change(screen.getByDisplayValue('81.4'), { target: { value: '81.41' } })
      fireEvent.change(screen.getByDisplayValue('81.5'), { target: { value: '81.51' } })
      fireEvent.change(screen.getByDisplayValue('81.6'), { target: { value: '81.61' } })
      fireEvent.change(screen.getByDisplayValue('81.7'), { target: { value: '81.71' } })
      fireEvent.change(screen.getByDisplayValue('81.8'), { target: { value: '81.81' } })
      fireEvent.change(screen.getByDisplayValue('71.1'), { target: { value: '71.11' } })
      fireEvent.change(screen.getByDisplayValue('71.2'), { target: { value: '71.21' } })
      fireEvent.change(screen.getByDisplayValue('71.3'), { target: { value: '71.31' } })
      fireEvent.change(screen.getByDisplayValue('71.4'), { target: { value: '71.41' } })
      fireEvent.change(screen.getByDisplayValue('71.5'), { target: { value: '71.51' } })
      fireEvent.change(screen.getByDisplayValue('71.6'), { target: { value: '71.61' } })
      fireEvent.change(screen.getByDisplayValue('71.7'), { target: { value: '71.71' } })
      fireEvent.change(screen.getByDisplayValue('71.8'), { target: { value: '71.81' } })

    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })

    await act(async () => {
      fireEvent.click(screen.getByText('Yes'))
    })
    expect(screen.queryByText('Yes')).not.toBeInTheDocument();
  });

  it('Testing PoolSetup change HUb correctly', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P2'))
    })
    await act(async () => {

      const subDistr = screen.getByPlaceholderText('Select Hub');
      fireEvent.change(subDistr, { target: { value: 'HUB12' } });


    })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "12-HUB12" });
    fireEvent.click(optionItem1);

    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByText('No'))
    })
    expect(screen.queryByText('No')).not.toBeInTheDocument();
  });
  it('Testing individual methods of PoolSetup component', async () => {
    const poolSetupRemovePool = jest.fn(), savePlanPool = jest.fn(), clearPoolSetupDetailData = jest.fn();
    const props = {
      getPoolSetupOSCodes: jest.fn(),
      getPoolSetup: jest.fn(), getPoolSetupHubCodes: jest.fn(), currPlan: {}, poolSetup: { data: [] }, poolSetupRemovePooloolSetup: {}, clearPoolSetupDetailData, poolSetupRemovePool, savePlanPool
    }

    await act(async () => {
      const unconnectedPoolSetup = (new PoolSetup.WrappedComponent(props));

      const event = {
        node: { selected: false },
        api: {
          getSelectedRows: jest.fn(() => {
            return [{ action: 'ADD', id: 1, name: 'Row Data 1' }]
          })
        }
      }
      unconnectedPoolSetup.onOffDutyRulesGridReady({ columnApi: { autoSizeAllColumns: jest.fn() }, api: { selected: false } });
      unconnectedPoolSetup.handleRowSelection(event);
      unconnectedPoolSetup.handleRowSelectionTurn(event);
      unconnectedPoolSetup.handleTerminalRowSelection(event);
      unconnectedPoolSetup.state = { isUpdated: true, addPool: false, currRow: {} };
      unconnectedPoolSetup.handleSearchReport();
      unconnectedPoolSetup.addPool();
      unconnectedPoolSetup.copyPool();
      unconnectedPoolSetup.removePool();
      unconnectedPoolSetup.state = { addPool: true };
      unconnectedPoolSetup.addPool();
      unconnectedPoolSetup.copyPool();
      unconnectedPoolSetup.addTerminalSystem({});
      unconnectedPoolSetup.handleSearchReport();
      unconnectedPoolSetup.updateBooleanField('', 'False');
      unconnectedPoolSetup.updateOtherField('dhTravelM', '0.00');
      unconnectedPoolSetup.updateOtherField('dhTravelMM', '0.00');
      unconnectedPoolSetup.updateField('dhTravelM', '0.00');
      unconnectedPoolSetup.updateField('dhTravelM', '110.00');
      unconnectedPoolSetup.gridApi = { setRowData: jest.fn(), deselectAll: jest.fn() }
      unconnectedPoolSetup.reset();
      unconnectedPoolSetup.state = { mode: '', currRow: { hubName: 'hub1', action: 'ADD' } };
      unconnectedPoolSetup.poolConfirmSaveEvent({});
      expect(savePlanPool).toHaveBeenCalled();
      unconnectedPoolSetup.state = { mode: '', currRow: { hubName: 'hub1', action: 'REMOVE' } };
      unconnectedPoolSetup.poolConfirmSaveEvent();
      expect(savePlanPool).toHaveBeenCalled();
      unconnectedPoolSetup.number_test('99-2');
      unconnectedPoolSetup.number_test('99');
      unconnectedPoolSetup.number_test('99.888');
      expect(props.clearPoolSetupDetailData).toHaveBeenCalled();
    })
  })
  it('Testing PoolSetup Edit turn functionality', async () => {
    render(<Provider store={store}><PoolSetup /></Provider>);
    const filters = screen.getAllByRole('combobox')
    fireEvent.change(filters[0], { target: { value: 'AA' } })
    fireEvent.change(filters[1], { target: { value: 'SAAA' } })
    fireEvent.change(filters[2], { target: { value: 'pool1' } })
    await act(async () => {
      fireEvent.click(screen.getByText('Search'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-P1'))
    })
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-turn1'))
    })
    await act(async () => {
      fireEvent.click(screen.getAllByText('Edit')[1])
    })
    const pool = screen.getByPlaceholderText('Turn ID');
    fireEvent.change(pool, { target: { value: '1234' } });


    await act(async () => {
      fireEvent.click(screen.getAllByText('Update')[0])
    })

    expect(screen.getByTestId('aggridid-P1')).toBeInTheDocument();
  });

});
