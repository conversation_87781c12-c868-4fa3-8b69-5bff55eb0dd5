import { render, screen, fireEvent, act, within, cleanup } from '@testing-library/react';
import Plan from '../../../plan/Plan';
import { changeCurrentPlanViewData, getMasterPlans, getPoolSetupsFilters } from '../../../plan/planActions';
import React from 'react';
import { CREW_PRO_PLANS_SUMMARY_MSG } from "../../../config/ccpconstants";
import { Provider } from 'react-redux';
import { store } from "../../../app/store";
jest.mock('popper.js');
jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  createRef: jest.fn(() => {
    return {
      current: {
        planName: { select: jest.fn() }, clear: jest.fn(),

      }
    }
  }),
}));
jest.mock('react-bootstrap', () => {
  const originalModule = jest.requireActual('react-bootstrap');
  return {
    ...originalModule,
    Form: {
      ...originalModule.Form,
      render: ({ ...props }) => <form {...props} inline="false" validated="false" />,
    }
  };
});
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >
    {
      params.onGridReady && params.onGridReady({
        api: {
          deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
          forEachNode: jest.fn((callback) => {
            const mockNode = {
              data: { id: 'id1', currRecord: true },
              setSelected: jest.fn(),
            }
            callback(mockNode); callback({ data: { id: 33 }, setSelected: jest.fn() })
          }),
          getSelectedRows: () => [],
          getSelectedNodes: jest.fn(),
          sizeColumnsToFit: jest.fn(),
          forEachLeafNode: jest.fn((callback) => {
            params.rowData && params.rowData.forEach((item) => {
              callback({
                data: item,
                setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
              })
            })

          }),
          setQuickFilter: jest.fn(() => { }),
          ensureNodeVisible: jest.fn(),
          exportDataAsExcel: jest.fn(() => {

          }),
          setRowData: jest.fn()
        },
        columnApi: { autoSizeAllColumns: jest.fn(), ensureIndexVisible: jest.fn() }
      })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}
    {params.onPaginationChanged && params.onPaginationChanged()}
    {params.components && params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: { view: '' } })
    }
    {params.components && params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: { view: 'plansummary', planType: 'CREWPRO' } })
    }
    {params.rowData && params.rowData.map((item, index) => <div key={index} data-testid={`aggridid-${item.view}`}
      onClick={() => {
        const selectedData = {
          api: { getDisplayedRowAtIndex: jest.fn(() => { return { setSelected: jest.fn() } }), getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        };
        params.onRowSelected && params.onRowSelected(selectedData);
        params.components && params.components.fileCellRenderer.prototype.init({ value: 'New Val', data: { view: item.view } })

        params.onSelectionChanged && params.onSelectionChanged(selectedData);
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
      }} >
      <span >{item.view}</span> <span >{item.hierarchyName}</span>
    </div>)
    }
  </div>

}));

describe('Test Plan Component', () => {
  store.dispatch({ type: 'GET_SYSTEM_CONFIGURATION', payload: { isEditEnabled: true } })
  store.dispatch(changeCurrentPlanViewData({ currPlan: { planId: 'plan1', id: 'plan1' } }));
  store.dispatch(getMasterPlans());

  ;
  store.dispatch(getPoolSetupsFilters({ planId: '1' }));
  beforeEach(() => {
  });
  afterEach(cleanup);



  it('Testing Plan crewproplans rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getByTestId('aggridid-crewproplans'));
    })
    expect(screen.getByText(CREW_PRO_PLANS_SUMMARY_MSG)).toBeInTheDocument();
  });
  it('Testing Plan plansummary rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-plansummary')[0]);
    })
    expect(screen.getByText('Plan Summary: 1')).toBeInTheDocument();
  });
  it('Testing Plan copyplan rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-copyplan')[0]);
    })
    expect(screen.getAllByText('Copy Plan')[0]).toBeInTheDocument();
  });
  it('Testing Plan crewprofiles rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-crewprofiles')[0]);
    })
    expect(screen.getAllByText('Crew Profiles')[0]).toBeInTheDocument();
  });
  it('Testing Plan cardedjobs rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-cardedjobs')[0]);
    })
    expect(screen.getAllByText('Carded Jobs')[0]).toBeInTheDocument();


  });
  it('Testing Plan extraboard rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-extraboard')[0]);
    })
    expect(screen.getAllByText('Extraboard')[0]).toBeInTheDocument();
  });
  it('Testing Plan hubeditor rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-hubeditor')[0]);
    })
    expect(screen.getAllByText('Hub Editor')[0]).toBeInTheDocument();
  });
  it('Testing Plan idpools rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-idpools')[0]);
    })
    expect(screen.getAllByText('ID Pools')[0]).toBeInTheDocument();
  });
  it('Testing Plan poolsetup rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-poolsetup')[0]);
    })
    expect(screen.getAllByText('Pool Setup')[0]).toBeInTheDocument();

  });
  it('Testing Plan tieupexceptionrules rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-tieupexceptionrules')[0]);
    })
    expect(screen.getAllByText('Tie-Up Exception Rules')[0]).toBeInTheDocument();
  });
  it('Testing Plan workrestprofiles rendered correctly', async () => {
    render(<Provider store={store}><Plan /></Provider>);
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('aggridid-workrestprofiles')[0]);
    })
    expect(screen.getAllByText('Work Rest Profiles')[0]).toBeInTheDocument();
  });
});
