
import * as planActions from "../../../plan/planActions";
import { act, cleanup } from "@testing-library/react";
import { store } from "../../../app/store";
import {
  getMasterPlansAPI, getPoolSetupAPI, getHubDetailsAPI, hubEditorRemoveAPI, hubEditorUpdateAPI, hubEditorSaveAPI,
  getPoolSetupHubCodesAPI, planExtraboardReomveSaveAPI, getPoolSetupDetailAPI, planRemoveCrewProfileAPI, getExtraboardFiltersAPI,
  getPoolSetupsFiltersAPI, getWrpFiltersAPI, getTueFiltersAPI, getIdPoolsFiltersAPI, getWorkRestProfilesExportDetailsAPI
  , getCrewProfilesAPI, getTieUpExceptionRulesAPI, getTieUpExceptionRuleDetailsAPI, getCardedJobFiltersAPI, getWorkRestProfilesAPI, getCrewProfileFiltersAPI
  , planExtraboardSaveAPI, planPoolSaveAPI, getCardedJobsExportDetailsAPI, deletePlanAPI, planPoolRemoveSaveAPI, getCardedJobsAPI, getCardedJobDetailsAPI,
  getIdPoolsAPI, getExtraboardAPI, getExtraboardDetailsAPI, copyPlanAPI, getExtraboardExportDetailsAPI, updatePlanAPI, getCrewProfileDetailsAPI,
  getPoolSetupOSCodesAPI, getWorkRestProfileDetailsAPI, planCrewProfileSaveAPI, planCrewProfileAddAPI
} from "../../../config/API.js";
import * as appActions from "../../../app/appActions";

jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));
jest.mock('exceljs/dist/exceljs.bare.min.js', () => {
  const ExcelJSRow = {
    values: [],
    addRow: jest.fn().mockReturnThis(),
  };

  const ExcelJSSheet = {
    views: [{ showGridLines: true }],
    addWorksheet: jest.fn().mockReturnThis(),
    addRows: jest.fn().mockReturnThis(),
    getRow: jest.fn(() => ExcelJSRow),
  };

  const ExcelJSWorkbook = {
    xlsx: {
      writeBuffer: jest.fn(() => ({
        then: jest.fn()
      })),
    },
    addWorksheet: jest.fn(() => ExcelJSSheet),
  };

  return {
    Workbook: jest.fn(() => ExcelJSWorkbook),
  };
});
jest.mock('popper.js');
jest.mock("file-saver", () => ({
  saveAs: jest.fn(),
}));

describe('Testing Run Simulation Actions', () => {
  afterEach(cleanup);

  it('Testing getMasterPlans functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    getMasterPlansAPI.mockResolvedValueOnce({ data: {} });
    await act(async () => {
      store.dispatch(planActions.getMasterPlans());
    })
    getMasterPlansAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getMasterPlans());
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P002", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing refreshMasterPlans functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getMasterPlansAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.refreshMasterPlans());
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P003", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing getHubDetails functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getHubDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getHubDetails());
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P009", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing removeHubEditor functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    hubEditorRemoveAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupOSCodesFail());
      store.dispatch(planActions.getPoolSetupHubCodesFail());
      store.dispatch(planActions.getHubEditorDataFail());
      store.dispatch(planActions.removeHubEditor());
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing updateHubEditor functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    hubEditorUpdateAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.updateHubEditor({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing saveHubEditor functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    await act(async () => {
      store.dispatch(planActions.exportCrewProfileDetails({ planId: 2 }));
    })
    hubEditorSaveAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.saveHubEditor({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getPoolSetupHubCodes functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getPoolSetupHubCodesAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupHubCodes({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-C002", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getHubEditorData functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getPoolSetupHubCodesAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getHubEditorData({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-C002", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getPoolSetupOSCodes functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getPoolSetupOSCodesAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupOSCodes({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-C002", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getWrpFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getWrpFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getWrpFilters({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P020", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getTueFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getTueFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getTueFilters({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P019", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getPoolSetupsFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getPoolSetupsFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupsFilters({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P018", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getIdPoolsFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getIdPoolsFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getIdPoolsFilters({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P017", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getExtraboardFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getExtraboardFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getExtraboardFilters({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P016", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanRemoveCrewProfile functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    planRemoveCrewProfileAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.savePlanRemoveCrewProfile({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P010", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanAddCrewProfile functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    planCrewProfileAddAPI.mockResolvedValueOnce({ data: [] });
    await act(async () => {

      store.dispatch(planActions.savePlanAddCrewProfile({ planId: 2 }));

    })
    planCrewProfileAddAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.savePlanAddCrewProfile({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getWorkRestProfileDetails functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getWorkRestProfileDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getWorkRestProfileDetails({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P015", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanCrewProfile functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    planCrewProfileSaveAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.savePlanCrewProfile({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getCrewProfileDetails functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getCrewProfileDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getCrewProfileDetails({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P022", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getCrewProfiles functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getCrewProfilesAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getCrewProfiles({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P008", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getCrewProfileFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getCrewProfileFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getCrewProfileFilters({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P022", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing getWorkRestProfiles functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getWorkRestProfilesAPI.mockRejectedValueOnce(error);
    await act(async () => {

      store.dispatch(planActions.getTieUpExceptionRulesFail({ planId: 2 }));

      store.dispatch(planActions.getWorkRestProfiles({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P014", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing getCardedJobFilters functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getCardedJobFiltersAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getCardedJobFilters({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P021", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getTieUpExceptionRuleDetails functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getTieUpExceptionRuleDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getTieUpExceptionRuleDetails({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P013", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getTieUpExceptionRules functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getTieUpExceptionRulesAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getTieUpExceptionRules({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P012", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getPoolSetupDetail functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const resp = { data: { poolSetUps: [{ craft: 'CO', poolRotationHistories: [{ craft: 'CO' }, { craft: 'EN' }] }, { craft: 'EN' }] } };
    const error = { response: { status: 404 } };

    getPoolSetupDetailAPI.mockResolvedValueOnce(resp);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupDetail({ planId: 2 }));

    })
    getPoolSetupDetailAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetupDetail({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P011", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getPoolSetup functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getPoolSetupAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getPoolSetup({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P010", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanRemoveExtraboard functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    planExtraboardReomveSaveAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.savePlanRemoveExtraboard({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P010", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanExtraboard functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    planExtraboardSaveAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.savePlanExtraboard({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing updatePlan functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    updatePlanAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.updatePlan({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P005", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing copyPlan functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    copyPlanAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.copyPlan({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing exportExtraboardDetails functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    await act(async () => {
      store.dispatch(planActions.exportExtraboardDetails({ planId: 2 }));
    })
    getExtraboardExportDetailsAPI.mockResolvedValueOnce({});

    await act(async () => {
      store.dispatch(planActions.exportExtraboardDetails({ planId: 2 }));
    })
    getExtraboardExportDetailsAPI.mockRejectedValueOnce(error);

    await act(async () => {
      store.dispatch(planActions.exportExtraboardDetails({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P026", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getExtraboardDetails   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    getWorkRestProfilesExportDetailsAPI.mockResolvedValueOnce({});
    await act(async () => {
      store.dispatch(planActions.exportWorkRestProfileDetails({ planId: 2 }));

    })
    getExtraboardDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getExtraboardDetails({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P009", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing getExtraboard  functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getExtraboardAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.changeCrewProfileReportExpired({ planId: 2 }));
      store.dispatch(planActions.getExtraboard({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P008", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getIDPools   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };

    getIdPoolsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.changeCardedJobExpired({ planId: 2 }));
      store.dispatch(planActions.getIDPools({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P007", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getCardedJobDetails   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    getCardedJobDetailsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getCardedJobDetails({ planId: 2 }));
    })
    const data = { data: { status: 404 } };
    getCardedJobsExportDetailsAPI.mockResolvedValueOnce(data);
    await act(async () => {
      store.dispatch(planActions.exportCardedJobDetails({ planId: 2 }));

    })
    getCardedJobsExportDetailsAPI.mockResolvedValueOnce({});
    await act(async () => {
      store.dispatch(planActions.exportCardedJobDetails({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P031", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing getCardedJobs   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    getCardedJobsAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getCardedJobs({ planId: 2 }));
    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P030", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing savePlanRemovePool   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    await act(async () => {
      store.dispatch(planActions.savePlanRemovePool({ planId: 2 }));

    })
    planPoolRemoveSaveAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.getMasterPlansRequest());

      store.dispatch(planActions.savePlanRemovePool({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P010", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });
  it('Testing savePlanPool   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    planPoolSaveAPI.mockRejectedValueOnce(error);

    await act(async () => {
      store.dispatch(planActions.savePlanPool({ planId: 2 }));

    })

    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P006", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });

  it('Testing deletePlan   functionality if API fails', async () => {
    const spyNotifyAppRequest = jest.spyOn(appActions, 'notifyAppRequest');
    const error = { response: { status: 404 } };
    await act(async () => {
      store.dispatch(planActions.deletePlanRequest({ planId: 2 }));
      store.dispatch(planActions.changeCurrentPlanView({ planId: 2 }));

      store.dispatch(planActions.deletePlan({ planId: 2 }));
    })
    deletePlanAPI.mockRejectedValueOnce(error);
    await act(async () => {
      store.dispatch(planActions.deletePlan({ planId: 2 }));

    })
    expect(spyNotifyAppRequest).toHaveBeenCalledWith("CCP-P004", { "response": { "status": 404 } });
    spyNotifyAppRequest.mockRestore();
  });


})