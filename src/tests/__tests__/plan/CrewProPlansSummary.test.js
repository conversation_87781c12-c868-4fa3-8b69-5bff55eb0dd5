import { render, screen,fireEvent, act, cleanup } from '@testing-library/react';
import CrewProPlansSummary from '../../../plan/CrewProPlansSummary';
import {CREW_PRO_PLANS_SUMMARY_MSG} from "../../../config/ccpconstants"
import React from 'react';
import { Provider } from 'react-redux';
import { store } from "../../../app/store";


describe('Test CrewProPlansSummary Component', () => {
  beforeEach(() => {
  });
  afterEach(cleanup);


  it('Testing CrewProPlansSummary rendered correctly', async() => {
    render(<Provider store={store}><CrewProPlansSummary /></Provider>); 
   expect(screen.getByText(CREW_PRO_PLANS_SUMMARY_MSG)).toBeInTheDocument();
  });
  
  
});
