import { render, screen, fireEvent, act, within, cleanup } from '@testing-library/react';
import ExtraboardEditor from '../../../../plan/extraboard/ExtraboardEditor';
import { changeCurrentPlanViewData, getCrewProfileFilters } from '../../../../plan/planActions';
import React from 'react';
import { store } from "../../../../app/store";
jest.mock('popper.js');
jest.mock('../../../../config/API.js', () => require('../../../mocks/API.mock.js'));
jest.mock("ag-grid-react", () => ({
  AgGridReact: (params) => <div data-testid='aggridid' className='agGridClass'
    type="text"
  >
    {
      params.onGridReady && params.onGridReady({
        api: {
          deselectAll: jest.fn(), ensureIndexVisible: jest.fn(),
          forEachNode: jest.fn((callback) => {
            const mockNode = {
              data: { id: '', currRecord: true },
              setSelected: jest.fn(),
            }
            callback(mockNode); callback({ data: { id: 33 }, setSelected: jest.fn() })
          }),
          getSelectedRows: () => [],
          getSelectedNodes: jest.fn(),
          sizeColumnsToFit: jest.fn(),
          forEachLeafNode: jest.fn((callback) => {
            params.rowData && params.rowData.forEach((item) => {
              callback({
                data: item,
                setSelected: jest.fn(), parent: { id: 'ROOT_NODE_ID', setExpanded: jest.fn() }
              })
            })

          }),
          setQuickFilter: jest.fn(() => { }),
          ensureNodeVisible: jest.fn(),
          exportDataAsExcel: jest.fn(() => {

          }),
          setRowData: jest.fn()
        },
        columnApi: { autoSizeAllColumns: jest.fn(), ensureIndexVisible: jest.fn() }
      })}
    {params.onFirstDataRendered && params.onFirstDataRendered({
      api: { deselectAll: jest.fn(), forEachNode: ((node) => { return true }) },
      columnApi: { autoSizeAllColumns: jest.fn() }
    })}
    {params.onPaginationChanged && params.onPaginationChanged()}
    {params.rowData && params.rowData.map((item, index) => <div key={index} data-testid={`aggridid-${item.id}`}
      onClick={() => {
        const selectedData = {
          api: { getDisplayedRowAtIndex: jest.fn(() => { return { setSelected: jest.fn() } }), getSelectedRows: (() => [item]), getSelectedNodes: (() => [{ data: item }]) }, node: {
            selected: true
          }
        };
        params.onRowSelected && params.onRowSelected(selectedData);
        params.onSelectionChanged && params.onSelectionChanged(selectedData);
        params.autoGroupColumnDef && params.autoGroupColumnDef.valueGetter({ node: { data: { hierarchyName: 'upsidesummary' } } });
        params.getRowHeight && params.getRowHeight();
      }} >
      <span >{item.distr}</span> <span >{item.crewOrgnOs}</span>
      <span >{item.subdistr}</span><span >{item.pool}</span></div>)
    }
  </div>
}));

describe('Test ExtraboardEditor Component', () => {
  store.dispatch(changeCurrentPlanViewData({ currPlan: { planId: 'plan1', id: 'plan1' } }));
  store.dispatch(getCrewProfileFilters({ id: 1, planId: 'plan1' }));
  beforeEach(() => {
  });
  afterEach(cleanup);
  const handleSuccess = jest.fn(), handleClose = jest.fn();
  let props = {
    handleSuccess,
    handleClose,
    successLabel: 'Add',
    filters: { distrs: [{ distr: 'AA', subDistrs: ['SAAA', 'SABB', 'SACC'] }, { distr: 'BB', subDistrs: ['SBAA', 'SBBB', 'SBCC'] }] },
    osCodes: [{ label: 'OS1' }, { label: 'OS2' }, { label: 'OS3' }, { label: 'OS4' }],
    data: [{ distr: 'AA', subDistr: 'SAAA', name: 'EXB5' },
    { distr: 'BB', subDistr: 'SBAA', name: 'EXB2' }],
  }

  it('Testing ExtraboardEditor Add functionality', async () => {
    render(<ExtraboardEditor handleClose={handleClose} title={`Add Extraboard`} show={true}
      handleSuccess={handleSuccess} successLabel={`Add`}
      filters={props.filters}
      hubCodes={props.hubCodes}
      data={props.data} />);
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    const crewProf = screen.getByPlaceholderText('Extraboard');
    fireEvent.change(crewProf, { target: { value: 'EXB1' } })
    expect(screen.getByText('Add')).not.toBeDisabled();
    fireEvent.click(screen.getByText('Add'))
    expect(handleSuccess).toHaveBeenCalled();
  });
  it('Testing ExtraboardEditor hide functionality', async () => {

    render(<ExtraboardEditor handleClose={handleClose} title={`Add Extraboard`}
      handleSuccess={handleSuccess} successLabel={`Add`}
      filters={props.filters}
      hubCodes={props.hubCodes}
      data={props.data}
    />);
    expect(screen.queryByText('Add Extraboard')).not.toBeInTheDocument();
  });
  it('Testing ExtraboardEditor display error correctly', async () => {

    render(<ExtraboardEditor handleClose={handleClose} title={`Add Extraboard`} show={true}
      handleSuccess={handleSuccess} successLabel={`Add`}
      filters={props.filters}
      hubCodes={props.hubCodes}
      data={props.data} />);
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    const crewProf = screen.getByPlaceholderText('Extraboard');
    fireEvent.change(crewProf, { target: { value: 'EXB5' } });
    expect(screen.getByText('Error! Same record already exists.')).toBeInTheDocument();
  });

  it('Testing ExtraboardEditor display error for same record correctly', async () => {

    render(<ExtraboardEditor handleClose={handleClose} title={`Add Extraboard`} show={true}
      handleSuccess={handleSuccess} successLabel={`Add`}
      filters={props.filters}
      hubCodes={props.hubCodes}
      data={props.data} />);
    const crewProf = screen.getByPlaceholderText('Extraboard');
    fireEvent.change(crewProf, { target: { value: 'EXB5' } });
    const distr = screen.getByPlaceholderText('District');
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList = screen.getByRole("listbox");
    const optionItem = within(optionsList).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem);
    const subDistr = screen.getByPlaceholderText('Sub district');
    fireEvent.change(subDistr, { target: { value: 'SAAA' } })
    const optionsList1 = screen.getByRole("listbox");
    const optionItem1 = within(optionsList1).getByRole("option", { name: "SAAA" });
    fireEvent.click(optionItem1);
    fireEvent.change(distr, { target: { value: 'BB' } })
    const optionsList2 = screen.getByRole("listbox");
    const optionItem2 = within(optionsList2).getByRole("option", { name: "BB" });
    fireEvent.click(optionItem2);
    fireEvent.change(distr, { target: { value: 'AA' } })
    const optionsList3 = screen.getByRole("listbox");
    const optionItem3 = within(optionsList3).getByRole("option", { name: "AA" });
    fireEvent.click(optionItem3);
    expect(screen.getByText('Error! Same record already exists.')).toBeInTheDocument();
  });

});
