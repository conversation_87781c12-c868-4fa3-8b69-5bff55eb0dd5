import * as scenarioComparisionActions from "../../../scenariocomparison/scenarioComparisonActions";
import { cleanup } from "@testing-library/react";

import { getScenarioReportAPI, getScenariosAPI, getScenariosByStatusAPI, runSimulationAPI, cancelScenarioAPI } from "../../../config/API.js";
jest.mock('../../../config/API.js', () => require('../../mocks/API.mock.js'));

describe('Testing Scenario Comparision Actions', () => {
  afterEach(cleanup);

  it('Testing getReport functionality if API fails', async () => {
    const dispatch = jest.fn();
    const error = { response: { status: 404 } };
    getScenarioReportAPI.mockRejectedValueOnce(error);
    scenarioComparisionActions.getReport()(dispatch);
    expect(dispatch).toHaveBeenCalledTimes(1);
  });
  it('Testing getScenarios functionality if API fails', async () => {
    const dispatch = jest.fn();
    const error = { response: { status: 404 } };
    getScenariosAPI.mockRejectedValueOnce(error);

    scenarioComparisionActions.getScenarios()(dispatch);
    expect(dispatch).toHaveBeenCalledTimes(1);
  });
  it('Testing getScenariosFail functionality ', async () => {
    const retvalue = scenarioComparisionActions.getScenariosFail();
    expect(retvalue.type).toEqual('GET_SCENARIOS_FAIL');
  });
  it('Testing getReportFail functionality ', async () => {
    const retvalue = scenarioComparisionActions.getReportFail();
    expect(retvalue.type).toEqual('GET_REPORT_FAIL');

  });

})