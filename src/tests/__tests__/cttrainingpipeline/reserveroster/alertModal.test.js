import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import AlertModal from '../../../../cttrainingpipeline/reserveroster/alertModal';
import { toggleAlert } from '../../../../cttrainingpipeline/ctTraineeActions';

// Mock the ctTraineeActions module
jest.mock('../../../../cttrainingpipeline/ctTraineeActions', () => ({
    toggleAlert: jest.fn(() => ({ type: 'MOCK_TOGGLE_ALERT' }))
}));

// Configure mock store
const mockStore = configureStore([]);

describe('AlertModal', () => {
    let store;

    // Helper function to render the component with store
    const renderWithStore = (initialState) => {
        store = mockStore(initialState);
        return render(
            <Provider store={store}>
                <AlertModal />
            </Provider>
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders nothing when showAlert is false', () => {
        renderWithStore({
            cttp: {
                showAlert: false,
                successMessage: '',
                errorMessage: ''
            }
        });

        // The Modal should not be in the document when showAlert is false
        expect(screen.queryByText('Notification')).not.toBeInTheDocument();
    });

    it('renders the modal with success message when showAlert is true', () => {
        const successMessage = 'Operation successful!';
        renderWithStore({
            cttp: {
                showAlert: true,
                successMessage,
                errorMessage: ''
            }
        });

        // Check that the modal and success message are rendered
        expect(screen.getByText('Notification')).toBeInTheDocument();
        expect(screen.getByText(successMessage)).toBeInTheDocument();

        // Use getAllByText to find all elements with "Close" text
        const closeButtons = screen.getAllByText('Close');
        expect(closeButtons.length).toBeGreaterThan(0);
    });

    it('renders the modal with error message when showAlert is true', () => {
        const errorMessage = 'Operation failed!';
        renderWithStore({
            cttp: {
                showAlert: true,
                successMessage: '',
                errorMessage
            }
        });

        // Check that the modal and error message are rendered
        expect(screen.getByText('Notification')).toBeInTheDocument();
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('renders both success and error messages when both are present', () => {
        const successMessage = 'Operation partially successful!';
        const errorMessage = 'But some parts failed!';
        renderWithStore({
            cttp: {
                showAlert: true,
                successMessage,
                errorMessage
            }
        });

        // Check that the modal and both messages are rendered
        expect(screen.getByText('Notification')).toBeInTheDocument();
        expect(screen.getByText(successMessage)).toBeInTheDocument();
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('dispatches toggleAlert action when footer close button is clicked', () => {
        renderWithStore({
            cttp: {
                showAlert: true,
                successMessage: 'Success',
                errorMessage: ''
            }
        });

        // Get all buttons and use the one in the footer with class btn-secondary
        const footerCloseButton = screen.getAllByRole('button').find(
            button => button.classList.contains('btn-secondary')
        );
        fireEvent.click(footerCloseButton);

        // Verify that toggleAlert was called and the action was dispatched
        expect(toggleAlert).toHaveBeenCalled();
        expect(store.getActions()).toEqual([{ type: 'MOCK_TOGGLE_ALERT' }]);
    });

    it('dispatches toggleAlert action when the modal is closed via header X button', () => {
        renderWithStore({
            cttp: {
                showAlert: true,
                successMessage: 'Success',
                errorMessage: ''
            }
        });

        // Find and click the close button in the header (the "×" symbol)
        const closeButton = screen.getByText('×');
        fireEvent.click(closeButton);

        // Verify that toggleAlert was called and the action was dispatched
        expect(toggleAlert).toHaveBeenCalled();
        expect(store.getActions()).toEqual([{ type: 'MOCK_TOGGLE_ALERT' }]);
    });
});