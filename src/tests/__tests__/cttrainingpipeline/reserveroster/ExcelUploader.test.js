import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ExcelUploader from '../../../../cttrainingpipeline/reserveroster/ExcelUploader';
import { saveCtClass, saveCtClassLocation } from '../../../../cttrainingpipeline/ctClassesActions';

// Mock XLSX library
jest.mock('xlsx', () => ({
    read: jest.fn().mockReturnValue({
        SheetNames: ['Sheet1'],
        Sheets: {
            Sheet1: {}
        }
    }),
    utils: {
        sheet_to_json: jest.fn().mockReturnValue([
            ['', '', '', 44621, 44622, 44623], // Headers with dates
            ['', '', '', 20, 25, 30], // Total class capacities
            ['', '', '', 20, 25, 30], // Available class capacities
            ['Location A', '', '', 10, 12, 15], // Location A capacities
            ['Location B', '', '', 10, 13, 15] // Location B capacities
        ])
    },
    SSF: {
        format: jest.fn()
            .mockReturnValueOnce('2022-02-01')
            .mockReturnValueOnce('2022-02-02')
            .mockReturnValueOnce('2022-02-03')
    }
}));

// Mock FileReader
global.FileReader = class {
    constructor() {
        this.readAsArrayBuffer = jest.fn(() => {
            setTimeout(() => {
                this.onload({ target: { result: new ArrayBuffer(8) } });
            }, 0);
        });
    }
};

// Mock the actual actions themselves
jest.mock('../../../../cttrainingpipeline/ctClassesActions');

// Mock react-redux connect to avoid using store
jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    connect: () => (Component) => Component
}));

describe('ExcelUploader', () => {
    let file;
    let consoleErrorSpy;
    let mockSaveCtClass;
    let mockSaveCtClassLocation;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Create the mock implementations as functions that return promises
        mockSaveCtClass = jest.fn().mockImplementation((payload) => {
            return Promise.resolve(
                payload.map((item, index) => ({
                    ...item,
                    classId: index + 1
                }))
            );
        });

        mockSaveCtClassLocation = jest.fn().mockImplementation(() => {
            return Promise.resolve([]);
        });

        // Set the mocked implementations
        saveCtClass.mockImplementation(mockSaveCtClass);
        saveCtClassLocation.mockImplementation(mockSaveCtClassLocation);

        // Create a mock file
        file = new File(['dummy content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // Mock console.error to prevent test output noise
        consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        // Restore console.error
        consoleErrorSpy.mockRestore();
    });

    it('renders without crashing', () => {
        render(
            <ExcelUploader
                saveCtClass={mockSaveCtClass}
                saveCtClassLocation={mockSaveCtClassLocation}
            />
        );

        // Check that the component rendered correctly
        expect(screen.getByText('Upload Excel File')).toBeInTheDocument();

        // Find the file input
        const fileInputs = document.querySelectorAll('input[type="file"]');
        expect(fileInputs.length).toBe(1);
    });

    it('handles file upload and processes data correctly', (done) => {
        // Create onUploadSuccess callback that completes test
        const onUploadSuccess = jest.fn(() => {
            setTimeout(() => {
                try {
                    // Verify the functions were called
                    expect(mockSaveCtClass).toHaveBeenCalled();
                    expect(mockSaveCtClassLocation).toHaveBeenCalled();
                    done();
                } catch (err) {
                    done(err);
                }
            }, 0);
        });

        render(
            <ExcelUploader
                saveCtClass={mockSaveCtClass}
                saveCtClassLocation={mockSaveCtClassLocation}
                onUploadSuccess={onUploadSuccess}
            />
        );

        // Get the file input element
        const fileInput = document.querySelector('input[type="file"]');

        // Mock the file input change event
        Object.defineProperty(fileInput, 'files', {
            value: [file],
            writable: true
        });

        // Trigger the change event
        fireEvent.change(fileInput);
    });

    it('does not process data when no file is selected', () => {
        render(
            <ExcelUploader
                saveCtClass={mockSaveCtClass}
                saveCtClassLocation={mockSaveCtClassLocation}
            />
        );

        // Get the file input element
        const fileInput = document.querySelector('input[type="file"]');

        // Trigger file input change with no file
        fireEvent.change(fileInput, { target: { files: [] } });

        // No actions should be dispatched
        expect(mockSaveCtClass).not.toHaveBeenCalled();
        expect(mockSaveCtClassLocation).not.toHaveBeenCalled();
    });
});