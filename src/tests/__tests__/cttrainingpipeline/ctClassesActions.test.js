import * as actions from "../../../cttrainingpipeline/ctClassesActions";
import { cleanup } from "@testing-library/react";
import {
    getAllCtClassesAPI,
    ctClassesSaveAPI,
    ctClassDeleteAPI,
    getAllCtClassesLocationAPI,
    ctClassesLocationsSaveAPI,
    ctClassLocationsSaveAPI,
    ctClassLocationUpdateAPI,
    getAllCtLocaitonAPI
} from "../../../config/API";

jest.mock('../../../config/API');

describe('CT Classes Actions', () => {
    afterEach(cleanup);

    it('Testing getCtClasses functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        getAllCtClassesAPI.mockRejectedValueOnce(error);

        await actions.getCtClasses()(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(getAllCtClassesAPI).toHaveBeenCalled();
    });

    it('Testing getCtLocation functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        getAllCtLocaitonAPI.mockRejectedValueOnce(error);

        await actions.getCtLocation()(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(getAllCtLocaitonAPI).toHaveBeenCalled();
    });

    it('Testing saveCtClass functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        const mockPayload = { id: 1, name: 'Class 1' };

        ctClassesSaveAPI.mockRejectedValueOnce(error);

        try {
            await actions.saveCtClass(mockPayload)(dispatch);
        } catch (error) {
            // Expected to throw error
        }

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClass functionality with successful response', async () => {
        const dispatch = jest.fn();
        const mockPayload = { id: 1, name: 'Class 1' };
        const responseData = { success: true };

        ctClassesSaveAPI.mockResolvedValueOnce({
            status: 200,
            data: responseData
        });

        await actions.saveCtClass(mockPayload)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClass functionality with non-200 response', async () => {
        const dispatch = jest.fn();
        const mockPayload = { id: 1, name: 'Class 1' };

        ctClassesSaveAPI.mockResolvedValueOnce({
            status: 400,
            data: { error: 'Bad Request' }
        });

        try {
            await actions.saveCtClass(mockPayload)(dispatch);
        } catch (error) {
            // Expected to throw error
        }

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing deleteCtClass functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        const mockId = 1;

        ctClassDeleteAPI.mockRejectedValueOnce(error);

        await actions.deleteCtClass(mockId)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassDeleteAPI).toHaveBeenCalledWith(mockId);
    });

    it('Testing deleteCtClass functionality with non-ok response', async () => {
        const dispatch = jest.fn();
        const mockId = 1;

        ctClassDeleteAPI.mockResolvedValueOnce({
            ok: false
        });

        await actions.deleteCtClass(mockId)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassDeleteAPI).toHaveBeenCalledWith(mockId);
    });

    it('Testing getCtClassesLocation functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };

        getAllCtClassesLocationAPI.mockRejectedValueOnce(error);

        await actions.getCtClassesLocation()(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(getAllCtClassesLocationAPI).toHaveBeenCalled();
    });

    it('Testing saveCtClassLocation functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        const mockPayload = { id: 1, name: 'Location 1' };

        ctClassesLocationsSaveAPI.mockRejectedValueOnce(error);

        try {
            await actions.saveCtClassLocation(mockPayload)(dispatch);
        } catch (error) {
            // Expected to throw error
        }

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesLocationsSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClassLocation functionality with successful response', async () => {
        const dispatch = jest.fn();
        const mockPayload = { id: 1, name: 'Location 1' };
        const responseData = { success: true };

        ctClassesLocationsSaveAPI.mockResolvedValueOnce({
            status: 200,
            data: responseData
        });

        await actions.saveCtClassLocation(mockPayload)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesLocationsSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClassLocation functionality with non-200 response', async () => {
        const dispatch = jest.fn();
        const mockPayload = { id: 1, name: 'Location 1' };

        ctClassesLocationsSaveAPI.mockResolvedValueOnce({
            status: 400,
            data: { error: 'Bad Request' }
        });

        try {
            await actions.saveCtClassLocation(mockPayload)(dispatch);
        } catch (error) {
            // Expected to throw error
        }

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassesLocationsSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClassesLocation functionality if API fails', async () => {
        const dispatch = jest.fn();
        const error = { response: { status: 404 } };
        const mockPayload = { id: 1, name: 'Locations' };

        ctClassLocationsSaveAPI.mockRejectedValueOnce(error);

        await actions.saveCtClassesLocation(mockPayload)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassLocationsSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });

    it('Testing saveCtClassesLocation functionality with non-200 response', async () => {
        const dispatch = jest.fn();
        const mockPayload = { id: 1, name: 'Locations' };

        ctClassLocationsSaveAPI.mockResolvedValueOnce({
            status: 400,
            data: { error: 'Bad Request' }
        });

        await actions.saveCtClassesLocation(mockPayload)(dispatch);

        expect(dispatch).toHaveBeenCalled();
        expect(ctClassLocationsSaveAPI).toHaveBeenCalledWith(JSON.stringify(mockPayload));
    });


});