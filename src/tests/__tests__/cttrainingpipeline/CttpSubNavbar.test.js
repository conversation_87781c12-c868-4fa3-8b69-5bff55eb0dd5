import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CttpSubNavbar from '../../../cttrainingpipeline/CttpSubNavbar';

describe('CttpSubNavbar', () => {
    it('renders without crashing', () => {
        render(<CttpSubNavbar />);
        expect(screen.getByText('Reserve Roster')).toBeInTheDocument();
        expect(screen.getByText('Training Guide')).toBeInTheDocument();
    });

    it('calls onSelect prop when a tab is clicked', () => {
        const onSelectMock = jest.fn();
        render(<CttpSubNavbar onSelect={onSelectMock} />);

        fireEvent.click(screen.getByText('Reserve Roster'));
        // expect(onSelectMock).toHaveBeenCalledWith('reserveRoster');
        expect(onSelectMock.mock.calls[0][0]).toBe('reserveRoster');

        fireEvent.click(screen.getByText('Training Guide'));
        expect(onSelectMock.mock.calls[1][0]).toBe('help');
    });

    it('renders all available tabs', () => {
        render(<CttpSubNavbar />);

        // Check that the uncommented tabs are present
        expect(screen.getByText('Reserve Roster')).toBeInTheDocument();
        expect(screen.getByText('Training Guide')).toBeInTheDocument();

        // Check that commented out tabs are not present
        expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
        expect(screen.queryByText('Training Center')).not.toBeInTheDocument();
        expect(screen.queryByText('Input Settings')).not.toBeInTheDocument();
        expect(screen.queryByText('Classes')).not.toBeInTheDocument();
    });
});