/* eslint eqeqeq: "off"*/
import React from "react";
import {Button, Col, Form, Modal, OverlayTrigger, Row, Tooltip} from "react-bootstrap";
import {
    LOG_LEVELS,
    POOL_SIZE_TYPES,
    RESET_INTERNAL_SETTINGS,
    YES_NO_TYPES
} from "../config/ccpconstants";
import {changeInternalSetting, resetInternalSettings,} from "./runSimulationActions";
import {connect} from "react-redux";

class InternalSettings extends React.Component {
    handleChange = (field, newValue) => {
        this.props.changeInternalSetting({key: field, value: newValue})
    }
    resetSettings = () => {
        this.props.resetInternalSettings();
    }

    render() {
        const {internalSettings: is = {}} = this.props.simulation || {};
        return (
            <Form
                className="font-weight-600 font-size-small"
                style={{margin: "1rem 0rem 0rem 0rem"}}>
                <Modal.Dialog style={{maxWidth: "100%", margin: "0.25rem 0rem 0.25rem 0rem"}}>
                    <Modal.Header className="settings-model-header">
                        <Modal.Title className="settings-model-title">Pool Size</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form.Row>
                            <Form.Group as={Col} sm="4" controlId="poolSettings1">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Maximum number of threads to use for pool sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of threads to use for pool sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            className=" max-width-100 "
                                            value={is.poolSizingMaxThreads ? is.poolSizingMaxThreads : ''}
                                            min={10} max={100}
                                            onChange={(event) => this.handleChange("poolSizingMaxThreads", event.target.value)}
                                            controlId="poolSizingMaxThreads"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Number of runs per cycle for extraboard sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Number of runs for pool sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingNumRuns ? is.poolSizingNumRuns : ''}
                                            onChange={(event) => this.handleChange("poolSizingNumRuns", event.target.value)}
                                            controlId="poolSizingNumRuns"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Comma-delimited list of integer random number seeds for pool sizing.
                                                    Seed amount must be equal to `Pool Sizing Number of Runs`.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Comma-delimited list of integer random number seeds for pool
                                                sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="text"
                                            value={is.poolSizingRandomSeeds ? is.poolSizingRandomSeeds : ''}
                                            onChange={(event) => this.handleChange("poolSizingRandomSeeds", event.target.value)}
                                            controlId="poolSizingRandomSeeds"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Default CO break-even point.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Default CO break-even point:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            value={is.poolSizingDefaultCoBep ? is.poolSizingDefaultCoBep : ''}
                                            onChange={(event) => this.handleChange("poolSizingDefaultCoBep", event.target.value)}
                                            step="1"
                                            controlId="poolSizingDefaultCoBep"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Default EN break-even point.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Default EN break-even point:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingDefaultEnBep ? is.poolSizingDefaultEnBep : ''}
                                            onChange={(event) => this.handleChange("poolSizingDefaultEnBep", event.target.value)}
                                            controlId="poolSizingDefaultEnBep"
                                        />
                                    </Col>
                                </Row>

                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="poolSettings2">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Minimum direction density.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Minimum direction density:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingDirectionDensityThreshold ? is.poolSizingDirectionDensityThreshold : ''}
                                            onChange={(event) => this.handleChange("poolSizingDirectionDensityThreshold", event.target.value)}
                                            controlId="poolSizingDirectionDensityThreshold"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Take 'Close-enough' value for comparing weights.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Take 'Close-enough' value for comparing weights:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingEpsilon ? is.poolSizingEpsilon : ''}
                                            onChange={(event) => this.handleChange("poolSizingEpsilon", event.target.value)}
                                            controlId="poolSizingEpsilon"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>
                                                    Initial CO upper bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial CO upper bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingMaxCo ? is.poolSizingMaxCo : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxCo", event.target.value)}
                                            controlId="poolSizingMaxCo"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Max. total delay due to CO turn shortage in unit of
                                                    "weight",<br/> for
                                                    example, 0.1 in weight = 0.1*cancellation duration (48h) = 4.8h,
                                                    i.e.
                                                    the max. allowed total delay is 4.8 hours.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum CO weight:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={0.1}
                                            value={is.poolSizingMaxCoWeight ? is.poolSizingMaxCoWeight : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxCoWeight", event.target.value)}
                                            controlId="poolSizingMaxCoWeight"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial EN upper bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial EN upper bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>

                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingMaxEn ? is.poolSizingMaxEn : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxEn", event.target.value)}
                                            controlId="poolSizingMaxEn"
                                        />
                                    </Col>
                                </Row>

                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="poolSettings3">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Max. total delay due to EN turn shortage in unit of
                                                    "weight",<br/> for
                                                    example, 0.1 in weight = 0.1*cancellation duration (48h) = 4.8h,
                                                    i.e.
                                                    the max. allowed total delay is 4.8 hours. .
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum EN weight:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={0.1}
                                            value={is.poolSizingMaxEnWeight ? is.poolSizingMaxEnWeight : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxEnWeight", event.target.value)}
                                            controlId="poolSizingMaxEnWeight"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Maximum number of seconds to run pool sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of seconds to run pool sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingMaxSeconds ? is.poolSizingMaxSeconds : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxSeconds", event.target.value)}
                                            controlId="poolSizingMaxSeconds"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial CO lower bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial CO lower bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingMinCo ? is.poolSizingMinCo : ''}
                                            onChange={(event) => this.handleChange("poolSizingMinCo", event.target.value)}
                                            controlId="poolSizingMinCo"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial EN lower bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial EN lower bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.poolSizingMinEn ? is.poolSizingMinEn : ''}
                                            onChange={(event) => this.handleChange("poolSizingMinEn", event.target.value)}
                                            controlId="poolSizingMinEn"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Maximum number of cycles for pool sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of cycles for pool sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col>
                                        <Form.Control
                                            type="number"
                                            min="1"
                                            step={1}
                                            value={is.poolSizingMaxCycles ? is.poolSizingMaxCycles : ''}
                                            onChange={(event) => this.handleChange("poolSizingMaxCycles", event.target.value)}
                                            controlId="poolSizingMaxCycles"
                                        />
                                        <Form.Control.Feedback type="invalid">Looks good!</Form.Control.Feedback>
                                    </Col>
                                </Row>
                            </Form.Group>
                        </Form.Row>
                    </Modal.Body>
                </Modal.Dialog>
                <Modal.Dialog style={{maxWidth: "100%", margin: "0.25rem 0rem 0.25rem 0rem"}}>
                    <Modal.Header className="settings-model-header">
                        <Modal.Title className="settings-model-title">Extra Board Size
                        </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form.Row>
                            <Form.Group as={Col} md="4" controlId="extraboardSizeSettings1">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Default CO break-even point.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Default CO break-even point:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingDefaultCoBep ? is.exbSizingDefaultCoBep : ''}
                                            onChange={(event) => this.handleChange("exbSizingDefaultCoBep", event.target.value)}
                                            controlId="exbSizingDefaultCoBep"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Default EN break-even point.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Default EN break-even point:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingDefaultEnBep ? is.exbSizingDefaultEnBep : ''}
                                            onChange={(event) => this.handleChange("exbSizingDefaultEnBep", event.target.value)}
                                            controlId="exbSizingDefaultEnBep"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Minimum direction density to determine UP/DOWN/STAY/BACK.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Minimum direction density:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>

                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingDirectionDensityThreshold ? is.exbSizingDirectionDensityThreshold : ''}
                                            onChange={(event) => this.handleChange("exbSizingDirectionDensityThreshold", event.target.value)}
                                            controlId="exbSizingDirectionDensityThreshold"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>'Close-enough' value for comparing weights.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>'Close-enough' value for comparing weights:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingEpsilon ? is.exbSizingEpsilon : ''}
                                            onChange={(event) => this.handleChange("exbSizingEpsilon", event.target.value)}
                                            controlId="exbSizingEpsilon"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial CO upper bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial CO upper bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMaxCo ? is.exbSizingMaxCo : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxCo", event.target.value)}
                                            controlId="exbSizingMaxCo"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial EN lower bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial EN lower bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMinEn ? is.exbSizingMinEn : ''}
                                            onChange={(event) => this.handleChange("exbSizingMinEn", event.target.value)}
                                            controlId="exbSizingMinEn"
                                        />
                                    </Col>
                                </Row>
                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="extraboardSizeSettings2">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Max. total delay due to CO exb shortage in unit of "weight", for
                                                    example,
                                                    0.1 in weight = 0.1*cancellation duration (48h) = 4.8h, <br/>i.e.
                                                    the
                                                    max. allowed total delay is 4.8 hours. .
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum CO weight:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={0.1}
                                            value={is.exbSizingMaxCoWeight ? is.exbSizingMaxCoWeight : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxCoWeight", event.target.value)}
                                            controlId="exbSizingMaxCoWeight"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial EN upper bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial EN upper bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMaxEn ? is.exbSizingMaxEn : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxEn", event.target.value)}
                                            controlId="exbSizingMaxEn"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Max. total delay due to EN exb shortage in unit of "weight",<br/> for
                                                    example, 0.1 in weight = 0.1*cancellation duration (48h) = 4.8h,
                                                    i.e.
                                                    the max. allowed total delay is 4.8 hours.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum EN weight:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>

                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={0.1}
                                            value={is.exbSizingMaxEnWeight ? is.exbSizingMaxEnWeight : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxEnWeight", event.target.value)}
                                            controlId="exbSizingMaxEnWeight"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Maximum number of seconds to run extraboard sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of seconds to run extraboard sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMaxSeconds ? is.exbSizingMaxSeconds : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxSeconds", event.target.value)}
                                            controlId="exbSizingMaxSeconds"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Maximum number of threads to use for extraboard sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of threads to use for extraboard
                                                sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMaxThreads ? is.exbSizingMaxThreads : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxThreads", event.target.value)}
                                            controlId="exbSizingMaxThreads"
                                        />
                                    </Col>
                                </Row>
                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="extraboardSizeSettings3">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Initial CO lower bound.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Initial CO lower bound:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMinCo ? is.exbSizingMinCo : ''}
                                            onChange={(event) => this.handleChange("exbSizingMinCo", event.target.value)}
                                            controlId="exbSizingMinCo"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Number of runs per cycle for extraboard sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Number of runs for extraboard sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingNumRuns ? is.exbSizingNumRuns : ''}
                                            onChange={(event) => this.handleChange("exbSizingNumRuns", event.target.value)}
                                            controlId="exbSizingNumRuns"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Comma-delimeted list of integer random number seeds for extraboard
                                                    sizing. Seed amount must be equal to exbSizingNumRuns.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Comma-delimeted list of integer random number seeds for
                                                extraboard
                                                sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="text"
                                            value={is.exbSizingRandomSeeds ? is.exbSizingRandomSeeds : ''}
                                            onChange={(event) => this.handleChange("exbSizingRandomSeeds", event.target.value)}
                                            controlId="exbSizingRandomSeeds"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Maximum number of cycles for extraboard sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Maximum number of cycles for extraboard sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.exbSizingMaxCycles ? is.exbSizingMaxCycles : ''}
                                            onChange={(event) => this.handleChange("exbSizingMaxCycles", event.target.value)}
                                            controlId="exbSizingMaxCycles"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>use orginal or optimized exb sizing.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Pool sizes for extraboard sizing:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            as="select" controlId="exbSizingUsesOriginalPoolSizes"
                                            defaultValue={is.exbSizingUsesOriginalPoolSizes}
                                            onChange={(event) => this.handleChange("exbSizingUsesOriginalPoolSizes", event.target.selectedOptions[0].id)}>
                                            {POOL_SIZE_TYPES.map((option) => <option id={option.key} key={option.key}
                                                                                     selected={option.key == (is.exbSizingUsesOriginalPoolSizes !== undefined ? is.exbSizingUsesOriginalPoolSizes : option.default && option.key)}

                                            >{option.desc}
                                            </option>)}
                                            ]
                                        </Form.Control>
                                    </Col>
                                </Row>
                            </Form.Group>
                        </Form.Row>
                    </Modal.Body>
                </Modal.Dialog>
                <Modal.Dialog style={{maxWidth: "100%", margin: "0.25rem 0rem 0.25rem 0rem"}}>
                    <Modal.Header className="settings-model-header">
                        <Modal.Title className="settings-model-title">Log
                        </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form.Row>
                            <Form.Group as={Col} sm="4" controlId="logSettings">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Log Level.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Log Level:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            as="select" controlId="logLevel"
                                            onChange={(event) => this.handleChange("logLevel", event.target.selectedOptions[0].id)}>
                                            {LOG_LEVELS.map((option) => <option id={option.key} key={option.key}
                                                                                selected={option.key == (is.logLevel !== undefined ? is.logLevel : option.default && option.key)}
                                            >{option.desc}
                                            </option>)}
                                            ]
                                        </Form.Control>
                                        <Form.Control.Feedback type="invalid">Looks good!</Form.Control.Feedback>
                                    </Col>
                                </Row>
                            </Form.Group>
                            <Form.Group as={Col} sm="4" controlId="logSettings2">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Per-entity log files.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Per-entity log files:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            as="select" controlId="perEntityLoggingEnabled"
                                            defaultValue={is.perEntityLoggingEnabled}
                                            onChange={(event) => this.handleChange("perEntityLoggingEnabled", event.target.selectedOptions[0].id)}>
                                            {YES_NO_TYPES.map((option) => <option id={option.key} key={option.key}
                                                                                  selected={option.key == (is.perEntityLoggingEnabled !== undefined ? is.perEntityLoggingEnabled : option.default && option.key)}

                                            >{option.desc}
                                            </option>)}
                                            ]
                                        </Form.Control>
                                    </Col>
                                </Row>
                            </Form.Group>
                        </Form.Row>
                    </Modal.Body>
                </Modal.Dialog>
                <Modal.Dialog style={{maxWidth: "100%", margin: "0.25rem 0rem 0.25rem 0rem"}}>
                    <Modal.Header className="settings-model-header">
                        <Modal.Title className="settings-model-title">Other
                        </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form.Row>
                            <Form.Group as={Col} md="4" controlId="other1">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Commute percentage.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Commute percentage:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.commutePctg ? is.commutePctg : ''}
                                            onChange={(event) => this.handleChange("commutePctg", event.target.value)}
                                            controlId="commutePctg"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Active districts (comma-delimeted list).
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Active districts (comma-delimeted list):</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="text"
                                            value={is.districts ? is.districts : ''}
                                            onChange={(event) => this.handleChange("districts", event.target.value)}
                                            controlId="districts"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Num location updates.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Num location updates:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.locationUpdate ? is.locationUpdate : ''}
                                            onChange={(event) => this.handleChange("locationUpdate", event.target.value)}
                                            controlId="locationUpdate"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Max assignment percentage.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Max assignment percentage:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.maxPctgAssgn ? is.maxPctgAssgn : ''}
                                            onChange={(event) => this.handleChange("maxPctgAssgn", event.target.value)}
                                            controlId="maxPctgAssgn"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Disable deadhead flip
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Disable deadhead flip:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Check
                                            type="checkbox"
                                            checked={is.deadheadFlipDisabled ? is.deadheadFlipDisabled : false}
                                            onChange={(event) => this.handleChange("deadheadFlipDisabled", event.target.checked ? 1 : 0)}
                                            id={`dh-flip`}
                                            style={{fontWeight:"bold"}}
                                        >
                                        </Form.Check>
                                    </Col>
                                </Row>
                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="other2">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Detailed entity tracking.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Detailed entity tracking:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            as="select" controlId="detailedTrackingEnabled"
                                            defaultValue={is.detailedTrackingEnabled}
                                            onChange={(event) => this.handleChange("detailedTrackingEnabled", event.target.selectedOptions[0].id)}>
                                            {YES_NO_TYPES.map((option) => <option id={option.key} key={option.key}
                                                                                  selected={option.key == (is.detailedTrackingEnabled !== undefined ? is.detailedTrackingEnabled : option.default && option.key)}

                                            >{option.desc}
                                            </option>)}
                                            ]
                                        </Form.Control>
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Rescue update minutes.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Rescue update minutes:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.rescueUpdateM ? is.rescueUpdateM : ''}
                                            onChange={(event) => this.handleChange("rescueUpdateM", event.target.value)}
                                            controlId="rescueUpdateM"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Random delay percentage.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Random delay percentage:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.rndDelayPctg ? is.rndDelayPctg : ''}
                                            onChange={(event) => this.handleChange("rndDelayPctg", event.target.value)}
                                            controlId="rndDelayPctg"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Random on-duty percentage.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Random on-duty percentage:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            step={1}
                                            value={is.rndOnDutyPctg ? is.rndOnDutyPctg : ''}
                                            onChange={(event) => this.handleChange("rndOnDutyPctg", event.target.value)}
                                            controlId="rndOnDutyPctg"
                                        />
                                    </Col>
                                </Row>
                            </Form.Group>
                            <Form.Group as={Col} md="4" controlId="other3">
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Run segments.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Run segments:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            as="select" controlId="runSegments"
                                            defaultValue={is.runSegments}
                                            onChange={(event) => this.handleChange("runSegments", event.target.selectedOptions[0].id)}
                                        >
                                            {YES_NO_TYPES.map((option) => <option id={option.key} key={option.key}
                                                                                  selected={option.key == (is.runSegments !== undefined ? is.runSegments : option.default && option.key)}

                                            >{option.desc}
                                            </option>)}
                                            ]
                                        </Form.Control>
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Only execute on the servant with the specified ID.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Only execute on the servant with the specified ID:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            value={is.servantId ? is.servantId : ''}
                                            step={1}
                                            onChange={(event) => this.handleChange("servantId", event.target.value)}
                                            controlId="servantId"
                                        />
                                    </Col>
                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Start days.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Start days:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            min="0"
                                            step="1"
                                            value={is.startD ? is.startD : ''}
                                            onChange={(event) => this.handleChange("startD", event.target.value)}
                                            controlId="startD"
                                        />
                                    </Col>

                                </Row>
                                <Row className="margin-top margin-bottom">
                                    <Col sm={8}>
                                        <OverlayTrigger
                                            key="bottom"
                                            placement="bottom"
                                            overlay={
                                                <Tooltip id={`tooltip-bottom`} style={{
                                                    backgroundColor: "white", color: "green !important"
                                                }}>Stop days.
                                                </Tooltip>
                                            }
                                        >
                                            <Form.Label>Stop days:</Form.Label>
                                        </OverlayTrigger>
                                    </Col>
                                    <Col sm={4}>
                                        <Form.Control
                                            type="number"
                                            value={is.stopD ? is.stopD : ''}
                                            min="0"
                                            step="1"
                                            onChange={(event) => this.handleChange("stopD", event.target.value)}
                                            controlId="stopD"
                                        />
                                    </Col>
                                </Row>
                            </Form.Group>
                        </Form.Row>
                    </Modal.Body>
                </Modal.Dialog>
                <Form.Row style={{marginBottom: "1rem"}} className="action-bar ccp-form">
                    <OverlayTrigger
                        placement="bottom"
                        overlay={<Tooltip id={`tooltip-bottom`}>
                            {RESET_INTERNAL_SETTINGS}
                        </Tooltip>}
                    >
                        <Button variant="warning" onClick={this.resetSettings}>Reset internal settings</Button>

                    </OverlayTrigger>
                </Form.Row>
            </Form>
        );
    }
}

function mapStateToProps(state) {
    const {
        simulation,
    } = state.runSimulation;

    return {
        simulation,
    }
}

const mapDispatchToProps = {
    changeInternalSetting,
    resetInternalSettings
}
export default connect(mapStateToProps, mapDispatchToProps)(InternalSettings)
export {InternalSettings};
