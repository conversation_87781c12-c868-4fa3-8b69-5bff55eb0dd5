.font-caps {
    text-align: right;
    font-size: x-large;
    font-weight: bolder;
    color: white;
    font-variant: petite-caps;
    background-image: url('/assets/images/running_horses_small.gif');
    background-repeat: no-repeat;
    background-position: right;
}
.header-content {
    text-align: right;
    font-size: x-large;
    font-weight: bolder;
    color: white;
    background-image: url('/assets/images/running_horses_small.gif');
    background-repeat: no-repeat;
    background-position: right;
    margin: 0;

}
.header-logo {
    text-align: left;
    font-size: x-large;
    font-weight: bolder;
    color: white;
    background-image: url('/assets/images/NSlogo.gif');
    background-repeat: no-repeat;
    background-position: left;
    margin: 0 0 0 1rem;

}

.ns-bg-color {
    background-color: rgb(2, 81, 234);
}

.menu-button-selected {
    color:#000 !important;
    padding: 0 5px 0 5px !important;
    background-color: white !important;
    border-radius: unset !important;
    border-color:#888C96 !important;
    font-weight: 600 !important;
    border-bottom: none !important;
    margin-bottom: -4px !important

}
.menu-button {
    color:#000 !important;
    padding: 0 5px 0 5px !important;
    background-color: #e1e1e1 !important;
    border-radius: unset !important;
    border-color:#888C96 !important;
    font-weight: 600 !important;
}
.menu-row {
    margin-right: 0 !important;
    background-color:#FBFBFB;
    border: 1px solid;
    border-right: 1px light;
}
.center-panel{
    width: 40em;
    margin: auto;
    padding: 2em;
}
.margin-image {
    /*width: 3em;*/
    /*height: 2em;*/
    padding: 6px 6px 6px 10px;

}
.label-img {
    padding: 2px;
}
.margin-text {
    display: table-row-group;
}

.margin-top {
    margin-top: .125rem;
}

.margin-bottom {
    margin-bottom: 0.125rem;
}

.alert {
    display: table-row-group;
}

.petite-caps-large-text{
    padding: inherit;
    font-size: large;
    font-variant: petite-caps;
    color: aliceblue;

}

.large-text{
    padding: inherit;
    font-size: large;
    color: black;

}
.petite-caps-text{
    font-variant: petite-caps;
}

.div-33vh{
    height: 33vh;

}
.div-50vh{
    height: 50vh;

}
.div-60vh{
    height: 60vh;

}
.div-40vh{
    height: 40vh;

}
.div-45vh{
    height: 45vh;

}
.div-20vh{
    height: 20vh;

}
.div-15vh{
    height: 15vh;

}
.div-70vh{
    height: 70vh;

}
.padding-1rem{
    padding: 1rem;
}
.padding-1by4rem {
    padding: 0.25rem;
}
.padding-1by2rem{
    padding: 0.5rem;
}

.padding-top-1rem{
    padding-top: 1rem;
}
.padding-left-1rem{
    padding:unset !important;
    padding-left: 1rem !important;
}

.header-banner {
    height: 45px;
    background-color: #0251EA;
    background-repeat: initial;
    background-size: cover;
}

.modal-container {
    width: 100%;
}
.modal-alert {
    width: 100%;
    padding-right: 0rem;
}
.modal-alert.model-content {
background-color: unset !important;
border: 0rem !important;
}
.modal-dialog.modal-alert {
    max-width: 37rem;
}
.max-width-100 {
max-width:100%;
}

.width-50-pct {
width: 50% !important;
}

.runSimulationSectionHeading
{
text-decoration: underline;
color: #004AE3;
}
.runSimulationItem
{
padding: 0.125em;
}


.tile-container {
border: 1px solid #dee2e6;
border-radius: .25rem;
margin: .25rem 0 0.25rem 0;
}
/*.rstm-tree-item--active {*/
/*color: white;*/
/*background: #179ed3;*/
/*border-bottom: none;*/
/*}*/
/*.rstm-tree-item--focused {*/
/*box-shadow: none    ;*/
/*z-index: 999;*/
/*}*/

.font-weight-600 {
font-weight: 600 !important;
}

.font-weight-600 {
font-weight: 600 !important;
}
.settings-model-header {
padding:unset !important;
padding-left: 1rem !important;
}
.model-header {
   /* margin-bottom: 0;
    line-height: 1.5;
    font-size: 1.5rem;*/
    /*font-weight: bold;*/
}
.model-header.nav-link:active {
    color: darkblue;
}
.lsLayer{

}
.lsLayer:hover path {
stroke: #6e1313 !important;
opacity: unset !important;
stroke-width: 10px !important;


/*
 outline: 10px dashed red;
border: 20px solid red !important;
*/

}

.map-line-no-selection:hover path {
    stroke: red;
}
.settings-model-title {
    color: darkblue !important;
    font-weight:bold !important;
    font-size: medium !important;
}
.font-size-small {
    font-size: small !important;
}
.modal-xxl {
    max-width: 90% !important;
}

.modal-alert {
    margin: 1rem 1rem auto auto !important;
}

.spinner {
    position: fixed !important;
    z-index: 1 !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 50px !important;
    height: 50px !important;
    margin: auto !important;
}

.display-none {
    display: none;
}


.action-bar{
    margin-top: 1rem;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
}

.footer-center{
    justify-content: center !important;
}
.action-bar > button {
    margin-right:0.25rem;
    margin-left:0.25rem;

}


/*over ride*/



/*
#tooltip-bottom > .tooltip-inner {
    background-color: #fff;
    color: #000;
    !*
    border: 1px solid #062e56;
    *!
}

#tooltip-bottom > .tooltip-arrow  {
    background-color: #fff;
    color: #000;
    !*
    border: 1px solid #062e56;
    *!
}
*/


html,body{
    height:100%;
}

#wrapper{
    position:relative;
    /*background:#333;*/
    height:100%;
}

/*.profile-main-loader{*/
/*    left: 50% !important;*/
/*    margin-left:-100px;*/
/*    position: fixed !important;*/
/*    top: 50% !important;*/
/*    margin-top: -100px;*/
/*    width: 45px;*/
/*    z-index: 9000 !important;*/
/*}*/

/*.profile-main-loader .loader {*/
/*    position: relative;*/
/*    margin: 0px auto;*/
/*    width: 100px;*/
/*    height:100px;*/
/*}*/
/*.profile-main-loader .loader:before {*/
/*    content: '';*/
/*    display: block;*/
/*    padding-top: 100%;*/
/*}*/

/*.circular-loader {*/
/*    -webkit-animation: rotate 5s linear infinite;*/
/*    animation: rotate 5s linear infinite;*/
/*    height: 50%;*/
/*    -webkit-transform-origin: center center;*/
/*    -ms-transform-origin: center center;*/
/*    transform-origin: center center;*/
/*    width: 50%;*/
/*    position: absolute;*/
/*    top: 0;*/
/*    left: 0;*/
/*    margin: auto;*/
/*}*/

/*.loader-path {*/
/*    stroke-dasharray: 150,200;*/
/*    stroke-dashoffset: -10;*/
/*    -webkit-animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;*/
/*    animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;*/
/*    stroke-linecap: round;*/
/*}*/

/*@-webkit-keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-webkit-keyframes dash {
    0% {
        stroke-dasharray: 1,200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89,200;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 89,200;
        stroke-dashoffset: -124;
    }
}
@keyframes dash {
    0% {
        stroke-dasharray: 1,200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89,200;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 89,200;
        stroke-dashoffset: -124;
    }
}
@-webkit-keyframes color {
    0% {
        stroke: #70c542;
    }
    40% {
        stroke: #70c542;
    }
    66% {
        stroke: #70c542;
    }
    80%, 90% {
        stroke: #70c542;
    }
}
@keyframes color {
    0% {
        stroke: blue;
    }
    40% {
        stroke: blue;
    }
    66% {
        stroke: blue;
    }
    80%, 90% {
        stroke: blue;
    }
}*/
/**/
.folder {
    background: url('/assets/images/folder.svg') no-repeat center center;
    background-size: cover;

}
.folder-special {
    background: url('/assets/images/folder_special.svg') no-repeat center center;
    background-size: cover;

}
.folder-shared {
    background: url('/assets/images/folder_shared.svg') no-repeat center center;
    background-size: cover;

}
.icon-spl {
    height: 13px;
    width: 18px;
    display: inline-block;
}
.report {
    background: url('/assets/images/report.png') no-repeat center center;
    background-size: cover;
}
.copy-study {
    background: url('/assets/images/cloneStudy.png') no-repeat center center;
    background-size: cover;
}

.create-study {
    background: url('/assets/images/createStudy.png') no-repeat center center;
    background-size: cover;
}

.copy-plan {
    background: url('/assets/images/clonePlan.png') no-repeat center center;
    background-size: cover;
}

.plan-editor {
    background: url('/assets/images/editorPlan.png') no-repeat center center;
    background-size: cover;
}

.icon {
    height: 14px;
    width: 16px;
    display: inline-block;
}
.filename {
    padding: 5px;
    color: black;
    font-size: 16px;
}
.example-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/*split pane*/
.width-50 {
    width: 50px !important;
}
.width-300 {
    width: 300px !important;
}

.Resizer.disabled {
    cursor: auto !important;
}
/*split pane*/
/*
.map-container{
    width:1200px;
    height:1000px;
    overflow: auto;
    margin-top:10px;
}

.map-panel{
    top:40px;
    width:1100px;
    height:870px;
    position: absolute;
    !* border: 1px solid #CCCCCC;*!
}*/


/***/
.test{
    display: grid;
    align-content: center;
    text-align: left;
}



/* resize styles*/
.Resizer {
    background: #000;
    opacity: 0.2;
    z-index: 1;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -moz-background-clip: padding;
    -webkit-background-clip: padding;
    background-clip: padding-box;
}

.Resizer:hover {
    -webkit-transition: all 2s ease;
    transition: all 2s ease;
}

.Resizer.horizontal {
    height: 11px;
    margin: -5px 0;
    border-top: 5px solid rgba(255, 255, 255, 0);
    border-bottom: 5px solid rgba(255, 255, 255, 0);
    cursor: row-resize;
    width: 100%;
}

.Resizer.horizontal:hover {
    border-top: 5px solid rgba(0, 0, 0, 0.5);
    border-bottom: 5px solid rgba(0, 0, 0, 0.5);
}

.Resizer.vertical {
    width: 11px;
    margin: 0 -5px;
    border-left: 5px solid rgba(255, 255, 255, 0);
    border-right: 5px solid rgba(255, 255, 255, 0);
    cursor: col-resize;
}

.Resizer.vertical:hover {
    border-left: 5px solid rgba(0, 0, 0, 0.5);
    border-right: 5px solid rgba(0, 0, 0, 0.5);
   /* background-image: url('/assets/images/running_horses_small.gif');*/
}
.Resizer.disabled {
    cursor: not-allowed;
}
.Resizer.disabled:hover {
    border-color: transparent;
}

/* ag-grid overrides*/
.ag-group-value {
    align-self: center;
    overflow: hidden;
    text-overflow: ellipsis !important;
}

.ag-group-contracted {
    align-self: center !important;
    overflow: hidden;
    text-overflow: ellipsis !important;

}

.ag-group-expanded {
    align-self: center !important;
    overflow: hidden;
    text-overflow: ellipsis !important;
}

.ag-theme-alpine .ag-ltr .ag-cell {
    align-items: center;
    overflow: hidden;
    border: none !important;
    text-overflow: ellipsis !important;
}

.ag-center-cols-container {
    /* side effect on horizontal scroll in auto ize columns
    overflow: hidden;
    */
    /*width: 100% !important;
    text-overflow: ellipsis !important;*/
   /* width: auto !important;
    max-width: 100% !important;*/

}

.ag-cell-wrapper {
    align-items: center;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis !important;

}
.ag-cell-not-inline-editing {
    align-items: center;
}

.ag-theme-alpine .ag-cell {
    line-height: 30px !important;
}

.ag-cell-focus {
}
.ag-cell-no-focus {
    border: none !important;
}

/*header wrapping*/
.ag-header-cell-text {
    white-space: normal !important;
   /* overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: break-word;
    overflow-wrap: break-word;*/
}

/* ag-grid overrides*/

/* bootstrap overrides */
.form-control.is-valid{
    background-image: none !important;
    border-color: rgb(206, 212, 218) !important ;

}
.form-control.was-valid{
    background-image: none !important;
    border-color: rgb(206, 212, 218) !important;
}

.form-control:valid {
    background-image: none !important;
    border-color: rgb(206, 212, 218) !important;
}
.form-check-label{
    color: unset !important;
}
.model-no-border.modal-content {
    border: 0rem !important;
}
/* bootstrap overrides */

.form-group-item {
    border-radius: 0.25rem;
    border: 1px solid;
    border-color: #ced4da !important
}

.ccp-form * {
    font-size: small !important;
}
.ccp-label {
    font-weight: bold;
}
.bg-grey-rounded {
    background-color: #dee2e6;
    border-radius: 1.5rem;
}
.margin-025rem{
    margin-top: 0.25rem;
margin-bottom: 0.25rem;
}

/*.ccp-form > * {*/
/*    font-size: small !important;*/
/*}*/

/*.chip-input {*/
/*    display: block;*/
/*    border: 1px solid;*/
/*    border-color: rgb(206, 212, 218) !important;*/
/*    border-radius: 0.25rem;*/
/*}*/

.modal.fade {
    background: rgba(0, 0, 0, 0.25);
}

.modal-backdrop.fade {
    opacity: 0;
}
.ccp-modal-no-border-radius .modal-content {
    border-radius: 0rem;
}

/*Toastify over rides */
.Toastify__progress-bar {
    transform-origin: right !important;
}
/*Toastify overrides */


/*TextInputWithOptions Typeahead over rides */
.options-text-input .rbt-menu .dropdown-item {
    text-transform: uppercase;
}
/*TextInputWithOptions Typeahead over rides */

.create-run {
    background: url('/assets/images/create-run.png') no-repeat center center;
    background-size: cover;
}

.highcharts-data-label text {
    font-size: 50% !important;
    font-weight: 'bold';
    fill: grey !important;
}

.cell-error {
    background-color: #ffcdd2 !important;
}