import React from "react";
import "./App.css";
import Study from "./study/Study";
import { Button, Col, FormText, Row, Spinner } from "react-bootstrap";
import MenuGroup from "./components/MenuGroup";
import Plan from "./plan/Plan";
import RunSimulation from "./runsimulation/RunSimulation";
import Help from "./help/Help";
import Upside from "./upside/Upside";
import Header from "./header/Header";
import Lookahead from "./lookahead/Lookahead";
import "./style/ccpstyles.css";
import ScenarioComparison from "./scenariocomparison/ScenarioComparison";
import Footer from "./footer/Footer";
import { connect } from "react-redux";
import { changeMenu, logout, getNewJWTToken } from "./app/appActions";
import AppNotifier from "./components/AppNotifier";
import Administration from "./adminstration/Administration";
import { toast, ToastContainer } from "react-toastify";
import { obtainSamlToken } from "./app/appActions";
import { login } from "./app/appActions";
import Cttp from "./cttrainingpipeline/Cttp";
import SeparationModel from "./separationmodel/SeparationModel";
// import Ctpt from "./cttrainingpipeline/Ctpt";
//root component
class App extends React.Component {
  setSelectedOption = (e) => {
    this.props.changeMenu(e.target.name);
  };
  constructor() {
    super();
    this.state = {};
  }

  handleLogout = () => {
    this.props.logout();
  };

  //Feedback message for user actions.
  // Only one message should present at a given point of and render only latest message.
  getFeedback = () => {
    const { feedback = {} } = this.props;
    const { toastTimeStamp: timeStamp = `` } = this.state || {};
    const { id = ``, message = ``, variant = `` } = feedback;

    if (id && id !== timeStamp) {
      this.setState({ toastTimeStamp: feedback.id });
      //let variant = `success`
      const icon = (
        <img
          src={
            variant === "success"
              ? `../assets/images/check2-success.svg`
              : variant === "warning"
              ? `../assets/images/exclamation-triangle-warning.svg`
              : variant === "danger"
              ? `../assets/images/exclamation-octagon.svg`
              : ""
          }
          alt=""
          width="20"
          height="20"
          title="Feedback icon"
        />
      );
      const type =
        variant === "success"
          ? toast.TYPE.SUCCESS
          : variant === "warning"
          ? toast.TYPE.WARNING
          : variant === "danger"
          ? toast.TYPE.ERROR
          : toast.TYPE.DEFAULT;
      return toast(message, {
        toastId: id,
        autoClose: variant === "success" ? 5000 : false,
        icon,
        type,
        closeButton: (
          <Button variant="primary" style={{ margin: "auto 0rem auto 0rem" }}>
            Ok
          </Button>
        ),
      });
    }
  };

  componentDidMount() {
    let jwtSync = setInterval(this.refresh, 55 * 60 * 1000);
    this.setState({ jwtSync });
  }

  refresh = () => {
    this.props.getNewJWTToken();
  };
  componentWillUnmount() {
    clearInterval(this.state.jwtSync);
  }

  render() {
    if (this.props.loginStatus === "LOGOUT_SUCCESS") {
      window.location.href = `http://crewmax.nscorp.com`;
      //return (<div><a href={`http://crewmax.nscorp.com`}>CREWMAX</a><br/>Logout successful.</div>);
    }
    if (this.props.loginStatus !== "LOGGED_IN") {
      this.props.login();
    }
    const { menu, id } = this.props;
    this.getFeedback();
    return (
      <>
        <div className="sticky-top">
          <Header />
          {this.props.loginStatus === "LOGGED_IN" && (
            <div className="position-sticky">
              <Row className="ns-bg-color menu-row">
                <Col xs={10} sm={10} md={10} lg={10} xl={10}>
                  <MenuGroup
                    buttons={this.props.menus}
                    setSelectedOption={this.setSelectedOption}
                    selectedOption={menu}
                  />
                </Col>
                <Col
                  style={{ textAlign: "right", marginRight: "0rem" }}
                  xs={2}
                  sm={2}
                  md={2}
                  lg={2}
                  xl={2}
                >
                  <Row style={{ float: "right" }}>
                    <FormText
                      className="large-text"
                      style={{
                        marginRight: ".5rem",
                        marginTop: "unset",
                        fontWeight: "600",
                      }}
                    >
                      {id}
                    </FormText>
                    <Button
                      variant="outline-primary"
                      name="logout"
                      className="menu-button"
                      style={{ height: "fit-content" }}
                      onClick={this.handleLogout}
                      value="Logout"
                    >
                      <img
                        src={`../assets/images/logout.svg`}
                        className="label-img"
                        alt=""
                        width="28px"
                        height="28px"
                      />
                      Logout
                    </Button>
                  </Row>
                </Col>
              </Row>
            </div>
          )}
        </div>
        <ToastContainer />
        {this.props.loginStatus !== "LOGGED_IN" && <div>logging in</div>}
        {this.props.loginStatus === "LOGGED_IN" && (
          <>
            {
              {
                studies: <Study />,
                plans: <Plan />,
                runsimulation: <RunSimulation />,
                scenariocomparison: <ScenarioComparison />,
                administration: <Administration />,
                help: <Help />,
                upsides: <Upside />,
                cttp: <Cttp />,
                sp: <SeparationModel />,
                lookaheadmodel: <Lookahead />,
              }[menu]
            }
          </>
        )}
        <Footer />

        <AppNotifier alert={this.props.notification} />
        {this.props.isLoading && (
          <Spinner animation="border" variant="primary" className="spinner" />
        )}
      </>
    );
  }
}

function mapStateToProps(store) {
  const {
    loginStatus,
    name,
    notification,
    feedback,
    isLoading,
    role,
    menus,
    menu,
    id,
  } = store.app;
  return {
    loginStatus,
    name,
    notification,
    feedback,
    isLoading,
    role,
    menus,
    menu,
    id,
  };
}
const mapDispatchToProps = {
  login,
  obtainSamlToken,
  logout,
  changeMenu,
  getNewJWTToken,
};
export default connect(mapStateToProps, mapDispatchToProps)(App);
export { App };
