package com.nscorp.ccp.logic.markoff;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.common.markoff.MarkoffPrt;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;

@Slf4j
@Tag("fast")
class MarkoffProcessorLogicTest {
    private final MarkoffProcessorLogic processor = new MarkoffProcessorLogic();

    @Test
    public void test_MarkoffProcessorLogic() {
        TestUtils.runTestAndAssert("MarkoffProcessorLogicTest_test_MarkoffProcessorLogic", () -> {
            val markoffInputData = loadMarkoffInputData();
            val poolData = loadPoolData();
            val endDate = LocalDate.of(2024, 6, 30);
            val studyLengthMonths = 6;

            val processResult = processor.process(markoffInputData, poolData, endDate, studyLengthMonths);
            val resultList = new ArrayList<>();
            processResult.forEach(resultList::add);

            return JsonUtils.prettyPrint(resultList);
        });
    }

    private static class LocalDateTimeToInstantDeserializer extends JsonDeserializer<Instant> {
        @Override
        public Instant deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            String value = parser.getText();
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            return LocalDateTime.parse(value, DateTimeFormatter.ISO_LOCAL_DATE_TIME).toInstant(ZoneOffset.UTC);
        }
    }

    private List<MarkoffInputData> loadMarkoffInputData() {
        return loadJsonList("/test_input/markoffInputData.json", new TypeReference<List<MarkoffInputData>>() {});
    }

    private List<MarkoffPrt> loadPoolData() {
        return loadJsonList("/test_input/poolData.json", new TypeReference<List<MarkoffPrt>>() {});
    }

    private <T> List<T> loadJsonList(String resourcePath, TypeReference<List<T>> typeRef) {
        try (val is = getClass().getResourceAsStream(resourcePath)) {
            if (is == null) throw new IllegalStateException(resourcePath + " not found in test resources.");
            ObjectMapper mapper = new ObjectMapper();
            mapper.findAndRegisterModules();

            // Add custom deserializer for Instant fields that come as LocalDateTime strings
            SimpleModule module = new SimpleModule();
            module.addDeserializer(Instant.class, new LocalDateTimeToInstantDeserializer());
            mapper.registerModule(module);

            return mapper.readValue(is, typeRef);
        } catch (IOException e) {
            throw new RuntimeException("Failed to load " + resourcePath, e);
        }
    }

}
