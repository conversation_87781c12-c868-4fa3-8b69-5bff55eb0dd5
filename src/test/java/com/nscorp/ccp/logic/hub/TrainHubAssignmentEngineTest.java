package com.nscorp.ccp.logic.hub;
import lombok.val;

import com.nscorp.ccp.common.study.HubAssignmentInput;
import com.nscorp.ccp.testing.util.SampleDataManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static com.google.common.collect.Iterables.*;
import static com.nscorp.ccp.testing.util.TestUtils.*;

@Tag("fast")
@Slf4j
class TrainHubAssignmentEngineTest {

	@Test
	void assignHubs() {
		val assigner = new TrainHubAssignmentEngine();
		val inputs = transform(SampleDataManager.getSampleTrains(), e-> HubAssignmentInput.builder().
				fullTrainKey(e.getFullTrainKey()).
				assignFlag(e.getAssignFlag()).
				profileId(e.getProfileId()).
				crewOrgnOs(e.getCrewOrgnOs()).
				coExbKey(e.getCoExbKey()).
				enExbKey(e.getEnExbKey()).
				build());
		runTestAndAssert("TrainHubAssignmentEngineTest.assignHubs", () -> assigner.assignHubs(inputs,
				SampleDataManager.getSamplePlan().getCrewProfiles(), SampleDataManager.getSamplePlan().getExbPoolSetUps()));
	}
}
