package com.nscorp.ccp.logic.pba;
import io.vavr.collection.Stream;
import lombok.val;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.plan.CrewProfileAndBoardInfo;
import com.nscorp.ccp.common.study.Train;
import com.nscorp.ccp.logic.plan.CompletePlanSummarizer;
import com.nscorp.ccp.logic.study.PbaAssignmentInputMapper;
import com.nscorp.ccp.testing.util.SampleDataManager;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mapstruct.factory.Mappers;

@Slf4j
@Tag("fast")
public class TestPlanBasedAssigner {
	private final PlanBasedCrewProfileAssigner planBasedCrewProfileAssigner = new PlanBasedCrewProfileAssigner(); // diremoval
	private final PbaAssignmentInputMapper mapper = Mappers.getMapper(PbaAssignmentInputMapper.class); // diremoval
    private final CompletePlanSummarizer summarizer = new CompletePlanSummarizer(); // diremoval

    @Test
    public void test_PlanBasedAssigner() {
    	long scenarioId = 0L;
    	TestUtils.runTestAndAssert("test_ScenTrainPopulator", ()-> {
		    val completePlan = SampleDataManager.getSamplePlan();
		    val trains = ImmutableList.copyOf(SampleDataManager.getSampleStudy().getTrains());
		    TestUtils.writeOutput("test_ScenTrainPopulator_trains", trains.toString());
		    val input = Stream.ofAll(trains).
				    filter(Train::isCrewProfileUsed).
				    map(mapper::fromTrain);
		    TestUtils.writeOutput("test_ScenTrainPopulator_inputs", input.toString());
            val crewProfileAndBoardInfo = new CrewProfileAndBoardInfo(
                summarizer.getBoardInfo(completePlan),
                summarizer.getCrewProfileInfo(completePlan));
		    val result = planBasedCrewProfileAssigner.performPlanBasedAssignment(crewProfileAndBoardInfo, input);
		    return JsonUtils.prettyPrint(result.getAssignments());
	    });
    }
}
