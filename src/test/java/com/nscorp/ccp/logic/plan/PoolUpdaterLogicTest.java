package com.nscorp.ccp.logic.plan;

import com.nscorp.ccp.common.plan.Pool;
import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.testing.util.TestUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.*;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@Tag("fast")
class PoolUpdaterLogicTest {

	@BeforeEach
	public void init() {
		LogOutputInitializer.init();
	}

	/*
	@Test
	void test_PoolUpdaterLogic() {
		TestUtils.runTestAndAssert("PoolUpdaterLogic_result", ()-> {
			val existing = TestUtils.readTestInputJson("PoolUpdaterLogic_existing.txt", Pool.class);
			val fromRequest = TestUtils.readTestInputJson("PoolUpdaterLogic_fromRequest.txt", Pool.class);
			val poolId = 36;
			return new PoolUpdaterLogic().update(existing, fromRequest, poolId);
		});
	}
	 */
}
