package com.nscorp.ccp.logic.plan;
import lombok.val;

import com.nscorp.ccp.common.plan.ExbPoolSetUp;
import com.nscorp.ccp.logic.plan.ExbPoolSetUpUpdaterLogic;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;

@Slf4j
@Tag("fast")
public class TestUpdateExbPoolSetUpLogic {
	private final ExbPoolSetUpUpdaterLogic logic = new ExbPoolSetUpUpdaterLogic(); // diremoval
	@Test
	public void test_UpdateExbPoolSetUpLogic() {
		TestUtils.runTestAndAssert("exbPoolSetUp_modified", ()-> {
			val fromRequest = JsonUtils.readValue(TestUtils.readTestInput("exbPoolSetUp_fromRequest.txt"), ExbPoolSetUp.class);
			val existing = JsonUtils.readValue(TestUtils.readTestInput("exbPoolSetUp_existing.txt"), ExbPoolSetUp.class);
			val modified = logic.update(fromRequest, existing);
			return JsonUtils.prettyPrint(modified);
		});
	}
}
