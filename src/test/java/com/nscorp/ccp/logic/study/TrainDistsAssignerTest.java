package com.nscorp.ccp.logic.study;
import com.nscorp.ccp.common.study.TrainDistAssignmentInput;
import com.nscorp.ccp.testing.util.SampleDataManager;
import com.nscorp.ccp.testing.util.TestUtils;
import lombok.val;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static com.google.common.collect.Iterables.limit;
import static com.google.common.collect.Iterables.transform;

@Tag("fast")
/**
 * @see TrainDistsAssigner
 */
class TrainDistsAssignerTest {
	private final TrainDistsAssigner trainDistsAssigner = new TrainDistsAssigner(); // diremoval

	@Test
	public void test_TrainDistsAssigner_assignTrainDists2() {
		TestUtils.runTestAndAssert("test_TrainDistsAssigner_assignTrainDists", ()-> {
			val trains = limit(SampleDataManager.getSampleTrains(), 2500);
			val inputs = transform(trains, e -> TrainDistAssignmentInput.builder().
					fullTrainKey(e.getFullTrainKey()).
					trnType(e.getTrnType()).
					fromOs(e.getFromOs()).
					toOs(e.getToOs()).
					trnSymb(e.getTrnSymb()).
					trnOrgnDt(e.getTrnOrgnDt()).
					trnCrewDistr(e.getTrnCrewDistr()).
					build());
			return trainDistsAssigner.assignTrainDists(
					inputs,
					SampleDataManager.getAllTrainDists(),
					true,
					true,
					true);
		});
	}
}
