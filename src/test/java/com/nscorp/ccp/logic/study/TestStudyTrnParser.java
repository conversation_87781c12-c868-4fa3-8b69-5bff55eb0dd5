package com.nscorp.ccp.logic.study;

import com.nscorp.ccp.common.study.TrnParts;
import com.nscorp.ccp.logic.modelJson.UnparseableTrnException;
import com.nscorp.ccp.logic.study.TrnParser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@Slf4j
@Tag("fast")
public class TestStudyTrnParser {
	private final TrnParser trnParser = new TrnParser();
	@Test
	public void test_Study_trnParser() {
		TrnParts parts = trnParser.parse("ABCDEL018");
		assertThat(parts.getTrnSymb()).isEqualTo("ABCDE");
		assertThat(parts.getTrnCrewDistr()).isEqualTo("L0");
		assertThat(parts.getDayOfMonth()).isEqualTo(18);
		assertThatThrownBy(()->trnParser.parse("ABC")).isInstanceOf(UnparseableTrnException.class);
		assertThatThrownBy(()->trnParser.parse("ABCL9XX")).isInstanceOf(UnparseableTrnException.class);

		assertThat(trnParser.buildTrn("ABCDE", "L0", LocalDate.of(2021, 7, 18))).isEqualTo("ABCDEL018");
	}

	class Svc {
		private final TrnParser parser = new TrnParser();
		public TrnParts parse(String trn) {
			return parser.parse(trn);
		}
	}

	@Test
	public void test_Study_trnParser_mocking() {
		var svc = new Svc();

		// Demonstrate the use of ReflectionTestUtils.setField() to change the value of a final field
		//      in order to mock a dependency that cannot be injected through the constructor.

		var parts = svc.parse("ABCDEL018");
		var mockTrnParser = mock(TrnParser.class);
		when(mockTrnParser.parse("ABCDEL018")).thenReturn(TrnParts.builder().trnSymb("x").trnCrewDistr("x").dayOfMonth(1).build());
		assertThat(parts.getTrnSymb()).isEqualTo("ABCDE");
		ReflectionTestUtils.setField(svc, "parser", mockTrnParser);

		var parts2 = svc.parse("ABCDEL018");
		assertThat(parts2.getTrnSymb()).isEqualTo("x");
		assertThat(parts2.getTrnCrewDistr()).isEqualTo("x");
		assertThat(parts2.getDayOfMonth()).isEqualTo(1);
	}

}
