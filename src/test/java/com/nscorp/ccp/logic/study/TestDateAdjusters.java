package com.nscorp.ccp.logic.study;
import lombok.val;

import com.nscorp.ccp.common.historicalTrain.HistoricalTrain;
import com.nscorp.ccp.common.localYardJob.LocalTrainYardJob;
import com.nscorp.ccp.logic.study.HybridHistoricalTrainDateAdjuster;
import com.nscorp.ccp.logic.study.LocalYardJobDateAdjuster;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.date.DateRange;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;

import static com.nscorp.ccp.utils.date.DateTimeUtils.daysBetween;
import org.junit.jupiter.api.*;

@Slf4j
@Tag("fast")
public class TestDateAdjusters { 
	private static final Instant now = Instant.parse("2021-06-02T00:00:00.00Z");

	// @Autowired // diremoval
	private final HybridHistoricalTrainDateAdjuster hybridHistoricalTrainDateAdjuster = new HybridHistoricalTrainDateAdjuster(); // diremoval

	// @Autowired // diremoval
	private final LocalYardJobDateAdjuster localYardJobDateAdjuster = new LocalYardJobDateAdjuster(); // diremoval

	// @Test
	@Test
	public void test_HybridHistoricalTrainDateAdjuster() {
		TestUtils.runTestAndAssert("test_HybridHistoricalTrainDateAdjuster", () -> {
			val currentDate = LocalDate.of(2022, 1, 1);
			val range = DateRange.of(LocalDate.of(2028, 1, 1), LocalDate.of(2028, 2, 1));
			return hybridHistoricalTrainDateAdjuster.computeAdjustedDateRange(currentDate, range);
		});
	}

	// @Test
	@Test
	public void test_HybridHistoricalDateAdjuster2() {
		TestUtils.runTestAndAssert("test_HybridHistoricalDateAdjuster2", () -> {
			val currentDate = LocalDate.of(2022, 1, 1);
			val ht = HistoricalTrain.builder().
					fromTs(now).
					toTs(now.plusSeconds(3600)).
					trn("12N").
					trnCrewDistr("L1").
					trnOrgnDt(LocalDate.of(2022, 4, 1)).
					build();
			val range = DateRange.of(LocalDate.of(2028, 1, 1), LocalDate.of(2028, 2, 1));
			val adjusted = hybridHistoricalTrainDateAdjuster.computeAdjustedDateRange(currentDate, range);
			val dayAdjustment = daysBetween(adjusted.getStartDate(), range.getStartDate());
			return hybridHistoricalTrainDateAdjuster.adjustDates(ht, dayAdjustment);
		} );
	}
	// @Test
	@Test
	public void test_LocalYardJobDateAdjuster() {
		TestUtils.runTestAndAssert("test_LocalYardJobDateAdjuster", () -> {
			val currentDate = LocalDate.of(2022, 1, 1);
			val range = DateRange.of(LocalDate.of(2028, 1, 1), LocalDate.of(2028, 2, 1));
			return localYardJobDateAdjuster.computeAdjustedDateRange(currentDate, range);
		} );
	}

	// @Test
	@Test
	public void test_LocalYardJobDateAdjuster2() {
		TestUtils.runTestAndAssert("test_LocalYardJobDateAdjuster2", () -> {
			val currentDate = LocalDate.of(2022, 1, 1);
			val lj = LocalTrainYardJob.builder().
					fromTs(now).
					toTs(now.plusSeconds(3600)).
					trnSymb("12N").
					trnCrewDistr("L1").
					trnOrgnDt(LocalDate.of(2022, 4, 1)).
					build();
			val range = DateRange.of(LocalDate.of(2028, 1, 1), LocalDate.of(2028, 2, 1));
			val adjusted = localYardJobDateAdjuster.computeAdjustedDateRange(currentDate, range);
			val dayAdjustment = daysBetween(adjusted.getStartDate(), range.getStartDate());
			return localYardJobDateAdjuster.adjustDates(lj, dayAdjustment);
		} );
	}
}
