package com.nscorp.ccp.logic.study;
import lombok.val;

import com.nscorp.ccp.common.costCtr.TrainCostCtr;
import com.nscorp.ccp.common.study.Train;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static com.google.common.collect.ImmutableList.*;
import static org.assertj.core.api.Assertions.*;

@Tag("fast")
@Slf4j
class CostCtrAssignerTest {

	@Test
	void assignCostCtr() {
		val assigner = new CostCtrAssigner();
		val trains = of(
				newTrain("ts1", "tcd1", "fos1"),
				newTrain("ts2", "tcd2", "fos2")
		);
		val costCenters = of(
				TrainCostCtr.builder().costCtr(0).costCtrDesc("ctrdesc1").roadProfileOrgnOs("fos1").trnCrewDistr("tcd1").trnSymb("ts1").build()
		);
		val result = copyOf(assigner.assignCostCtr(trains, costCenters));
		assertThat(result).hasSize(2);
		assertThat(result.get(0).getCostCtrDesc()).isEqualTo("ctrdesc1");
		assertThat(result.get(1).getCostCtrDesc()).isNull();
		log.warn(result.toString());
	}
	Train newTrain(String trnSymb, String trnCrewDistr, String fromOs) {
		return Train.builder().
				trnSymb(trnSymb).
				trnCrewDistr(trnCrewDistr).
				fromOs(fromOs).
				build();
	}
}
