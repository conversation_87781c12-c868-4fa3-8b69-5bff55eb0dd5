package com.nscorp.ccp.logic.modelJson;
import lombok.val;

import com.nscorp.ccp.common.modelJson.ModelJsonRequestInfo;
import com.nscorp.ccp.logic.modelJson.ModelJsonToDomainObjectMapper;
import com.nscorp.ccp.testing.util.SampleDataManager;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;

@Slf4j
@Tag("fast")
public class TestRequestExport {
	private final ModelJsonToDomainObjectMapper mapper = new ModelJsonToDomainObjectMapper(); // diremoval
	@Test
	public void test_exportRequest() {
		TestUtils.runTestAndAssert("test_RequestExport", ()-> {
			val completePlan = SampleDataManager.getSamplePlan();
			val completeStudy = SampleDataManager.getSampleStudy();
			val scenarioCfg = SampleDataManager.getSampleScenarioCfg();
			val requestInfo = ModelJsonRequestInfo.builder().
					requestId("my-request-id").
					requestTs(SampleDataManager.getSampleDate()).
					source("TEST").
					build();
			val jsonRequest = mapper.dtoToJson(completePlan, completeStudy.getTrains(), scenarioCfg).toBuilder().
					requestInfo(requestInfo).
					build();
			return JsonUtils.prettyPrint(jsonRequest);
		});
	}
	@Test
	public void test_exportResponse() {
		TestUtils.runTestAndAssert("test_ResponseExport", ()-> {
			val modelJsonResponse = SampleDataManager.getSampleModelJsonResponse();

			val completePlan = SampleDataManager.getSamplePlan();
			val completeStudy = SampleDataManager.getSampleStudy();
			val scenarioCfg = SampleDataManager.getSampleScenarioCfg();
			val dto = mapper.jsonToDto(modelJsonResponse);
			val converted = mapper.dtoToJson(SampleDataManager.getSampleInstant(), dto);
			val requestInfo = ModelJsonRequestInfo.builder().
					requestId("my-request-id").
					requestTs(SampleDataManager.getSampleDate()).
					source("TEST").
					build();
			val jsonRequest = mapper.dtoToJson(completePlan, completeStudy.getTrains(), scenarioCfg).toBuilder().
					requestInfo(requestInfo).
					build();
			return JsonUtils.prettyPrint(jsonRequest);
		});
	}
}
