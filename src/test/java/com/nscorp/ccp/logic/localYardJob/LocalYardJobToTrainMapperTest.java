package com.nscorp.ccp.logic.localYardJob;
import lombok.val;

import com.nscorp.ccp.testing.util.SampleDataManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static com.nscorp.ccp.testing.util.TestUtils.*;

@Tag("fast")
@Slf4j
class LocalYardJobToTrainMapperTest {

	@Test
	void map() {
		val mapper = new LocalYardJobToTrainMapper();
		runTestAndAssert("LocalYardJobToTrainMapperTest_map", () -> mapper.map(SampleDataManager.getLocalTrainYardJobs()));
	}
}
