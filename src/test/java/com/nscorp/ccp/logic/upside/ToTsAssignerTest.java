package com.nscorp.ccp.logic.upside;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.transittime.HistTransitTime;
import com.nscorp.ccp.testing.util.TestUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("fast")
class ToTsAssignerTest {
	private final HistoricalToTsAssigner toTsAssigner = new HistoricalToTsAssigner();

	@Test
	void assignToTsUsingHistory() {
		var toTsAssignerTrains = ImmutableList.copyOf(TestUtils.readTestInputJsonAsList("assignToTsUsingHistory_input.txt", ToTsAssignerTrain.class));
		var histTransitTimes = ImmutableList.copyOf(TestUtils.readTestInputJsonAsList("assignToTsUsingHistory_histTransitTime.txt", HistTransitTime.class));
		TestUtils.runTestAndAssert("assignToTsUsingHistory", ()->toTsAssigner.assignToTsUsingHistory(toTsAssignerTrains, histTransitTimes));
	}
}
