package com.nscorp.ccp.rest;
import com.nscorp.ccp.rest.mappers.JsonPlanMapper;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.*;
import com.nscorp.ccp.common.opd.OpdPlan;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.common.plan.export.*;
import com.nscorp.ccp.common.plan.filter.*;
import com.nscorp.ccp.dao.plan.CrewProfileReportDAO;
import com.nscorp.ccp.logic.plan.PlanCopierException;
import com.nscorp.ccp.biz.opd.OpdIntegrationManager;
import com.nscorp.ccp.biz.plan.*;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.json.export.JsonExpCardedJob;
import com.nscorp.ccp.rest.json.export.JsonExpTieUpException;
import com.nscorp.ccp.rest.json.filter.*;
import com.nscorp.ccp.rest.mappers.AbstractJsonPlanMapperImpl;
import com.nscorp.ccp.rest.mappers.export.JsonPlanExportMapper;
import com.nscorp.ccp.rest.mappers.filter.JsonPlanFilterOptionsMapper;
import com.nscorp.ccp.testing.util.BackgroundInvokerMockImpl;
import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.utils.commonTypes.Craft;
import com.nscorp.ccp.utils.commonTypes.ExtraboardKey;
import com.nscorp.ccp.utils.commonTypes.sort.*;
import com.nscorp.ccp.utils.rest.RestMethodRunnerImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@Tag("fast")
@Slf4j
public class TestPlanRestController {
	private HttpServletRequest request;

	@BeforeEach
	public void init() {
		LogOutputInitializer.init();
		planManager = mock(PlanManager.class);
		planReportManager = mock(PlanReportManager.class);
		crewProfileReportDAO = mock(CrewProfileReportDAO.class);
		request = mock(HttpServletRequest.class);
		opdIntegrationManager = mock(OpdIntegrationManager.class);
		planUpdater = mock(PlanUpdater.class);
		planCopier = mock(PlanCopier.class);
		exbPoolSetUpCreator = mock(ExbPoolSetUpCreator.class);
		exbPoolSetUpUpdater = mock(ExbPoolSetUpUpdater.class);
		poolUpdater = mock(PoolUpdater.class);
		poolCreator = mock(PoolCreator.class);
		jsonPlanMapper = mock(JsonPlanMapper.class);
		val jsonPlanFilterOptionsMapper = new JsonPlanFilterOptionsMapper();
		val jsonPlanMapper = new AbstractJsonPlanMapperImpl();
		val jsonPlanExportMapper = new JsonPlanExportMapper(jsonPlanMapper);
		planRestService = PlanRestService.builder().
				runner(new RestMethodRunnerImpl()).
				jsonPlanMapper(jsonPlanMapper).
				backgroundInvoker(new BackgroundInvokerMockImpl()).
				crewProfileReportDAO(crewProfileReportDAO).
				jsonPlanExportMapper(jsonPlanExportMapper).
				planManager(planManager).
				planReportManager(planReportManager).
				opdIntegrationManager(opdIntegrationManager).
                planUpdater(planUpdater).
				planCopier(planCopier).
				exbPoolSetUpCreator(exbPoolSetUpCreator).
				exbPoolSetUpUpdater(exbPoolSetUpUpdater).
				poolUpdater(poolUpdater).
				poolCreator(poolCreator).
				jsonPlanMapper(jsonPlanMapper).
				jsonPlanFilterOptionsMapper(jsonPlanFilterOptionsMapper).
				build();
	}
	private void mockGetRequestURL() {
		when(request.getRequestURL()).thenReturn(new StringBuffer("http://localhost:8099"));
	}
	private PoolUpdater poolUpdater;
	private PoolCreator poolCreator;
	private ExbPoolSetUpUpdater exbPoolSetUpUpdater;
	private ExbPoolSetUpCreator exbPoolSetUpCreator;
	private PlanCopier planCopier;
    private PlanUpdater planUpdater;
	private PlanReportManager planReportManager;
	private CrewProfileReportDAO crewProfileReportDAO;
	private PlanManager planManager;
	private OpdIntegrationManager opdIntegrationManager;
	private PlanRestService planRestService;
	private  JsonPlanMapper jsonPlanMapper;


	@Test
	public void test_getAllOpdPlans() {
		mockGetRequestURL();
        val opdPlan1 = OpdPlan.builder().
            planSa(0).
            opdPlanId(0L).
            planName("name").
            planStatus("OK").
            build();
        val result = ImmutableList.of(opdPlan1);
        when(opdIntegrationManager.getPlans()).thenReturn(result);
        val resp = planRestService.getAllOpdPlans(request);
        assertThat(resp.getBody()).isNotNull();
        assertThat(resp.getBody().getCount()).isEqualTo(1);
        val jsonOpdPlan = (JsonOpdPlan)resp.getBody().getItems().get(0);
        assertThat(jsonOpdPlan.getPlanName()).isEqualTo("name");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getAllPlans() {
		mockGetRequestURL();
        val plan = Plan.builder().
            planId(0L).
            planName("name").
            status(PlanStatus.COMPLETE).
            userId("sys").
            build();
        val result = ImmutableList.of(plan);
        when(planManager.getAllPlans()).thenReturn(result);
        val resp = planRestService.getAllPlans(request);
        assertThat(resp.getBody()).isNotNull();
        assertThat(resp.getBody().getCount()).isEqualTo(1);
        val jsonPlan = (JsonPlan)resp.getBody().getItems().get(0);
        assertThat(jsonPlan.getName()).isEqualTo("name");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_updatePlan() {
        val upd = PlanUpdate.builder().
                planId(0L).
                planName("name").
                planDescription("desc").
                userId("sys").
                build();
        val plan = Plan.builder().
            planId(0L).
            planName("name").
            status(PlanStatus.COMPLETE).
            userId("sys").
            build();
        when(planUpdater.updatePlan(upd)).thenReturn(plan);

        val resp = planRestService.updatePlan(0L, "name", "desc", "sys");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

    @Test
    public void test_copyPlan_success() {
        val plan = Plan.builder().
            planId(1L).
            planName("name").
            status(PlanStatus.COMPLETE).
            userId("sys").
            build();
        val completePlan = CompletePlan.builder().
            plan(plan).
            build();
        when(planCopier.beginCopy(0L, "sys", "name", "desc")).thenReturn(plan);
        when(planCopier.finishCopy(1L)).thenReturn(completePlan);
        val resp = planRestService.copyPlan(0L, "name", "desc", "sys");
	    assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
    }


    @Test
    public void test_copyPlan_failure() {
        val plan = Plan.builder().
            planId(1L).
            planName("name").
            status(PlanStatus.COMPLETE).
            userId("sys").
            build();
        val completePlan = CompletePlan.builder().
            plan(plan).
            build();
        when(planCopier.beginCopy(0L, "sys", "name", "desc")).thenThrow(new PlanCopierException("error"));
        val resp = planRestService.copyPlan(0L, "name", "desc", "sys");
		assertThat(resp.getStatusCode() == HttpStatus.BAD_REQUEST);
    }

    @Test
    public void test_deletePlan() {
        val plan = Plan.builder().
            planId(0L).
            planName("name").
            status(PlanStatus.COMPLETE).
            userId("sys").
            build();
        when(planManager.markPlanDeletedIfNotLocked(0L)).thenReturn(plan);
        val resp = planRestService.deletePlan(0L);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().isSuccessful()).isTrue();
	    assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void test_getUniqueTerminals() {
        mockGetRequestURL();
        val result = ImmutableSet.<String>of("A", "B");
        when(planReportManager.getUniqueTerminalsByPlanId(0L)).thenReturn(result);
        val re = planRestService.getUniqueTerminals(0L, request);
		assertThat(re.getBody()).isNotNull();
		assertThat(re.getBody().getCount()).isEqualTo(2);
		assertThat(re.getBody().getItems().get(0)).isEqualTo("A");
		assertThat(re.getBody().getItems().get(1)).isEqualTo("B");
    }

    @Test
    public void test_getCardedJobs() {
        mockGetRequestURL();
        val cj1 = CardedJobSummary.builder().
            distr("AL").
            subDistr("BI").
            poolName("CC").
            build();
        val result = ImmutableList.of(cj1);
		doReturn(result).when(planReportManager).getCardedJobSummariesByPlanIdAndDistrAndSubDistrAndPoolName(anyLong(), any(), any(), any());

        val resp = planRestService.getCardedJobs(0L, "AL", "BI", "CC", request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonCardedJobSummary summ = (JsonCardedJobSummary)resp.getBody().getItems().get(0);
		assertThat(summ.getDistr()).isEqualTo("AL");
		assertThat(summ.getSubDistr()).isEqualTo("BI");
		assertThat(summ.getPool()).isEqualTo("CC");
	    assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

	    val resp2 = planRestService.getCardedJobs(0L, "ALL", "ALL", "ALL", request);
	    assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void test_getCardedJobDetails() {
        mockGetRequestURL();
        val cjd = CardedJobDetail.builder().
            cardedDayId(0L).
            dayOfCycle(2).
            build();
        val result = ImmutableList.of(cjd);
        when(planReportManager.getCardedJobDetailsByCardedPoolId(0L)).thenReturn(result);
	    val resp = planRestService.getCardedJobDetails(0L, request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonCardedJobDetail detail = (JsonCardedJobDetail)resp.getBody().getItems().get(0);
        assertThat(detail.getId()).isEqualTo(0L);
        assertThat(detail.getDayOfCycle()).isEqualTo(2);
	    assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void test_getCardedJobExportDetails() {
        mockGetRequestURL();
        val ecj = ExpCardedJob.builder().
            distr("AL").
            subDistr("BI").
            poolName("CC").
			dayOfCycle(2).
            build();
        val result = ImmutableList.of(ecj);

		doReturn(result).when(planReportManager).getExpCardedJobsByPlanIdAndDistrAndSubDistrAndPoolName(anyLong(), any(), any(), any());

	    val resp = planRestService.getCardedJobExportDetails(0L, "AL", "BI", "CC", request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonExpCardedJob cj = (JsonExpCardedJob) resp.getBody().getItems().get(0);
        assertThat(cj.getDistr()).isEqualTo("AL");
	    assertThat(cj.getSubDistr()).isEqualTo("BI");
	    assertThat(cj.getPool()).isEqualTo("CC");
        assertThat(cj.getDayOfCycle()).isEqualTo(2);
	    assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

	    val resp2 = planRestService.getCardedJobExportDetails(0L, "ALL", "ALL", "ALL", request);
	    assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

	@Test
	public void test_getCrewProfiles() {
		mockGetRequestURL();
		val sortModel = SortModel.builder().colId(SortColumns.PROFILEID).sort(SortTypes.ASC).build();
		val models = ImmutableList.of(sortModel);
		val cprpt1 = CrewProfileReport.builder().
				crewProfileId(0L).
				planId(0L).
				distr("HB").
				craft(Craft.CO).
				subDistr("CR").
				poolName("CR").
				build();
		val result = ImmutableList.of(cprpt1);
		doReturn(result).when(crewProfileReportDAO).getCrewProfileReport(anyLong(), any(), any(), any(), anyInt(), anyInt(),
				anyList());
		val resp = planRestService.getCrewProfiles(0L, 1, "HB", "CR", "CR",
				null, request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
		assertThat(((JsonCrewProfile)resp.getBody().getItems().get(0)).getDistr()).isEqualTo("HB");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

		val resp2 = planRestService.getCrewProfiles(0L, 1, "ALL", "ALL", "ALL", null, request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getExtraboardSummaries() {
		mockGetRequestURL();
        val exbSummary = ExtraboardSummary.builder().
		        distr("AL").
		        subDistr("BI").
		        poolName("CC").
		        craft(Craft.CO).
		        homeOs("148H").
            build();
		val result = ImmutableList.of(exbSummary);
		doReturn(result).when(planReportManager).getExtraBoardSummariesByPlanIdAndFilterCriteria(anyLong(), any(), any());

	    val resp = planRestService.getExtraBoardSummaries(0L, "AL", "BI", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonExtraboardSummary jes = (JsonExtraboardSummary) resp.getBody().getItems().get(0);
        assertThat(jes.getDistr()).isEqualTo("AL");
	    assertThat(jes.getSubDistr()).isEqualTo("BI");

		val resp2 = planRestService.getExtraBoardSummaries(0L, "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getCrewProfile() {
		mockGetRequestURL();
		val crewProfile = CrewProfile.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				craft(Craft.CO).
				build();
		when(planManager.getCrewProfileById(0L)).thenReturn(crewProfile);

		val resp = planRestService.getCrewProfileByCrewProfileId(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody()).isNotNull();
		final JsonCrewProfile entity = resp.getBody();
		assertThat(entity.getDistr()).isEqualTo("AL");
		assertThat(entity.getSubDistr()).isEqualTo("BI");
	}

	@Test
	public void test_getExtraboardDetails() {
		mockGetRequestURL();
        val exbPoolSetUp = ExbPoolSetUp.builder().
		        distr("AL").
		        subDistr("BI").
		        poolName("CC").
		        craft(Craft.CO).
		        homeOs("148H").
            build();
		when(planManager.getExbPoolSetUpById(0L)).thenReturn(exbPoolSetUp);

	    val resp = planRestService.getExtraBoardDetails(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody()).isNotNull();
        final JsonExbPoolSetUp entity = (JsonExbPoolSetUp) resp.getBody();
        assertThat(entity.getDistr()).isEqualTo("AL");
	    assertThat(entity.getSubDistr()).isEqualTo("BI");
	}
	@Test
	public void test_getExtraboardDetails_noMatchingResult() {
		mockGetRequestURL();
		val resp = planRestService.getExtraBoardDetails(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
	}

	@Test
	public void test_getExtraBoardExportDetails() {
		val expExbTurn = ExpExbTurn.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				craft("CO").
				turnId("C1").
				build();
		val supportedPool = ExpExbSupportedPool.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				build();
        val result = ExpExtraboardDetails.builder().
		        turns(ImmutableList.of(expExbTurn)).
		        supportedPools(ImmutableList.of(supportedPool)).
				build();
		doReturn(result).when(planReportManager).getExtraBoardExportDetailsByPlanIdAndFilterCriteria(anyLong(), any(), any());

	    val resp = planRestService.getExtraBoardExportDetails(0L, "AL", "BI", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody()).isNotNull();
        val entity = resp.getBody();
		val jsonTurn = entity.getTurns().get(0);
		assertThat(jsonTurn.getDistr()).isEqualTo("AL");
		assertThat(jsonTurn.getSubDistr()).isEqualTo("BI");
		assertThat(jsonTurn.getPoolName()).isEqualTo("CC");
		assertThat(jsonTurn.getCraft()).isEqualTo("CO");
		assertThat(jsonTurn.getTurnId()).isEqualTo("C1");

		val resp2 = planRestService.getExtraBoardExportDetails(0L, "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getIdPools() {
		mockGetRequestURL();
        val idPool = IdPool.builder().
		        distr("AL").
		        subDistr("BI").
		        poolName("CC").
		        craft(Craft.CO).
            build();
		val result = ImmutableList.of(idPool);
		doReturn(result).when(planManager).getIdPoolsByPlanIdAndFilterCriteria(anyLong(), any(), any(), any());

	    val resp = planRestService.getIdPools(0L, "AL", "BI", "CO", request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonIdPool jip = (JsonIdPool) resp.getBody().getItems().get(0);
        assertThat(jip.getDistr()).isEqualTo("AL");
	    assertThat(jip.getSubDistr()).isEqualTo("BI");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

		val resp2 = planRestService.getIdPools(0L, "ALL", "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}


	@Test
	public void test_getPoolSetups() {
		mockGetRequestURL();
        val poolSummary = PoolSummary.builder().
		        distr("AL").
		        subDistr("BI").
		        poolName("CC").
            build();
		val result = ImmutableList.of(poolSummary);
		doReturn(result).when(planReportManager).getPoolSummariesByPlanIdAndFilterCriteria(anyLong(), any(), any(), any());

	    val resp = planRestService.getPoolSetups(0L, "AL", "BI", "CC", request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
        final JsonPoolSummary ps = (JsonPoolSummary) resp.getBody().getItems().get(0);
        assertThat(ps.getDistr()).isEqualTo("AL");
	    assertThat(ps.getSubDistr()).isEqualTo("BI");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

		val resp2 = planRestService.getPoolSetups(0L, "ALL", "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getPoolSetupExportDetails() {
		mockGetRequestURL();
		val terminal = ExpPoolSetUpTerminal.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				build();
		val turn = ExpPoolSetUpTurn.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				build();
		val other = ExpPoolSetUpOther.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				build();
        val expPoolSetUp = ExpPoolSetUp.builder().
		        terminals(ImmutableList.of(terminal)).
		        turns(ImmutableList.of(turn)).
		        others(ImmutableList.of(other)).
            build();
		doReturn(expPoolSetUp).when(planReportManager).getPoolSetupExportDetails(anyLong(), any(), any(), any());

	    val resp = planRestService.getPoolSetupExportDetails(0L, "AL", "BI", "CC", request);
		assertThat(resp.getBody()).isNotNull();
        val jpsu = resp.getBody();
        assertThat(jpsu.getTurns().get(0).getDistr()).isEqualTo("AL");
		assertThat(jpsu.getTerminals().get(0).getDistr()).isEqualTo("AL");
		assertThat(jpsu.getOthers().get(0).getDistr()).isEqualTo("AL");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

		val resp2 = planRestService.getPoolSetupExportDetails(0L, "ALL", "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getPoolSetup() {
		mockGetRequestURL();
        val pool = Pool.builder().
            poolId(0L).
            distr("AL").
            subDistr("BI").
            poolName("CC").
            build();
		when(planManager.getPoolById(0L)).thenReturn(pool);

	    val resp = planRestService.getPoolSetup(0L, request);
		assertThat(resp.getBody()).isNotNull();
        final JsonPool entity = (JsonPool) resp.getBody();
        assertThat(entity.getDistr()).isEqualTo("AL");
	    assertThat(entity.getSubDistr()).isEqualTo("BI");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getPoolSetup_notFound() {
		mockGetRequestURL();
		when(planManager.getPoolById(0L)).thenReturn(null);

		val resp = planRestService.getPoolSetup(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
	}

	@Test
	public void test_deletePool() {
		doNothing().when(planManager).deletePoolByIdIfNotLocked(0L);
		val entity = planRestService.deletePool(0L);
		assertThat(entity.getBody().isSuccessful()).isTrue();
		assertThat(entity.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_createExbPoolSetUp() {
		val turn = ExbTurn.builder().turnId("C1").build();
		val turns = ImmutableSet.of(turn);
		val extraboardKey = ExtraboardKey.builder().distr("AL").subDistr("BI").poolName("CC").exbBoardName("B1").build();
		val extraboard = Extraboard.builder().extraboardKey(extraboardKey).poolCraft(Craft.CO).poolName("CC").build();
		val extraboards = ImmutableSet.of(extraboard);
		val psu = ExbPoolSetUp.builder().distr("AL").subDistr("BI").poolName("CC").
				exbTurns(turns).extraboards(extraboards).craft(Craft.CO).homeOs("148H").build();

		when(exbPoolSetUpCreator.create(any())).thenReturn(psu.withExbPoolSetUpId(0L));

		val mapper = new AbstractJsonPlanMapperImpl();
		val resp = planRestService.createExbPoolSetUp(mapper.toJson(psu));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getDistr()).isEqualTo("AL");
		assertThat(resp.getBody().getExbTurns()).hasSize(1);
		assertThat(resp.getBody().getExbTurns().get(0).getId()).isEqualTo("C1");
		assertThat(resp.getBody().getExtraboards()).hasSize(1);
	}

	@Test
	public void test_updateExbPoolSetUp() {
		val exbTurn = ExbTurn.builder().turnId("C1").build();
		val turns = ImmutableSet.of(exbTurn);
		val extraboard = Extraboard.builder().poolCraft(Craft.CO).poolName("CC").build();
		val extraboards = ImmutableSet.of(extraboard);
		val exbPoolSetUp = ExbPoolSetUp.builder().distr("AL").subDistr("BI").poolName("CC").craft(Craft.CO).exbTurns(turns).homeOs("148H").extraboards(extraboards).build();
		when(exbPoolSetUpUpdater.update(any(), eq(0L))).thenReturn(exbPoolSetUp);
		val mapper = new AbstractJsonPlanMapperImpl();
		val json = mapper.toJson(exbPoolSetUp);
		val resp = planRestService.updateExbPoolSetUp(0L, json);
		warn(log, String.format("resp=%s", resp));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_deleteExbPoolSetUp() {
		doNothing().when(planManager).deleteExbPoolSetUpByIdIfNotLocked(0L);
		val resp = planRestService.deleteExbPoolSetUp(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getHubs() {
		val hub = Hub.builder().hubName("hub1").hubId(0L).planId(0L).build();
		val hubs = ImmutableList.of(hub);
		when(planManager.getHubsByPlanId(0L)).thenReturn(hubs);
		val resp = planRestService.getHubs(0L,request);
		assertThat(resp.getBody()).isNotNull();
		assertThat(resp.getBody().getCount()).isEqualTo(1);
		final JsonHub jsonHub = (JsonHub) resp.getBody().getItems().get(0);
		assertThat(jsonHub.getHubName()).isEqualTo("hub1");
		assertThat(jsonHub.getId()).isEqualTo(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_createHub() {
		val hub = Hub.builder().hubName("hub1").planId(0L).build();
		val hubs = ImmutableList.of(hub);
		when(planManager.saveIfNotLocked(hub)).thenReturn(hub.withHubId(0L));
		val mapper = new AbstractJsonPlanMapperImpl();
		val resp = planRestService.createHub(mapper.toJson(hub));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val jsonHub = resp.getBody();
		assertThat(jsonHub.getHubName()).isEqualTo("hub1");
		assertThat(jsonHub.getId()).isEqualTo(0L);
	}

	@Test
	public void test_updateHub() {
		val hub = Hub.builder().hubId(0L).hubName("hub1").planId(0L).build();
		when(planManager.saveIfNotLocked(hub)).thenReturn(hub);
		val mapper = new AbstractJsonPlanMapperImpl();
		val resp = planRestService.updateHub(0L, mapper.toJson(hub));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_deleteHub() {
		doNothing().when(planManager).deleteHubByIdIfNotLocked(0L);
		val resp = planRestService.deleteHub(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_updatePool() {
		val pool = Pool.builder().distr("AL").subDistr("BI").poolName("CC").poolId(0L).build();
		when(poolUpdater.update(pool, 0L)).thenReturn(pool);
		val mapper = new AbstractJsonPlanMapperImpl();
		val jsonPool = mapper.toJson(pool);
		val resp = planRestService.updatePool(0L, jsonPool);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_createPool() {
		val rotation = PoolRotationH.builder().tieupShort(1).tieupThru(0).build();
		val rotations = ImmutableSet.of(rotation);
		val psu = PoolSetUp.builder().os("148H").poolRotationHistories(rotations).regularRestTime(9.0).undisturbedRest("Y").build();
		val psus = ImmutableSet.of(psu);
		val pool = Pool.builder().distr("AL").subDistr("BI").poolName("CC").poolSetUps(psus).build();
		when(poolCreator.create(any())).thenReturn(pool.withPoolId(0L));
		val mapper = new AbstractJsonPlanMapperImpl();
		val resp = planRestService.createPool(mapper.toJson(pool));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getDistr()).isEqualTo("AL");
		assertThat(resp.getBody().getSubDistr()).isEqualTo("BI");
	}

	@Test
	public void test_getTieUpExceptionRules() {
		mockGetRequestURL();
		val tue = TieUpExceptionSummary.builder().tueSeqNbr(0).distr("AL").subDistr("BI").craft(Craft.CO).planId(0L).tieUpExceptionId(0L).build();
		val tues = ImmutableSet.of(tue);
		doReturn(tues).when(planReportManager).getTieUpExceptionSummariesByPlanIdAndFilterCriteria(anyLong(), any(), any());
		val resp = planRestService.getTieUpExceptionRules(0L, "AL", "BI", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getCount()).isEqualTo(1);
		val jsonTue = (JsonTieUpExceptionSummary)resp.getBody().getItems().get(0);
		assertThat(jsonTue.getDistr()).isEqualTo("AL");
		assertThat(jsonTue.getSubDistr()).isEqualTo("BI");
		assertThat(jsonTue.getTueSeqNbr()).isEqualTo(0L);
		assertThat(jsonTue.getId()).isEqualTo(0L);

		val resp2 = planRestService.getTieUpExceptionRules(0L, "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getTieUpExceptionRuleExportDetails() {
		mockGetRequestURL();
		val tue = ConsolidatedTieUpException.builder().distr("AL").subDistr("BI").fromPoolName("C1").toPoolName("D1").craft(Craft.CO).tueSeqNbr(0).tueSubSeqNbr(0).sp1Distr("AL").sp1SubDistr("BI").sp1PoolName("CC").build();
		val tues = ImmutableSet.of(tue);

		doReturn(tues).when(planManager).getConsolidatedTieUpExceptionsByPlanIdAndFilterCriteria(anyLong(), any(), any());

		val resp = planRestService.getTieUpExceptionRuleExportDetails(0L, "AL", "BI", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getCount()).isEqualTo(1);
		val json = (JsonExpTieUpException)resp.getBody().getItems().get(0);
		assertThat(json.getDistr()).isEqualTo("AL");
		assertThat(json.getSubDistr()).isEqualTo("BI");
		assertThat(json.getTueSeqNbr()).isEqualTo(0L);

		val resp2 = planRestService.getTieUpExceptionRuleExportDetails(0L, "ALL", "ALL", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getTieUpExceptionRuleDetails() {
		val searchPool = SearchPool.builder().distr("AL").subDistr("BI").poolName("CC").searchPoolId(0L).build();
		val searchPools = ImmutableList.of(searchPool);
		val subrule = TueSubrule.builder().fromDistr("AL").fromSubDistr("BI").fromPoolName("CC").toDistr("AL").toSubDistr("BI").toPoolName("CC").searchPools(searchPools).build();
		val subrules = ImmutableSet.of(subrule);
		val tue = TieUpException.builder().distr("AL").subDistr("BI").craft(Craft.CO).planId(0L).tueSubrules(subrules).planId(0L).tueSeqNbr(0).build();
		when(planManager.getTieUpExceptionById(0L)).thenReturn(tue);
		val resp = planRestService.getTieUpExceptionRuleDetails(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getDistr()).isEqualTo("AL");
		assertThat(body.getSubDistr()).isEqualTo("BI");
		assertThat(body.getTueSeqNbr()).isEqualTo(0);
	}

	@Test
	public void test_getTieUpExceptionRuleDetails_notFound() {
		when(planManager.getTieUpExceptionById(0L)).thenReturn(null);
		val resp = planRestService.getTieUpExceptionRuleDetails(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
	}

	@Test
	public void test_getWorkRestProfiles() {
		mockGetRequestURL();;
		val wrpSummary = WorkRestProfSummary.builder().planId(0L).distr("AL").subDistr("BI").poolName("CC").profName("P1").poolType("POOL").build();
		val wrpSummaries = ImmutableSet.of(wrpSummary);
		doReturn(wrpSummaries).when(planReportManager).getWorKRestProfSummariesByPlanIdAndDistrAndSubDistrAndPoolNameAndPoolType(anyLong(), any(), any(), any(), any());
		val resp = planRestService.getWorkRestProfiles(0L, "AL", "BI", "CC", "EXTRABOARD", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getCount()).isEqualTo(1);
		val json = (JsonWorkRestProfSummary)resp.getBody().getItems().get(0);
		assertThat(json.getDistr()).isEqualTo("AL");
		assertThat(json.getSubDistr()).isEqualTo("BI");
		assertThat(json.getPoolName()).isEqualTo("CC");
		assertThat(json.getName()).isEqualTo("P1");

		val resp2 = planRestService.getWorkRestProfiles(0L, "ALL", "ALL", "ALL", "EXTRABOARD", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getWorkRestProfileExportDetails() {
		mockGetRequestURL();;
		val turn = ExpWorkRestProfTurn.builder().turnId("C1").profName("P1").distr("AL").subDistr("BI").poolName("CC").build();
		val turns = ImmutableList.of(turn);
		val rotation = ExpWorkRestProfRotation.builder().distr("AL").subDistr("BI").poolName("CC").profName("P1").ordSeq(0).build();
		val rotations = ImmutableList.of(rotation);
		val expWorkRestProf = ExpWorkRestProf.builder().turns(turns).rotations(rotations).build();
		doReturn(expWorkRestProf).when(planReportManager).getExpWorkRestProfByPlanIdAndFilterCriteria(anyLong(), any(), any(), any(), any());
		val resp = planRestService.getWorkRestProfileExportDetails(0L, "AL", "BI", "CC", "EXTRABOARD", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		val jsonTurn = json.getTurns().get(0);
		assertThat(jsonTurn.getDistr()).isEqualTo("AL");
		assertThat(jsonTurn.getSubDistr()).isEqualTo("BI");
		assertThat(jsonTurn.getPool()).isEqualTo("CC");
		assertThat(jsonTurn.getTurnId()).isEqualTo("C1");

		val resp2 = planRestService.getWorkRestProfileExportDetails(0L, "ALL", "ALL", "ALL", "EXTRABOARD", request);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
	}

	@Test
	public void test_getWorkRestProfileDetails() {
		mockGetRequestURL();
		val turn = WorkRestProfTGrp.builder().turnId("C1").craft(Craft.CO).build();
		val turns = ImmutableSet.of(turn);
		val rotation = WorkRestProfRotn.builder().ordSeq(0).rotnId(0).build();
		val rotations = ImmutableSet.of(rotation);
		val prof = WorkRestProf.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				profName("P1").
				turnGroups(turns).
				rotations(rotations).
				build();
		when(planManager.getWorkRestProfById(0L)).thenReturn(prof);
		val resp = planRestService.getWorkRestProfileDetails(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		val jsonTurn = json.getTurnGroups().get(0);
		assertThat(jsonTurn.getId()).isEqualTo("C1");
		assertThat(jsonTurn.getCraft()).isEqualTo(Craft.CO);

		val jsonRot = json.getRotations().get(0);
		assertThat(jsonRot.getId()).isEqualTo(0L);
		assertThat(jsonRot.getOrdSeq()).isEqualTo(0);
	}

	@Test
	public void test_getWorkRestProfileDetails_notFound() {
		mockGetRequestURL();
		when(planManager.getWorkRestProfById(0L)).thenReturn(null);
		val resp = planRestService.getWorkRestProfileDetails(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
	}

	@Test
	public void test_getCardedJobsFilters() {
		mockGetRequestURL();
		val filter1 = CardedJobFilter.builder().distr("AL").subDistr("BI").poolName("CC").build();
		val filter2 = CardedJobFilter.builder().distr("AL").subDistr("BI").poolName("DD").build();
		val filter3 = CardedJobFilter.builder().distr("AL").subDistr("XX").poolName("11").build();
		val filter4 = CardedJobFilter.builder().distr("AL").subDistr("XX").poolName("22").build();
		val filterList = ImmutableList.of(filter1, filter2, filter3, filter4);
		when(planReportManager.getCardedJobFiltersByPlanId(0L)).thenReturn(filterList);
		val resp = planRestService.getCardedJobsFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body).isNotNull();
		assertThat(body.getDistrs()).hasSize(1);
		val cjDistr = Iterables.getFirst(body.getDistrs(), null);
		assertThat(cjDistr.getDistr()).isEqualTo("AL");
		assertThat(cjDistr.getSubDistrs()).hasSize(2);
		warn(log, String.format("body=%s", body));
	}

	@Test
	public void test_getCrewProfileFilters() {
		mockGetRequestURL();
		val filter = CrewProfileFilter.builder().distr("AL").subDistr("BI").poolName("CC").build();
		val filters = ImmutableList.of(filter);
		when(planReportManager.getCrewProfileFiltersByPlanId(0L)).thenReturn(filters);
		val resp = planRestService.getCrewProfileFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body).isNotNull();
		assertThat(body.getDistrs()).hasSize(1);
		val distr = Iterables.getFirst(body.getDistrs(), null);
		assertThat(distr.getDistr()).isEqualTo("AL");
		assertThat(distr.getSubDistrs()).hasSize(1);
	}

	@Test
	public void test_getCrewProfileSimpleFilters() {
		mockGetRequestURL();
		val filt1 = CrewProfileSimpleFilter.builder().profileId("P1").crewOrgnOs("148H").build();
		val filt2 = CrewProfileSimpleFilter.builder().profileId("P2").crewOrgnOs("547A").build();
		val filters = ImmutableList.of(filt1, filt2);
		when(planReportManager.getCrewProfileSimpleFilters()).thenReturn(filters);
		val resp = planRestService.getCrewProfileSimpleFilters(request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getCount()).isEqualTo(2);
		val json1 = (JsonCrewProfileSimpleFilter)body.getItems().get(0);
		val json2 = (JsonCrewProfileSimpleFilter)body.getItems().get(1);
		assertThat(json1.getProfileId()).isEqualTo("P1");
		assertThat(json2.getProfileId()).isEqualTo("P2");
	}

	@Test
	public void test_getCrewProfilePlans() {
		mockGetRequestURL();
		val plan1 = CrewProfilePlan.builder().planId(0L).planName("plan1").planDescription("desc").build();
		val plan2 = CrewProfilePlan.builder().planId(0L).planName("plan2").planDescription("desc").build();
		val plans = ImmutableList.of(plan1, plan2);
		when(planReportManager.getPlans("148H", "P1")).thenReturn(plans);
		val resp = planRestService.getCrewProfilePlans("148H", "P1", request);
		warn(log, String.format("resp=%s", resp));
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val items = resp.getBody();
		assertThat(items.getCount()).isEqualTo(2);
		val json1 = (JsonPlan)items.getItems().get(0);
		assertThat(json1.getId()).isEqualTo(0L);
		assertThat(json1.getName()).isEqualTo("plan1");
		assertThat(json1.getDesc()).isEqualTo("desc");
		assertThat(json1.getLocked()).isNull();
		assertThat(json1.getPlanType()).isNull();
		assertThat(json1.getUserId()).isNull();
		assertThat(json1.getStatus()).isNull();
	}

	@Test
	public void test_getExtraboardSummariesFilters() {
		mockGetRequestURL();
		val filt1 = ExtraboardFilter.builder().distr("AL").subDistr("BI").build();
		val filt2 = ExtraboardFilter.builder().distr("AL").subDistr("XX").build();
		val filters = ImmutableList.of(filt1, filt2);
		when(planReportManager.getExtraboardFiltersByPlanId(0L)).thenReturn(filters);
		val resp = planRestService.getExtraboardSummariesFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val ebDistr = JsonExtraboardFilters.EbDistr.builder().distr("AL").subDistrs(ImmutableSet.of("BI", "XX")).build();
		val jsonFilt1 = resp.getBody().getDistrs();
		assertThat(jsonFilt1).contains(ebDistr);
	}

	@Test
	public void test_getIdPoolFilters() {
		val filt1 = IdPoolFilter.builder().distr("AL").subDistr("BI").build();
		val filt2 = IdPoolFilter.builder().distr("AL").subDistr("XX").build();
		val filters = ImmutableList.of(filt1, filt2);
		when(planReportManager.getIdPoolFiltersByPlanId(0L)).thenReturn(filters);
		val resp = planRestService.getIdPoolFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		val idDistr = JsonIdPoolFilters.IdDistr.builder().distr("AL").subDistrs(ImmutableSet.of("BI", "XX")).build();
		assertThat(body.getDistrs()).contains(idDistr);
	}

	@Test
	public void test_getPoolSetupFilters() {
		mockGetRequestURL();
		val filt1 = PoolSetupFilter.builder().distr("AL").subDistr("BI").poolName("C1").build();
		val filt2 = PoolSetupFilter.builder().distr("AL").subDistr("CI").poolName("C2").build();
		val filt3 = PoolSetupFilter.builder().distr("AL").subDistr("CI").poolName("C3").build();
		val filt4 = PoolSetupFilter.builder().distr("AL").subDistr("BI").poolName("C4").build();
		val filters = ImmutableList.of(filt1, filt2, filt3, filt4);
		when(planReportManager.getPoolSetupFiltersByPlanId(0L)).thenReturn(filters);
		val resp = planRestService.getPoolSetupFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		val psSubDistr1 = JsonPoolSetupFilters.PsSubDistr.builder().subDistr("BI").poolNames(ImmutableSet.of("C1", "C4")).build();
		val psSubDistr2 = JsonPoolSetupFilters.PsSubDistr.builder().subDistr("CI").poolNames(ImmutableSet.of("C2", "C3")).build();
		val psDistr1 = JsonPoolSetupFilters.PsDistr.builder().distr("AL").subDistrs(ImmutableSet.of(psSubDistr1, psSubDistr2)).build();
		assertThat(json.getDistrs()).contains(psDistr1);
	}

	@Test
	public void test_getTueFilters() {
		mockGetRequestURL();
		val filt1 = TueFilter.builder().distr("AL").subDistr("BI").build();
		val filt2 = TueFilter.builder().distr("AL").subDistr("CI").build();
		val filters = ImmutableList.of(filt1, filt2);
		when(planReportManager.getTueFiltersByPlanId(0L)).thenReturn(filters);
		val resp = planRestService.getTueFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		val tueDistr = JsonTueFilters.TueDistr.builder().distr("AL").subDistrs(ImmutableSet.of("BI", "CI")).build();
		assertThat(json.getDistrs()).contains(tueDistr);
	}

	@Test
	public void test_getWrpFilters() {
		mockGetRequestURL();
		val filt1 = WrpFilter.builder().distr("AL").subDistr("BI").poolName("CC").build();
		val filt2 = WrpFilter.builder().distr("AL").subDistr("BI").poolName("DD").build();
		val filters = ImmutableList.of(filt1, filt2);
		when(planReportManager.getWrpFiltersByPlanId(0L)).thenReturn(filters);

		val resp = planRestService.getWrpFilters(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		val sd1 = JsonWrpFilters.WrpSubDistr.builder().subDistr("BI").poolNames(ImmutableSet.of("CC", "DD")).build();
		val wrpDistr = JsonWrpFilters.WrpDistr.builder().distr("AL").subDistrs(ImmutableSet.of(sd1)).build();
		assertThat(json.getDistrs()).contains(wrpDistr);
	}
	@Test
	public void test_getHubDetail() {
		val extraboardInfo = ExtraboardInfo.builder().
				exbPoolSetUpId(1l).
				poolName("NS").
				distr("AL").
				subDistr("BI").
				build();
		val pool = PoolInfo.builder().
				poolName("NS").
				subDistr("BI").
				distr("AL").
				poolId(1l).
				build();
		val hub = Hub.builder().
				hubId(6038l).
				hubName("hub_18861_1").
				build();
		val hubDetails = HubDetail.builder().
				extraboards(ImmutableSet.of(extraboardInfo)).
				pools(ImmutableSet.of(pool)).
				hub(hub).
				build();
		when(planManager.getHubDetails(0L)).thenReturn(hubDetails);
		val resp = planRestService.getHubDetail(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val json = resp.getBody();
		assertThat(json.getHubName()).isEqualTo("hub_18861_1");
	}
	@Test
	public void test_getHubDetail_noMatchingResult() {
		val resp = planRestService.getHubDetail(1L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
		val json = resp.getBody();
	}
	@Test
	public void test_getCrewProfileByCrewProfileId_noMatchingResult() {
		mockGetRequestURL();
		val resp = planRestService.getCrewProfileByCrewProfileId(0L, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
	}
	@Test
	public void test_createCrewProfile() {
		val jsonCrewProfile = JsonCrewProfile.builder().
				build();
		val crewProfile = CrewProfile.builder().
				crewProfileId(1l).
				distr("AL").
				subDistr("BI").
				build();
		when(jsonPlanMapper.fromJson(jsonCrewProfile)).thenReturn(crewProfile);
		when(planManager.saveIfNotLocked(any(CrewProfile.class))).thenReturn(crewProfile);
		when(jsonPlanMapper.toJson(crewProfile)).thenReturn(jsonCrewProfile);
		val resp = planRestService.createCrewProfile(jsonCrewProfile);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getCrewProfileId()).isEqualTo(1l);
	}
	@Test
	public void test_updateCrewProfile() {
		val jsonCrewProfile = JsonCrewProfile.builder().
				build();
		val crewProfile = CrewProfile.builder().
				crewProfileId(1l).
				distr("AL").
				subDistr("BI").
				build();
		when(jsonPlanMapper.fromJson(jsonCrewProfile)).thenReturn(crewProfile);
		when(planManager.saveIfNotLocked(any(CrewProfile.class))).thenReturn(crewProfile);
		val resp = planRestService.updateCrewProfile(1l, jsonCrewProfile);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
	}
	@Test
	public void test_deleteCrewProfileId() {
		doNothing().when(planManager).deleteCrewProfileByIdIfNotLocked(1L);
		val resp = planRestService.deleteCrewProfileId(1L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().isSuccessful()).isTrue();
	}
}
