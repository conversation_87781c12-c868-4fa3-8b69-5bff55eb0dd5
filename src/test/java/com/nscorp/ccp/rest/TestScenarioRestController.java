package com.nscorp.ccp.rest;

import com.google.common.collect.*;
import com.nscorp.ccp.biz.job.JobDetailBuilder;
import com.nscorp.ccp.biz.job.JobManager;
import com.nscorp.ccp.biz.opd.OpdIntegrationManager;
import com.nscorp.ccp.biz.scenario.*;
import com.nscorp.ccp.common.job.JobDetail;
import com.nscorp.ccp.common.job.JobSummary;
import com.nscorp.ccp.common.opd.OpdScenario;
import com.nscorp.ccp.common.plan.Hub;
import com.nscorp.ccp.common.scenario.*;
import com.nscorp.ccp.common.scenario.filter.*;
import com.nscorp.ccp.logic.scenario.DefaultScenarioCfg;
import com.nscorp.ccp.rest.config.Constants;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.json.filter.JsonSimulationTrainFilter;
import com.nscorp.ccp.rest.json.filter.JsonTurnUtilizationFilter;
import com.nscorp.ccp.rest.mappers.*;
import com.nscorp.ccp.testing.util.DateTimeProviderMockImpl;
import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.utils.commonTypes.*;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import com.nscorp.ccp.utils.rest.RestMethodRunnerImpl;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.*;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;

import static com.nscorp.ccp.logic.scenario.ScenarioTestUtils.getSampleScenario;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@Tag("fast")
@Slf4j
public class TestScenarioRestController {
	private HttpServletRequest request;

	@BeforeEach
	public void init() {
		LogOutputInitializer.init();
		request = mock(HttpServletRequest.class);
		opdIntegrationManager = mock(OpdIntegrationManager.class);
		jsonPlanMapper = new AbstractJsonPlanMapperImpl();
		manager = mock(ScenarioManager.class);
		updater = mock(ScenarioUpdater.class);
		reportManager = mock(ScenarioReportManager.class);
		mapper = new JsonScenarioMapperImpl();
		jsonScenarioCustomMapper = new JsonScenarioCustomMapper(mapper);
		dateTimeProvider = new DateTimeProviderMockImpl();
		scenarioCanceller = mock(ScenarioCanceller.class);
		jobManager = mock(JobManager.class);
		jobDetailBuilder = mock(JobDetailBuilder.class);
		scenarioRestService = ScenarioRestService.builder().
				manager(manager).
				scenarioUpdater(updater).
				reportManager(reportManager).
				mapper(mapper).
				opdIntegrationManager(opdIntegrationManager).
				jsonPlanMapper(jsonPlanMapper).
				jsonScenarioCustomMapper(jsonScenarioCustomMapper).
				dateTimeProvider(dateTimeProvider).
				scenarioCanceller(scenarioCanceller).
				jobManager(jobManager).
				runner(new RestMethodRunnerImpl()).
				jobDetailBuilder(jobDetailBuilder).
				build();
	}
	private JobDetailBuilder jobDetailBuilder;
	private ScenarioRestService scenarioRestService;
	private ScenarioManager manager;
	private ScenarioUpdater updater;
	private ScenarioReportManager reportManager;
	private JsonScenarioMapper mapper;
	private OpdIntegrationManager opdIntegrationManager;
	private JsonPlanMapper jsonPlanMapper;
	private JsonScenarioCustomMapper jsonScenarioCustomMapper;
	private DateTimeProvider dateTimeProvider;
	private ScenarioCanceller scenarioCanceller;
	private JobManager jobManager;
	private DefaultScenarioCfg defaultScenarioCfg = new DefaultScenarioCfg(); // diremoval

	private void mockGetRequestURL() {
		when(request.getRequestURL()).thenReturn(new StringBuffer("http://localhost:8099"));
	}

	@Test
	public void test_getAllOpdScenarios() {
		mockGetRequestURL();
		val opdScen1 = OpdScenario.builder().
				opdScenarioId(0L).
				scenarioName("scen1").
				planName("plan1").
				studyName("study1").
				studyStartDt(LocalDate.of(2022, 12, 1)).
				studyEndDt(LocalDate.of(2022, 12, 15)).
				targetStartDt(LocalDate.of(2022, 12, 1)).
				targetEndDt(LocalDate.of(2022, 12, 15)).
				studySa(1).
				scenSa(1).
				planSa(1).
				build();
		when(opdIntegrationManager.getScenarios()).thenReturn(ImmutableList.of(opdScen1));
		val resp = scenarioRestService.getAllOpdScenarios(request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		assertThat(resp.getBody().getCount() > 0 );
		val result0 = (JsonOpdScenario)resp.getBody().getItems().get(0);
		assertThat(result0.getScenarioName()).isEqualTo("scen1");
		assertThat(result0.getPlanName()).isEqualTo("plan1");
		assertThat(result0.getStudyName()).isEqualTo("study1");
	}

	@Test
	public void test_getAllOpdTrnTypes() {
		mockGetRequestURL();
		when(opdIntegrationManager.getOpdTrnTypes()).thenReturn(ImmutableSet.of("IM", "MER"));

		val resp = scenarioRestService.getAllOpdTrnTypes(request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getItems()).contains("IM");
		assertThat(body.getItems()).contains("MER");
	}

	@Test
	public void test_updateScenario() {
		mockGetRequestURL();
		val scen = getSampleScenario();
		when(updater.updateScenario(0L, "newname", "newdesc", "user2")).thenReturn(scen);
		when(updater.updateScenario(99L, "newname", "newdesc", "user2")).thenThrow(new IllegalArgumentException());
		val resp = scenarioRestService.updateScenario(0L, "newname", "newdesc", "user2");
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);

		// Try a scenario that doesn't exist.
		val resp2 = scenarioRestService.updateScenario(99L, "newname", "newdesc", "user2");
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
	}

	@Test
	public void test_deleteScenario() {
		val scen = getSampleScenario();
		when(manager.markScenarioDeleted(0L)).thenReturn(scen);
		val resp = scenarioRestService.deleteScenario(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		verify(manager).markScenarioDeleted(0L);
	}

	@Test
	public void test_cancelScenario() {
		val scen = getSampleScenario();
		when(scenarioCanceller.cancelScenario(0L)).thenReturn(scen);
		val resp = scenarioRestService.cancelScenario(0L);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.isSuccessful()).isTrue();
		verify(scenarioCanceller).cancelScenario(0L);

		// Try a scenario that doesn't exist.
		val resp2 = scenarioRestService.cancelScenario(99L);
		assertThat(resp2.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body2 = resp2.getBody();
		assertThat(body2.isSuccessful()).isFalse();
	}

	@Test
	public void test_getInProgressScenarioDetails() {
		mockGetRequestURL();
		val sd1 = ScenarioDetail.builder().
				scenarioId(0L).
				status(ScenarioStatus.COMPLETE).
				scenName("scen1").
				studyName("study1").
				userId("sys").
				build();
		when(reportManager.getScenarioDetailsByStatusInProgress()).thenReturn(ImmutableList.of(sd1));
		val resp = scenarioRestService.getInProgressScenarioDetails(request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val detail = (JsonScenarioDetail)resp.getBody().getItems().get(0);
		assertThat(detail.getId()).isEqualTo(0L);
		assertThat(detail.getName()).isEqualTo("scen1");
		assertThat(detail.getStatus()).isEqualTo("COMPLETE");
		assertThat(detail.getUserId()).isEqualTo("sys");

		// val scenarioDetails = reportManager.getScenarioDetailsByStatusInProgress();
	}

	@Test
	public void test_getBoardSummaries_totalStarts() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final long planId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		val bs1 = BoardSummaryReportSummary.builder().
				boardType("POOL").
				boardName("C1").
				distr("AL").
				subDistr("BI").
				craft("CO").
				currentBoardSize(5.0).
				nbrOfStarts(20.0).
				breakEvenPoint(5.0).
				deltaBoardSize(10.0).
				startType(Constants.TOTAL_STARTS).
				build();
		when(reportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				null
		)).thenReturn(ImmutableList.of(bs1));
		val resp = scenarioRestService.getBoardSummaries(scenarioId, planId, Constants.TOTAL_STARTS, "ALL", "ALL", "ALL", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportSummary)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("C1");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getBoardSummaries_avgStartsPerTrain() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final long planId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		val bs1 = BoardSummaryReportSummary.builder().
				boardType("POOL").
				boardName("C1").
				distr("AL").
				subDistr("BI").
				craft("CO").
				currentBoardSize(5.0).
				nbrOfStarts(20.0).
				breakEvenPoint(5.0).
				deltaBoardSize(10.0).
				startType(Constants.AVG_STARTS_PER_WEEK).
				build();
		when(reportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				null
		)).thenReturn(ImmutableList.of(bs1));
		val resp = scenarioRestService.getBoardSummaries(scenarioId, planId, Constants.AVG_STARTS_PER_WEEK, "ALL", "ALL", "ALL", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportSummary)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("C1");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getBoardSummaryExportDetails_totalStarts() {
		final long scenarioId = 0L;
		final long planId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		val bs1 = BoardSummaryReportSummary.builder().
				boardType("POOL").
				boardName("C1").
				distr("AL").
				subDistr("BI").
				craft("CO").
				currentBoardSize(5.0).
				nbrOfStarts(20.0).
				breakEvenPoint(5.0).
				deltaBoardSize(10.0).
				startType(Constants.TOTAL_STARTS).
				build();
		when(reportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				ScenarioReportManager.EXPORT_DETAILS
		)).thenReturn(ImmutableList.of(bs1));

		val resp = scenarioRestService.getBoardSummaryExportDetails(scenarioId, planId, Constants.TOTAL_STARTS, "ALL", "ALL", "ALL", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportSummary)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("C1");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getBoardSummaryExportDetails_avgStartsPerWeek() {
		final long scenarioId = 0L;
		final long planId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		val bs1 = BoardSummaryReportSummary.builder().
				boardType("POOL").
				boardName("C1").
				distr("AL").
				subDistr("BI").
				craft("CO").
				currentBoardSize(5.0).
				nbrOfStarts(20.0).
				breakEvenPoint(5.0).
				deltaBoardSize(10.0).
				startType(Constants.AVG_STARTS_PER_WEEK).
				build();
		when(reportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				ScenarioReportManager.EXPORT_DETAILS
		)).thenReturn(ImmutableList.of(bs1));

		val resp = scenarioRestService.getBoardSummaryExportDetails(scenarioId, planId, Constants.AVG_STARTS_PER_WEEK, "ALL", "ALL", "ALL", request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportSummary)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("C1");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getBoardSummaryDetails_totalStarts() {
		final long scenarioId = 0L;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft =  null;
		val bs1 = BoardSummaryReportDetail.builder().
				startType("ST").
				nbrOfStarts(20.0).
				build();
		when(reportManager.getBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft(
				scenarioId,
				boardName,
				distr,
				subDistr,
				craft
		)).thenReturn(ImmutableList.of(bs1));
		val resp = scenarioRestService.getBoardSummaryDetails(scenarioId, Constants.TOTAL_STARTS, boardName, distr, subDistr, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportDetail)resp.getBody().getItems().get(0);
		assertThat(item0.getStartType()).isEqualTo("ST");
		assertThat(item0.getNbrOfStarts()).isEqualTo(20.0);
	}

	@Test
	public void test_getBoardSummaryDetails_avgStartsPerWeek() {
		final long scenarioId = 0L;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft =  null;
		val bs1 = BoardSummaryReportDetail.builder().
				startType("ST").
				nbrOfStarts(20.0).
				build();
		when(
				reportManager.getBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft(
						scenarioId,
						 boardName,
						 distr,
						 subDistr,
						 craft
				)
		).thenReturn(ImmutableList.of(bs1));
		val resp = scenarioRestService.getBoardSummaryDetails(scenarioId, Constants.AVG_STARTS_PER_WEEK, boardName, distr, subDistr, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonBoardSummaryReportDetail)resp.getBody().getItems().get(0);
		assertThat(item0.getStartType()).isEqualTo("ST");
		assertThat(item0.getNbrOfStarts()).isEqualTo(20.0);
	}

	@Test
	public void test_getTrainBalanceByScenario_totalStarts() {
		mockGetRequestURL();
		// trainBalances = reportManager.getTrainBalanceSimulationTotalsByScenarioId(scenarioId);
		final long scenarioId = 0L;
		val tb1 = TrainBalance.builder().
				trainType("MER").
				crewOs("148H").
				lineSegment("L1").
				inbound(1.0).
				outbound(1.0).
				difference(2.0).
				build();
		when(reportManager.getTrainBalanceSimulationTotalsByScenarioId(scenarioId)).thenReturn(ImmutableList.of(tb1));
		val resp = scenarioRestService.getTrainBalanceByScenario(scenarioId,
				Constants.TOTAL_STARTS,
				Constants.TBR_BALANCE_PRD_SIMULATION,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainBalance)resp.getBody().getItems().get(0);
		assertThat(item0.getTrainType()).isEqualTo("MER");
		assertThat(item0.getCrewOs()).isEqualTo("148H");
	}

	@Test
	public void test_getTrainBalanceByScenario_avgStartsPerWeek() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val tb1 = TrainBalance.builder().
				trainType("MER").
				crewOs("148H").
				lineSegment("L1").
				inbound(1.0).
				outbound(1.0).
				difference(2.0).
				build();
		when(reportManager.getTrainBalanceSimulationAvgsByScenarioId(scenarioId)).thenReturn(ImmutableList.of(tb1));
		val resp = scenarioRestService.getTrainBalanceByScenario(scenarioId,
				Constants.AVG_STARTS_PER_WEEK,
				Constants.TBR_BALANCE_PRD_SIMULATION,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainBalance)resp.getBody().getItems().get(0);
		assertThat(item0.getTrainType()).isEqualTo("MER");
		assertThat(item0.getCrewOs()).isEqualTo("148H");
	}

	@Test
	public void test_getTrainBalanceByStudy_totalStarts() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val tb1 = TrainBalance.builder().
				trainType("MER").
				crewOs("148H").
				lineSegment("L1").
				inbound(1.0).
				outbound(1.0).
				difference(2.0).
				build();
		when(reportManager.getTrainBalanceInputTrnTotalsByStudyId(scenarioId)).thenReturn(ImmutableList.of(tb1));
		val resp = scenarioRestService.getTrainBalanceByStudy(scenarioId,
				Constants.TOTAL_STARTS,
				Constants.TBR_BALANCE_PRD_IP_TRN,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainBalance)resp.getBody().getItems().get(0);
		assertThat(item0.getTrainType()).isEqualTo("MER");
		assertThat(item0.getCrewOs()).isEqualTo("148H");
	}

	@Test
	public void test_getDeadheadReport_totalStarts() {
		final long scenarioId = 0L;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft = null;
		val dh1 = Deadhead.builder().
				distr("AL").
				subDistr("BI").
				boardName("CC").
				craft("CO").
				count(10.0).
				build();
		when(reportManager.getDeadheadTotalStartsReport(
				scenarioId,
				distr,
				 subDistr,
				 boardName,
				 craft
		)).thenReturn(ImmutableList.of(dh1));

		val resp = scenarioRestService.getDeadheadReport(scenarioId, Constants.TOTAL_STARTS,
				distr, subDistr, boardName, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonDeadhead)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("CC");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getDeadheadReport_avgStartsPerWeek() {
		final long scenarioId = 0L;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft = null;
		val dh1 = Deadhead.builder().
				distr("AL").
				subDistr("BI").
				boardName("CC").
				craft("CO").
				count(10.0).
				build();
		when(reportManager.getDeadheadAvgStartsPerWeekReport(
				scenarioId,
				distr,
				subDistr,
				boardName,
				craft
		)).thenReturn(ImmutableList.of(dh1));

		val resp = scenarioRestService.getDeadheadReport(scenarioId, Constants.AVG_STARTS_PER_WEEK,
				distr, subDistr, boardName, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonDeadhead)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("CC");
		assertThat(item0.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getTrainDelayReport_duration_district() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainType = null;
		val td1 = TrainDelay.builder().
				distr("AL").
				lineSegment("L1").
				delayDuration(1.0).
				delayPercentage(10.0).
				build();
		when(reportManager.getTrainDelayDistrictDurationByScenarioIdTrainType(
				scenarioId,
				 trainType)).thenReturn(ImmutableList.of(td1));

		val resp = scenarioRestService.getTrainDelayReport(scenarioId,
				trainType,
				Constants.DURATION,
				Constants.DISTRICT,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainDelay)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getLineSegment()).isEqualTo("L1");
	}

	@Test
	public void test_getTrainDelayReport_duration_lineSeg() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainType = null;
		val td1 = TrainDelay.builder().
				distr("AL").
				lineSegment("L1").
				delayDuration(1.0).
				delayPercentage(10.0).
				build();
		when(reportManager.getTrainDelayLineSegDurationByScenarioIdTrainType(
				scenarioId,
				trainType)).thenReturn(ImmutableList.of(td1));

		val resp = scenarioRestService.getTrainDelayReport(scenarioId,
				trainType,
				Constants.DURATION,
				Constants.LINESEG,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainDelay)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getLineSegment()).isEqualTo("L1");
	}

	@Test
	public void test_getTrainDelayReport_rate_district() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainType = null;
		val td1 = TrainDelay.builder().
				distr("AL").
				lineSegment("L1").
				delayDuration(1.0).
				delayPercentage(10.0).
				build();
		when(reportManager.getTrainDelayDistrictPercentageByScenarioIdTrainType(
				scenarioId,
				trainType)).thenReturn(ImmutableList.of(td1));

		val resp = scenarioRestService.getTrainDelayReport(scenarioId,
				trainType,
				Constants.RATE,
				Constants.DISTRICT,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainDelay)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getLineSegment()).isEqualTo("L1");
	}

	@Test
	public void test_getTrainDelayReport_rate_lineSeg() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainType = null;
		val td1 = TrainDelay.builder().
				distr("AL").
				lineSegment("L1").
				delayDuration(1.0).
				delayPercentage(10.0).
				build();
		when(reportManager.getTrainDelayLineSegPercentageByScenarioIdTrainType(
				scenarioId,
				trainType)).thenReturn(ImmutableList.of(td1));

		val resp = scenarioRestService.getTrainDelayReport(
				scenarioId,
				trainType,
				Constants.RATE,
				Constants.LINESEG,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainDelay)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getLineSegment()).isEqualTo("L1");
	}

	@Test
	public void test_getSimulationTrainReport() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String distr = null;
		final String trainType = null;
		final String lineSegment = null;
		final String trainId = null;
		final String fromOs = null;
		final String delayType = null;
		val str1 = SimulationTrainReport.builder().
				fromOs("148H").
				toOs("148H").
				trn("21N").
				crewOs("242H").
				build();
		doReturn(ImmutableList.of(str1)).when(reportManager).getSimulationTrainReport(
				any(),
				 any(),
				 any(),
				 any(),
				 any(),
				 any(),
				any()
		);
		val resp = scenarioRestService.getSimulationTrainReport(
				scenarioId,
				trainType,
				lineSegment,
				trainId,
				distr,
				delayType,
				fromOs,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonSimulationTrainReport)resp.getBody().getItems().get(0);
		assertThat(item0.getFromOs()).isEqualTo("148H");
		assertThat(item0.getToOs()).isEqualTo("148H");
		assertThat(item0.getTrn()).isEqualTo("21N");
		assertThat(item0.getCrewOs()).isEqualTo("242H");
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainStartsType = Constants.TOTAL_STARTS;
		final String groupBy = Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_SYSTEM;
		final String craft = "CO";
		val result0 = TrainStatisticsSummaryByPool.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				craft(Craft.CO).
				build();
		doReturn(ImmutableList.of(result0)).when(reportManager).getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
				any(),
				any(),
				any() );
		val resp = scenarioRestService.getTrnStatSumByPoolRpt(scenarioId, trainStartsType, groupBy, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainStatisticsSummaryByPool)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String trainStartsType = Constants.AVG_STARTS_PER_WEEK;
		final String groupBy = Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_SYSTEM;
		final String craft = "CO";
		val result0 = TrainStatisticsSummaryByPool.builder().
				distr("AL").
				subDistr("BI").
				poolName("CC").
				craft(Craft.CO).
				build();
		doReturn(ImmutableList.of(result0)).when(reportManager).getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
				any(),
				any(),
				any() );
		val resp = scenarioRestService.getTrnStatSumByPoolRpt(scenarioId, trainStartsType, groupBy, craft, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTrainStatisticsSummaryByPool)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
	}

	@Test
	public void test_simulationTrainReportUtils() {
		assertThat(SimulationTrainReportUtils.mapDelay(Constants.ALL)).isEqualTo(Constants.ALL);
		assertThat(SimulationTrainReportUtils.mapDelay(Constants.RECREW_DELAY)).isEqualTo(Constants.RC_DELAY);
		assertThat(SimulationTrainReportUtils.mapDelay(Constants.DELAY_DUE_TO_CREW)).isEqualTo(Constants.CR_DELAY);
		assertThat(SimulationTrainReportUtils.mapDelay(Constants.NO_DELAY)).isEqualTo(Constants.NO_DELAY);
		assertThat(SimulationTrainReportUtils.mapDelay(Constants.NA_DELAY)).isEqualTo(Constants.NA_DELAY);
	}

	@Test
	public void test_tsbppUtils() {
		assertThat(TssbpUtils.mapGroupByToSummaryLevel(Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_SYSTEM)).isEqualTo(Constants.DB_TSSBP_SYSTEM);
		assertThat(TssbpUtils.mapGroupByToSummaryLevel(Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_DISTR)).isEqualTo(Constants.DB_TSSBP_DISTR);
		assertThat(TssbpUtils.mapGroupByToSummaryLevel(Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_SUB_DISTR)).isEqualTo(Constants.DB_TSSBP_SUB_DISTR);
		assertThat(TssbpUtils.mapGroupByToSummaryLevel(Constants.TRN_STAT_SUM_BY_POOL_GROUP_BY_POOL)).isEqualTo(Constants.DB_TSSBP_POOL);
	}

	@Test
	public void test_getTurnUtilization_totalStarts() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft = null;
		final String trainStartsType = Constants.TOTAL_STARTS;

		val tu1 = TurnUtilization.builder().
				turnUtilizationId(0L).
				distr("AL").
				subDistr("BI").
				boardName("CC").
				hubId(0L).
				turnId("A01").
				boardType("POOL").
				build();

		when(manager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
				scenarioId,
				 boardType,
				 distr,
				 subDistr,
				 boardName,
				 craft
		)).thenReturn(ImmutableList.of(tu1));

		val resp = scenarioRestService.getTurnUtilization(scenarioId,
				trainStartsType,
				boardType,
				boardName,
				distr,
				subDistr,
				craft,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTurnUtilization)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("CC");
	}


	@Test
	public void test_getTurnUtilization_avgStartsPerWeek() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		final String boardName = null;
		final String craft = null;
		final String trainStartsType = Constants.AVG_STARTS_PER_WEEK;

		val tu1 = TurnUtilization.builder().
				turnUtilizationId(0L).
				distr("AL").
				subDistr("BI").
				boardName("CC").
				hubId(0L).
				turnId("A01").
				boardType("POOL").
				build();

		when(manager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
				scenarioId,
				boardType,
				distr,
				subDistr,
				boardName,
				craft
		)).thenReturn(ImmutableList.of(tu1));

		val resp = scenarioRestService.getTurnUtilization(scenarioId,
				trainStartsType,
				boardType,
				boardName,
				distr,
				subDistr,
				craft,
				request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonTurnUtilization)resp.getBody().getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("CC");
	}

	@Test
	public void test_getDefaultScenarioCfg() {
		val resp = scenarioRestService.getDefaultScenarioCfg();
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
	}

	@Test
	public void test_getScenarioCfg() {
		final long scenarioId = 0L;
		val scen = getSampleScenario();
		when(manager.getScenarioById(scenarioId)).thenReturn(scen);
		val resp = scenarioRestService.getScenarioCfg(scenarioId);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getCommutePctg()).isEqualTo(50.0);
	}

	@Test
	public void test_getScenarioSelectors() {
		mockGetRequestURL();
		val sel1 = ScenarioSelector.builder().
				scenarioId(0L).
				planId(0L).
				scenName("scen").
				studyName("study").
				planName("plan").
				userId("sys").
				build();
		when(reportManager.getScenarioSelectorsByStatus(ScenarioStatus.COMPLETE)).thenReturn(ImmutableList.of(sel1));
		val resp = scenarioRestService.getScenarioSelectors(request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val item0 = (JsonScenarioSelector)resp.getBody().getItems().get(0);
		assertThat(item0.getPlanId()).isEqualTo(0L);
		assertThat(item0.getUserId()).isEqualTo("sys");
	}

	@Test
	public void test_getBoardSummaryFilters() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val bsf = BoardSummaryFilter.builder().
				distr("AL").
				subDistr("BI").
				build();
		when(reportManager.getBoardSummaryFiltersByScenarioId(scenarioId)).thenReturn(ImmutableList.of(bsf));
		val resp = scenarioRestService.getBoardSummaryFilters(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		val distrs = body.getDistrs();
		val bsDistr = Iterables.getFirst(distrs, null);
		assertThat(bsDistr.getDistr()).isEqualTo("AL");
	}

	@Test
	public void test_getDeadheadFilters() {
		final long scenarioId = 0L;
		val df1 = DeadheadFilter.builder().
				distr("AL").
				subDistr("BI").
				boardName("CC").
				build();
		when(reportManager.getDeadheadFiltersByScenarioId(scenarioId)).thenReturn(ImmutableList.of(df1));
		val resp = scenarioRestService.getDeadheadFilters(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		val dhDistr = Iterables.getFirst(body.getDistrs(), null);
		assertThat(dhDistr.getDistr()).isEqualTo("AL");
	}

	@Test
	public void test_getTrainDelayFilters() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val stf1 = SimulationTrainFilter.builder().distr("AL").lineSegment("L1").fromOs("148H").trnSymb("21M").trnType("MER").build();
		when(reportManager.getSimulationTrainTrnTypesByScenarioId(scenarioId)).thenReturn(ImmutableList.of(stf1));
		val resp = scenarioRestService.getTrainDelayFilters(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getTrnTypes()).contains("MER");
	}

	@Test
	public void test_getSimulationTrainFilters() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val stf1 = SimulationTrainFilter.builder().distr("AL").lineSegment("L1").fromOs("148H").trnSymb("21M").trnType("MER").build();
		when(reportManager.getSimulationTrainFiltersByScenarioId(scenarioId)).thenReturn(ImmutableList.of(stf1));
		val resp = scenarioRestService.getSimulationTrainFilters(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		val item0 = (JsonSimulationTrainFilter)body.getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
	}

	@Test
	public void test_getTurnUtilizationFilters() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val tuf = TurnUtilizationFilter.builder().
				distr("AL").
				subDistr("BI").
				boardName("CC").
				boardType("POOL").
				build();
		when(reportManager.getTurnUtilizationFiltersByScenarioId(scenarioId)).thenReturn(ImmutableList.of(tuf));
		val resp = scenarioRestService.getTurnUtilizationFilters(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		val item0 = (JsonTurnUtilizationFilter)body.getItems().get(0);
		assertThat(item0.getDistr()).isEqualTo("AL");
		assertThat(item0.getSubDistr()).isEqualTo("BI");
		assertThat(item0.getBoardName()).isEqualTo("CC");
	}

	@Test
	public void test_getJobSummaryByScenarioId() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val jobSummary = JobSummary.builder().
				jobId(0L).
				hubId(0L).
				scenarioId(0L).
				status(JobStatus.RUNNING).
				build();
		val hub = Hub.builder().hubId(0L).hubName("hub0").description("desc").planId(0L).build();
		val jobDetail = JobDetail.builder().
				jobId(0L).
				hub(hub).
				jobSummary(jobSummary).
				pools(ImmutableSet.of()).
				extraboards(ImmutableSet.of()).
				build();
		when(jobDetailBuilder.getJobDetails(0L)).thenReturn(ImmutableList.of(jobDetail));
		val js1 = JobSummary.builder().jobId(0L).jobmgrJobId(0L).jobmgrJobStatus(JobmgrJobStatus.SUCCEEDED).scenarioId(0L).hubId(0L).status(JobStatus.RESULTS_LOADED).build();
		when(jobManager.getJobSummariesByScenarioId(scenarioId)).thenReturn(ImmutableList.of(js1));
		val resp = scenarioRestService.getJobSummaryByScenarioId(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = resp.getBody();
		assertThat(body.getSuccessHubsCnt()).isEqualTo(0);
	}
	@Test
	public void test_getCrewStartsByHireGrp() {
		mockGetRequestURL();
		final long scenarioId = 0L;
	    val crewStartsByHireGrp = CrewStartsByHireGrp.builder().
				hireGroup("test").
				completedCrewStarts(10.2).
				incompletedCrewStarts(1.3).scenarioId(0L).
				build();
		when(reportManager.getCrewStartsByHireGrpByScenarioId(0L)).thenReturn(ImmutableList.of(crewStartsByHireGrp));
		val resp = scenarioRestService.getCrewStartsByHireGrp(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val jsonCrewStartsByHireGrp = (JsonCrewStartsByHireGrp)resp.getBody().getItems().get(0);;
		assertThat(jsonCrewStartsByHireGrp.getHireGroup()).isEqualTo("test");
	}
	@Test
	public void test_getTargetHeadCountByHireGrp() {
		mockGetRequestURL();
		final long scenarioId = 0L;
		val targetHeadCountByHireGrp = TargetHeadCountByHireGrp.builder().
				hireGrpDesc("desc").
				scenarioId(0L).
				build();
		when(reportManager.getTargetHeadCountByHireGrpByScenarioId(0L)).thenReturn(ImmutableList.of(targetHeadCountByHireGrp));
		val resp = scenarioRestService.getTargetHeadCountByHireGrp(scenarioId, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val jsonTargetHeadCountByHireGrp = (JsonTargetHeadCountByHireGrp)resp.getBody().getItems().get(0);;
		assertThat(jsonTargetHeadCountByHireGrp.getHireGrpDesc()).isEqualTo("desc");
	}
}
