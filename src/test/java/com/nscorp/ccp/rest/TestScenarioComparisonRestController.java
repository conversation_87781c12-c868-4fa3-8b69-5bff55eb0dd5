package com.nscorp.ccp.rest;
import lombok.val;

import com.google.common.collect.*;
import com.nscorp.ccp.common.scenario.Scenario;
import com.nscorp.ccp.common.scenariocomparison.*;
import com.nscorp.ccp.logic.scenario.DefaultScenarioCfg;
import com.nscorp.ccp.biz.scenario.ScenarioManager;
import com.nscorp.ccp.biz.scenariocomparison.ScenarioComparisonManager;
import com.nscorp.ccp.rest.ScenarioComparisonRestService;
import com.nscorp.ccp.rest.config.Constants;
import com.nscorp.ccp.rest.json.*;
import com.nscorp.ccp.rest.mappers.*;
import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.utils.commonTypes.Craft;
import com.nscorp.ccp.utils.rest.RestMethodRunnerImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;

import static com.nscorp.ccp.testing.util.SeedDataUtils.*;
import static com.nscorp.ccp.testing.util.TestUtils.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@Tag("fast")
@Slf4j
public class TestScenarioComparisonRestController {
	private HttpServletRequest request;
	private ScenarioComparisonRestService service;
	private ScenarioComparisonManager manager;
	private ScenarioManager scenarioManager;

	@BeforeEach
	public void init() {
		LogOutputInitializer.init();
		request = mock(HttpServletRequest.class);

		manager = mock(ScenarioComparisonManager.class);
		scenarioManager = mock(ScenarioManager.class);
		service = ScenarioComparisonRestService.builder().
				runner(new RestMethodRunnerImpl()).
				manager(manager).
				scenarioManager(scenarioManager).
				mapper(new JsonScenarioComparisonMapperImpl()).
				jsonScenarioMapper(new JsonScenarioMapperImpl()).
				build();
	}

	private void mockGetRequestURL() {
		when(request.getRequestURL()).thenReturn(new StringBuffer("http://localhost:8099"));
	}

	@Test
	public void test_getScenarioComparison_totalStarts_boardSummaryPerPool() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val bscomp1 = BoardSummaryComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				craft("CO").
				build();

		when(manager.getBoardSummaryCompTotalsByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario)).thenReturn(ImmutableList.of(bscomp1));

		val resp = service.getScenarioComparison(Constants.TOTAL_STARTS, Constants.SCENARIO_COMP_BOARD_SUMMARY_PER_POOL,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getBoardSummaryPerPool().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo("CO");
	}

	@Test
	public void test_getScenarioComparison_totalStarts_compDeadheadPerPool() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val dhcomp1 = DeadheadComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				craft("CO").
				testCount(1.0).
				build();

		when(manager.getDeadheadCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(dhcomp1));

		val resp = service.getScenarioComparison(Constants.TOTAL_STARTS, Constants.SCENARIO_COMP_DEADHEAD_PER_POOL,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getDeadheadPerPool().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo(Craft.CO);
		assertThat(first.getTestCount()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_totalStarts_compTrainDelay() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val trainDelayComp = TrainDelayComp.builder().
				distr("AL").
				lineSegment("L1").
				baseTrnCount(1.0).
				testTrnCount(2.0).
				build();

		when(manager.getTrainDelayCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(trainDelayComp));

		val resp = service.getScenarioComparison(Constants.TOTAL_STARTS, Constants.SCENARIO_COMP_TRAIN_DELAY,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getTrainDelay().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getLineSegment()).isEqualTo("L1");
		assertThat(first.getBaseTrnCnt()).isEqualTo(1.0);
		assertThat(first.getTestTrnCnt()).isEqualTo(2.0);
	}

	@Test
	public void test_getScenarioComparison_totalStarts_compTurnUtilPerPoolPerStatus() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val turnUtilComp1 = TurnUtilizationComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				baseDuration(1.0).
				testDuration(2.0).
				build();

		when(manager.getTurnUtilizationCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(turnUtilComp1));

		val resp = service.getScenarioComparison(Constants.TOTAL_STARTS, Constants.SCENARIO_COMP_TURN_UTIL_PER_POOL_PER_STATUS,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		// turnUtilPerPoolPerStatus
		// JsonTurnUtilizationComp
		val first = body.getTurnUtilPerPoolPerStatus().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getBaseDur()).isEqualTo(1.0);
		assertThat(first.getTestDur()).isEqualTo(2.0);
	}

	@Test
	public void test_getScenarioComparison_avgStartsPerWeek_compBoardSummaryPerPool() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val boardSummaryComp = BoardSummaryComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				craft("CO").
				baseNbrOfStarts(1.0).
				build();

		when(manager.getBoardSummaryCompAvgByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario)).thenReturn(ImmutableList.of(boardSummaryComp));

		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_BOARD_SUMMARY_PER_POOL,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		// turnUtilPerPoolPerStatus
		// JsonTurnUtilizationComp
		val first = body.getBoardSummaryPerPool().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo("CO");
		assertThat(first.getBaseNbrOfStarts()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_avgStartsPerWeek_compDeadheadPerPool() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val deadheadComp = DeadheadComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				craft("CO").
				baseCount(1.0).
				build();

		when(manager.getDeadheadCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(deadheadComp));

		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_DEADHEAD_PER_POOL,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getDeadheadPerPool().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo(Craft.CO);
		assertThat(first.getBaseCount()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_avgStartsPerWeek_compTrainDelay() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val trainDelayComp = TrainDelayComp.builder().
				distr("AL").
				lineSegment("L1").
				baseTrnCount(1.0).
				build();

		when(manager.getTrainDelayCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(trainDelayComp));

		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_TRAIN_DELAY,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getTrainDelay().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getLineSegment()).isEqualTo("L1");
		assertThat(first.getBaseTrnCnt()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_avgStartsPerWeek_compTrainStatsPerPool() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val trainStatsComp = TrainStatsComp.builder().
				distr("AL").
				subDistr("BI").
				poolName("C1").
				craft("CO").
				delayedTrn(1.0).
				build();

		when(manager.getTrainStatsCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(trainStatsComp));

		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_TRAIN_STATS_PER_POOL,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getTrainStatsPerPool().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getPool()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo("CO");
		assertThat(first.getDelayedTrn()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_avgStartsPerWeek_compTurnUtilPerPoolPerStatus() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val turnUtilizationComp = TurnUtilizationComp.builder().
				distr("AL").
				subDistr("BI").
				boardName("C1").
				craft("CO").
				baseDuration(1.0).
				build();

		when(manager.getTurnUtilizationCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario)).thenReturn(ImmutableList.of(turnUtilizationComp));

		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_TURN_UTIL_PER_POOL_PER_STATUS,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val first = body.getTurnUtilPerPoolPerStatus().get(0);
		assertThat(first.getDistr()).isEqualTo("AL");
		assertThat(first.getSubDistr()).isEqualTo("BI");
		assertThat(first.getBoardName()).isEqualTo("C1");
		assertThat(first.getCraft()).isEqualTo("CO");
		assertThat(first.getBaseDur()).isEqualTo(1.0);
	}

	@Test
	public void test_getScenarioComparison_scenarioCompScenarioSettings() {
		mockGetRequestURL();
		val baseScenario = SCENARIO_ID_LONG;
		val basePlan = PLAN_ID_LONG;
		val baseStudy = STUDY_ID_LONG;
		val testScenario = 1L;
		val testPlan = 1L;
		val testStudy = 1L;
		val defaultCfg = new DefaultScenarioCfg().getDefaultScenarioCfg();
		val scen1 = Scenario.builder().
				scenarioId(baseScenario).
				scenName("scen1").
				planId(basePlan).
				studyId(baseStudy).
				userId("sys").
				scenarioCfg(defaultCfg).
				build();
		val scen2 = Scenario.builder().
				scenarioId(testScenario).
				scenName("scen2").
				planId(testPlan).
				studyId(testStudy).
				userId("sys").
				scenarioCfg(defaultCfg).
				build();
		when(scenarioManager.getScenarioById(0L)).thenReturn(scen1);
		when(scenarioManager.getScenarioById(1L)).thenReturn(scen2);
		val resp = service.getScenarioComparison(Constants.AVG_STARTS_PER_WEEK, Constants.SCENARIO_COMP_SCENARIO_SETTINGS,
				baseScenario, testScenario, basePlan, testPlan, request);
		assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.OK);
		val body = (JsonScenarioComparison)resp.getBody();
		val scenarioSettings = body.getScenarioSettingsComp();
		assertThat(scenarioSettings.getBaseScenarioSettings().getDhCost()).isEqualTo(scen1.getScenarioCfg().getDhCost());
		assertThat(scenarioSettings.getTestScenarioSettings().getDhCost()).isEqualTo(scen2.getScenarioCfg().getDhCost());
	}
}
