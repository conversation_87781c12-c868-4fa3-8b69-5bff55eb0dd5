package com.nscorp.ccp.biz.impl.scenario;

import com.google.common.collect.Iterables;
import com.nscorp.ccp.biz.opd.OpdIntegrationManager;
import com.nscorp.ccp.biz.scenario.*;
import com.nscorp.ccp.biz.scenariocomparison.ScenarioComparisonManager;
import com.nscorp.ccp.biz.study.StudyReportManager;
import com.nscorp.ccp.common.crewpro.CrewProResponse;
import com.nscorp.ccp.common.scenario.CompleteScenario;
import com.nscorp.ccp.common.scenario.ScenarioStatus;
import com.nscorp.ccp.logic.modelJson.ModelJsonResponseParser;
import com.nscorp.ccp.logic.modelJson.ModelJsonToDomainObjectMapper;
import com.nscorp.ccp.rest.config.Constants;
import com.nscorp.ccp.testing.util.*;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

import static com.google.common.collect.ImmutableSet.copyOf;
import static com.nscorp.ccp.testing.util.SeedDataUtils.*;
import static com.nscorp.ccp.testing.util.SeedDataUtils.PLAN_ID_LONG;
import static com.nscorp.ccp.testing.util.TestUtils.*;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
// @EnabledIf(expression = "#{environment.acceptsProfiles('hsqldb-test')}", loadContext = true)
@Tag("slow")
@Transactional
public class TestScenario extends SpringBasedTest {

    @Autowired
    private TestInitializer testInitializer;

	@PostConstruct
	public void init() {
        testInitializer.initTests();
	}

	@Autowired
	private ScenarioManager scenarioManager;

	@Autowired
	private ScenarioCopier scenarioCopier;

	// @Autowired // diremoval
	private final ModelJsonToDomainObjectMapper mapper = new ModelJsonToDomainObjectMapper(); // diremoval

	@Autowired
	private DateTimeProvider dateTimeProvider;

	// @Autowired // diremoval
	private final ModelJsonResponseParser parser = new ModelJsonResponseParser(); // diremoval

	@Test
	public void test_ScenarioManager_getCompleteScenarioById() {
		final CompleteScenario completeScenario = scenarioManager.getCompleteScenario(0);
		assertThat(completeScenario).isNotNull();
		assertThat(completeScenario.getScenario().getScenarioId()).isEqualTo(0);
		assertThat(completeScenario.getBoardSummaries()).isNotEmpty();
		assertThat(completeScenario.getTurnUtilizations()).isNotEmpty();
		assertThat(completeScenario.getBoardTrainStarts()).isNotEmpty();
	}

    private CrewProResponse toCrewProResponse(CompleteScenario scen) {
        return CrewProResponse.builder().
                boardSummaries(copyOf(scen.getBoardSummaries())).
                boardTrainStarts(copyOf(scen.getBoardTrainStarts())).
                simulationTrainOutputs(copyOf(scen.getSimulationTrainOutputs())).
                turnUtilizations(copyOf(scen.getTurnUtilizations())).
		        requestId("hub1").
                build();
    }

	@Test
	public void test_ScenarioCopier() {
		val orig = scenarioManager.getCompleteScenario(0);
		assertThat(orig).isNotNull();
		val origResponse = toCrewProResponse(orig);
		Long copyId = null;
		val copy = scenarioCopier.copyScenario(0L);
		assertThat(copy).isNotNull();
		copyId = copy.getScenario().getScenarioId();
		val completeCopy = scenarioManager.getCompleteScenario(copyId);
		assertThat(completeCopy).isNotNull();
		val copyResponse = toCrewProResponse(completeCopy);
		val origResponseStr = parser.formatResponse(mapper.dtoToJson(dateTimeProvider.now(), origResponse));
		val copyResponseStr = parser.formatResponse(mapper.dtoToJson(dateTimeProvider.now(), copyResponse));

		// Assert that the original and copied scenarios are identical.
		assertThat(origResponseStr).isEqualTo(copyResponseStr);
	}

	@Autowired
	private ScenarioComparisonManager scenarioComparisonManager;

	@Test
	public void test_scenarioComparison_totals() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		val basePlan = PLAN_ID_LONG;
		val testPlan = 1L;
		runTestAndAssert("test_scenarioComparison_totalStarts", ()-> scenarioComparisonManager.getBoardSummaryCompTotalsByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario));
	}

	@Test
	public void test_scenarioComparison_avg() {
		val baseScenario = 0L;
		val testScenario = 0L;
		val basePlan = PLAN_ID_LONG;
		val testPlan = PLAN_ID_LONG;
		runTestAndAssert("test_scenarioComparison_avg", ()-> scenarioComparisonManager.getBoardSummaryCompAvgByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario));
	}

	@Autowired
	private ScenarioCreator scenarioCreator;

	@Autowired
	private OpdIntegrationManager opdIntegrationManager;

	@Test
	public void test_getAllOpdScenarios() {
		runTestAndAssert("test_getAllOpdScenarios", ()-> opdIntegrationManager.getScenarios());
	}

	@Test
	public void test_getOpdTrnTypes() {
		runTestAndAssert("test_getOpdTrnTypes", ()-> opdIntegrationManager.getOpdTrnTypes());
	}

	@Autowired
	private ScenarioReportManager scenarioReportManager;

	@Test
	public void test_getInProgressDetails() {
		runTestAndAssert("test_getInProgressDetails", ()-> scenarioReportManager.getScenarioDetailsByStatusInProgress());
	}

	@Test
	public void test_getBoardSummaries_all_totalStarts() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;

		runTestAndAssert("test_getBoardSummaries_all_totalStarts", ()->
			scenarioReportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				null));
	}

	@Test
	public void test_getBoardSummaries_totalStarts_boardType_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String distr = "HB";
		final String subDistr = "CR";
		final String boardType = Constants.POOL;

		runTestAndAssert("test_getBoardSummaries_totalStarts_boardType_distr_subDistr", ()->
				scenarioReportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
						scenarioId,
						planId,
						boardType,
						distr,
						subDistr,
						null));
	}

	@Test
	public void test_getBoardSummaries_all_avgStartsType() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String boardType = null;
		final String distr = null;
		final String subDistr = null;
		runTestAndAssert("test_getBoardSummaries_all_avgStartsType", ()->
				scenarioReportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
					scenarioId,
					planId,
					boardType,
					distr,
					subDistr,
					null
		));
	}

	@Test
	public void test_getBoardSummaries_avgStartsType_boardType_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val boardType = Constants.POOL;
		runTestAndAssert("test_getBoardSummaries_avgStartsType_boardType_distr_subDistr", ()-> scenarioReportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				null
		));
	}

	@Test
	public void test_getBoardSummaryExportDetails_all_totalStarts() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String boardType = null;
		runTestAndAssert("test_getBoardSummaryExportDetails_all_totalStarts", ()-> scenarioReportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				 boardType,
				 distr,
				 subDistr,
				ScenarioReportManager.EXPORT_DETAILS
		));
	}

	@Test
	public void test_getBoardSummaryExportDetails_totalStarts_boardType_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val boardType = Constants.POOL;
		runTestAndAssert("test_getBoardSummaryExportDetails_totalStarts_boardType_distr_subDistr", ()-> scenarioReportManager.getBoardSummaryTotalsByScenarioIdPlanIdBoardTypeDistrSubDistr(
				scenarioId,
				planId,
				boardType,
				distr,
				subDistr,
				ScenarioReportManager.EXPORT_DETAILS
		));
	}

	@Test
	public void test_getBoardSummaryExportDetails_all_avgStartsType() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String boardType = null;
		runTestAndAssert("test_getBoardSummaryExportDetails_all_avgStartsType", ()->
			scenarioReportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
					scenarioId,
					planId,
					boardType,
					distr,
					subDistr,
					ScenarioReportManager.EXPORT_DETAILS
			));
	}

	@Test
	public void test_getBoardSummaryExportDetails_avgStartsType_boardType_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		val planId = PLAN_ID_LONG;
		final String distr = "HB";
		final String subDistr = "CR";
		final String boardType = Constants.POOL;
		runTestAndAssert("test_getBoardSummaryExportDetails_avgStartsType_boardType_distr_subDistr", ()->
				scenarioReportManager.getBoardSummaryAvgByScenarioIdPlanIdBoardTypeDistrSubDistr(
						scenarioId,
						planId,
						boardType,
						distr,
						subDistr,
						ScenarioReportManager.EXPORT_DETAILS
				));
	}

	@Test
	public void test_getBoardSummaryDetails_totalStarts() {
		val scenarioId = SCENARIO_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val craft = "CO";
		val boardName = "CR";
		runTestAndAssert("test_getBoardSummaryDetails_totalStarts", ()->
			scenarioReportManager.getBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft(
				scenarioId,
				 boardName,
				 distr,
				 subDistr,
				 craft
		));
	}

	@Test
	public void test_getBoardSummaryDetails_all_totalStarts() {
		val scenarioId = SCENARIO_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		final String boardName = null;
		runTestAndAssert("test_getBoardSummaryDetails_all_totalStarts", ()->
				scenarioReportManager.getBoardSummaryDetailTotalsByScenarioIdBoardNameDistrSubDistrCraft(
						scenarioId,
						boardName,
						distr,
						subDistr,
						craft
				));
	}

	@Test
	public void test_getBoardSummaryDetails_avgStartsPerWeek() {
		val scenarioId = SCENARIO_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val craft = "CO";
		val boardName = "CR";
		runTestAndAssert("test_getBoardSummaryDetails_avgStartsPerWeek", ()->
				scenarioReportManager.getBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft(
						scenarioId,
						 boardName,
						 distr,
						 subDistr,
						 craft
		));
	}

	@Test
	public void test_getBoardSummaryDetails_all_avgStartsPerWeek() {
		val scenarioId = SCENARIO_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		final String boardName = null;
		runTestAndAssert("test_getBoardSummaryDetails_all_avgStartsPerWeek", ()->
				scenarioReportManager.getBoardSummaryDetailAvgByScenarioIdBoardNameDistrSubDistrCraft(
						scenarioId,
						boardName,
						distr,
						subDistr,
						craft
				));
	}

	@Test
	public void test_getTrainBalanceByScenario_totalStarts() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getTrainBalanceByScenario_totalStarts", ()->
				scenarioReportManager.getTrainBalanceSimulationTotalsByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getTrainBalanceByScenario_avgStartsPerWeek() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getTrainBalanceByScenario_avgStartsPerWeek", ()->
				scenarioReportManager.getTrainBalanceSimulationAvgsByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getTrainBalanceByStudy_totalStarts() {
		val studyId = STUDY_ID_LONG;
		runTestAndAssert("test_getTrainBalanceByStudy_totalStarts", ()->
				scenarioReportManager.getTrainBalanceInputTrnTotalsByStudyId(studyId)
		);
	}

	@Test
	public void test_getTrainBalanceByStudy_avgStartsPerWeek() {
		val studyId = STUDY_ID_LONG;
		runTestAndAssert("test_getTrainBalanceByStudy_avgStartsPerWeek", ()->
				scenarioReportManager.getTrainBalanceInputTrnAvgsByStudyId(studyId)
		);
	}

	@Test
	public void test_getDeadheadReportTotals_all() {
		val scenarioId = SCENARIO_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		final String boardName = null;
		runTestAndAssert("test_getDeadheadReportTotals_all", ()->
				scenarioReportManager.getDeadheadTotalStartsReport(
						scenarioId,
						 distr,
						 subDistr,
						 boardName,
						 craft
				)
		);
	}

	@Test
	public void test_getDeadheadReportTotals_specific() {
		val scenarioId = SCENARIO_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val craft = "CO";
		val boardName = "CO";
		runTestAndAssert("test_getDeadheadReportTotals_specific", ()->
				scenarioReportManager.getDeadheadTotalStartsReport(
						scenarioId,
						 distr,
						 subDistr,
						 boardName,
						 craft
				)
		);
	}

	@Test
	public void test_getDeadheadReportAvg_all() {
		val scenarioId = SCENARIO_ID_LONG;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		final String boardName = null;
		runTestAndAssert("test_getDeadheadReportAvg_all", ()->
				scenarioReportManager.getDeadheadAvgStartsPerWeekReport(
						scenarioId,
						 distr,
						 subDistr,
						 boardName,
						 craft
				)
		);
	}

	@Test
	public void test_getDeadheadReportAvg_specific() {
		val scenarioId = SCENARIO_ID_LONG;
		val distr = "HB";
		val subDistr = "CR";
		val craft = "CO";
		val boardName = "CO";
		runTestAndAssert("test_getDeadheadReportAvg_specific", ()->
				scenarioReportManager.getDeadheadAvgStartsPerWeekReport(
						scenarioId,
						 distr,
						 subDistr,
						 boardName,
						 craft
				)
		);
	}

	@Test
	public void test_getTrainDelayReport_duration_district() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		runTestAndAssert("test_getTrainDelayReport_duration_district", ()->
			scenarioReportManager.getTrainDelayDistrictDurationByScenarioIdTrainType(
				scenarioId,
				 trainType)
		);
	}

	@Test
	public void test_getTrainDelayReport_duration_lineSeg() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		runTestAndAssert("test_getTrainDelayReport_duration_lineSeg", ()->
				scenarioReportManager.getTrainDelayLineSegDurationByScenarioIdTrainType(
						scenarioId,
						 trainType)
		);
	}

	@Test
	public void test_getTrainDelayReport_rate_district() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		runTestAndAssert("test_getTrainDelayReport_rate_district", ()->
				scenarioReportManager.getTrainDelayDistrictPercentageByScenarioIdTrainType(
						scenarioId,
						trainType)
		);
	}

	@Test
	public void test_getTrainDelayReport_rate_lineSeg() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		runTestAndAssert("test_getTrainDelayReport_rate_lineSeg", ()->
				scenarioReportManager.getTrainDelayLineSegPercentageByScenarioIdTrainType(
						scenarioId,
						trainType)
		);
	}

	@Test
	public void test_getSimulationTrainReport_all() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		final String lineSegment = null;
		final String trainId = null;
		final String distr = null;
		final String delayType = null;
		final String fromOs = null;
		runTestAndAssert("test_getSimulationTrainReport_all", ()->
			scenarioReportManager.getSimulationTrainReport(
					scenarioId,
					 distr,
					 trainType,
					 lineSegment,
					 trainId,
					 fromOs,
					 delayType )
		);
	}

	@Test
	public void test_getSimulationTrainReport_recrewDelay() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		final String lineSegment = null;
		final String trainId = null;
		final String distr = null;
		final String delayType = Constants.RC_DELAY;
		final String fromOs = null;
		runTestAndAssert("test_getSimulationTrainReport_recrewDelay", ()->
				scenarioReportManager.getSimulationTrainReport(
						scenarioId,
						distr,
						trainType,
						lineSegment,
						trainId,
						fromOs,
						delayType )
		);
	}

	@Test
	public void test_getSimulationTrainReport_crewDelay() {
		val scenarioId = SCENARIO_ID_LONG;
		final String trainType = null;
		final String lineSegment = null;
		final String trainId = null;
		final String distr = null;
		final String delayType = Constants.CR_DELAY;
		final String fromOs = null;
		runTestAndAssert("test_getSimulationTrainReport_crewDelay", ()->
				scenarioReportManager.getSimulationTrainReport(
						scenarioId,
						distr,
						trainType,
						lineSegment,
						trainId,
						fromOs,
						delayType )
		);
	}

	@Test
	public void test_getSimulationTrainReport_specific() {
		val scenarioId = SCENARIO_ID_LONG;

		final String trainType = "CE";
		final String lineSegment = "L8";
		final String trainId = "423";
		final String distr = "PO";
		final String delayType =Constants.RC_DELAY;
		final String fromOs = "00248";

		runTestAndAssert("test_getSimulationTrainReport_specific", ()->
				scenarioReportManager.getSimulationTrainReport(
						scenarioId,
						distr,
						trainType,
						lineSegment,
						trainId,
						fromOs,
						delayType )
		);
	}


	@Test
	public void test_getSimulationTrainReport_noDelay() {
		val scenarioId = SCENARIO_ID_LONG;

		final String trainType = null;
		final String lineSegment = null;
		final String trainId = null;
		final String distr = null;
		final String delayType = Constants.NO_DELAY;
		final String fromOs = null;

		runTestAndAssert("test_getSimulationTrainReport_noDelay", ()->
				scenarioReportManager.getSimulationTrainReport(
						scenarioId,
						distr,
						trainType,
						lineSegment,
						trainId,
						fromOs,
						delayType )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_system() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_SYSTEM;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_system", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_system_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String summaryLevel = Constants.DB_TSSBP_SYSTEM;
		final String craft = "CO";
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_system_craft", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_distr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_distr", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_distr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_distr_craft", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "ALL";
		final String summaryLevel = Constants.DB_TSSBP_SUB_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_subDistr", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_subDistr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_SUB_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_subDistr_craft", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_pool() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_POOL;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_pool", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_totalStarts_pool_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_POOL;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_totalStarts_pool_craft", ()->
				scenarioReportManager.getTSSBPTotalsByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_system() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_SYSTEM;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_system", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_system_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_SYSTEM;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_system_craft", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_distr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_distr", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_distr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_distr_craft", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_SUB_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_subDistr", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_subDistr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_SUB_DISTR;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_subDistr_craft", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_pool() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = null;
		final String summaryLevel = Constants.DB_TSSBP_POOL;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_pool", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTrnStatSumByPoolRpt_avgStartsPerWeek_pool_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String craft = "CO";
		final String summaryLevel = Constants.DB_TSSBP_POOL;
		runTestAndAssert("test_getTrnStatSumByPoolRpt_avgStartsPerWeek_pool_craft", ()->
				scenarioReportManager.getTSSBPAvgByScenarioIdAndSummaryLevelAndCraft(
						scenarioId,
						summaryLevel,
						craft )
		);
	}

	@Test
	public void test_getTurnUtilizationTotals_all() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = null;
		String boardName = null;
		String distr = null;
		String subDistr = null;
		String craft = null;
		runTestAndAssert("test_getTurnUtilizationTotals_all", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						 boardType,
						 distr,
						 subDistr,
						 boardName,
						 craft
				)
		);
	}

	@Test
	public void test_getTurnUtilizationTotals_boardType() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = Constants.EXTRA;
		String boardName = null;
		String distr = null;
		String subDistr = null;
		String craft = null;
		runTestAndAssert("test_getTurnUtilizationTotals_boardType", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft
				)
		);
	}


	@Test
	public void test_getTurnUtilizationTotals_boardType_boardName() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = Constants.EXTRA;
		String boardName = "CO";
		String distr = null;
		String subDistr = null;
		String craft = null;
		runTestAndAssert("test_getTurnUtilizationTotals_boardType_boardName", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft
				)
		);
	}

	@Test
	public void test_getTurnUtilizationTotals_boardType_boardName_distr() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = Constants.EXTRA;
		String boardName = "CO";
		String distr = "HB";
		String subDistr = null;
		String craft = null;
		runTestAndAssert("test_getTurnUtilizationTotals_boardType_boardName_distr", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft
				)
		);
	}

	@Test
	public void test_getTurnUtilizationTotals_boardType_boardName_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = Constants.EXTRA;
		String boardName = "CO";
		String distr = "HB";
		String subDistr = "CR";
		String craft = null;
		runTestAndAssert("test_getTurnUtilizationTotals_boardType_boardName_distr_subDistr", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft
				)
		);
	}

	@Test
	public void test_getTurnUtilizationTotals_boardType_boardName_distr_subDistr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		String boardType = Constants.EXTRA;
		String boardName = "CO";
		String distr = "HB";
		String subDistr = "CR";
		String craft = "CO";
		runTestAndAssert("test_getTurnUtilizationTotals_boardType_boardName_distr_subDistr_craft", ()->
				scenarioManager.getTurnUtilizationByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft
				)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_all() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = null;
		final String boardName = null;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		runTestAndAssert("test_getTurnUtilizationAvg_all", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						 boardType,
						 distr,
						 subDistr,
						 boardName,
						 craft)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_boardType() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = Constants.EXTRA;
		final String boardName = null;
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		runTestAndAssert("test_getTurnUtilizationAvg_boardType", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_boardType_boardName() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = Constants.EXTRA;
		final String boardName = "CO";
		final String distr = null;
		final String subDistr = null;
		final String craft = null;
		runTestAndAssert("test_getTurnUtilizationAvg_boardType_boardName", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_boardType_boardName_distr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = Constants.EXTRA;
		final String boardName = "CO";
		final String distr = "HB";
		final String subDistr = null;
		final String craft = null;
		runTestAndAssert("test_getTurnUtilizationAvg_boardType_boardName_distr", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_boardType_boardName_distr_subDistr() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = Constants.EXTRA;
		final String boardName = "CO";
		final String distr = "HB";
		final String subDistr = "CR";
		final String craft = null;
		runTestAndAssert("test_getTurnUtilizationAvg_boardType_boardName_distr_subDistr", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft)
		);
	}

	@Test
	public void test_getTurnUtilizationAvg_boardType_boardName_distr_subDistr_craft() {
		val scenarioId = SCENARIO_ID_LONG;
		final String boardType = Constants.EXTRA;
		final String boardName = "CO";
		final String distr = "HB";
		final String subDistr = "CR";
		final String craft = "CO";
		runTestAndAssert("test_getTurnUtilizationAvg_boardType_boardName_distr_subDistr_craft", ()->
				scenarioManager.getTurnUtilizationAvgByScenarioIdBoardTypeDistrSubDistrBoardNameCraft(
						scenarioId,
						boardType,
						distr,
						subDistr,
						boardName,
						craft)
		);
	}

	@Test
	public void test_getScenarioCfg() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getScenarioCfg", ()->
			scenarioManager.getScenarioById(scenarioId).getScenarioCfg()
		);
	}

	@Test
	public void test_getScenarioSelectors() {
		runTestAndAssert("test_getScenarioSelectors", ()->
				scenarioReportManager.getScenarioSelectorsByStatus(ScenarioStatus.COMPLETE)
		);
	}

	@Test
	public void test_getBoardSummaryFilters() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getBoardSummaryFilters", ()->
			scenarioReportManager.getBoardSummaryFiltersByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getDeadheadFilters() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getDeadheadFilters", ()->
			scenarioReportManager.getDeadheadFiltersByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getTrainDelayFilters() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getTrainDelayFilters", ()->
			scenarioReportManager.getSimulationTrainTrnTypesByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getSimulationTrainFilters() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getSimulationTrainFilters", ()->
			scenarioReportManager.getSimulationTrainFiltersByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_getTurnUtilizationFilters() {
		val scenarioId = SCENARIO_ID_LONG;
		runTestAndAssert("test_getTurnUtilizationFilters", ()->
			scenarioReportManager.getTurnUtilizationFiltersByScenarioId(scenarioId)
		);
	}

	@Test
	public void test_post_updateScenario() {
		val scenarioId = SCENARIO_ID_LONG;
		val scenario = scenarioManager.getScenarioById(scenarioId);
		val updated = scenario.toBuilder()
				.scenName("updatedScenario")
				.description("updatedDesc")
				.updateTs(dateTimeProvider.now())
				.updateUserId("user2").
				build();
		scenarioManager.save(updated);
		val updatedScenario = scenarioManager.getScenarioById(scenarioId);
		assertThat(updatedScenario.getScenName()).isEqualTo("updatedScenario");
		assertThat(updatedScenario.getDescription()).isEqualTo("updatedDesc");
	}

	@Autowired
	private StudyReportManager studyReportManager;

	@Autowired
	private ScenarioCanceller scenarioCanceller;

	@Test
	public void test_deleteScenario() {
		val scenarioId = SCENARIO_ID_LONG;

		// Delete the scenario.
		scenarioManager.markScenarioDeleted(scenarioId);

		// The scenario should still exist but should have DELETED status.
		val scenario = scenarioManager.getScenarioById(scenarioId);
		assertThat(scenario).isNotNull();
		assertThat(scenario.getStatus()).isEqualTo(ScenarioStatus.DELETED);

		// The scenario must not appear in the study tree.
		val studiesTree = studyReportManager.getStudiesTree();
		assertThat(Iterables.filter(studiesTree, st -> st.getScenarioId() != null && st.getScenarioId().equals(scenarioId))).isEmpty();
	}

	@Test
	public void test_cancelScenario() {
		val scenarioId = SCENARIO_ID_LONG;
		scenarioCanceller.cancelScenario(scenarioId);
		val scenario = scenarioManager.getScenarioById(scenarioId);
		assertThat(scenario).isNotNull();
		assertThat(scenario.getStatus()).isEqualTo(ScenarioStatus.CANCELLED);
	}
}
