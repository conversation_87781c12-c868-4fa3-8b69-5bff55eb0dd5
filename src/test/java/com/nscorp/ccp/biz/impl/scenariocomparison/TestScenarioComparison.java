package com.nscorp.ccp.biz.impl.scenariocomparison;
import lombok.val;

import com.nscorp.ccp.biz.scenariocomparison.ScenarioComparisonManager;
import com.nscorp.ccp.testing.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

import static com.nscorp.ccp.testing.util.SeedDataUtils.PLAN_ID_LONG;
import static com.nscorp.ccp.testing.util.SeedDataUtils.SCENARIO_ID_LONG;
import static com.nscorp.ccp.testing.util.TestUtils.*;

@Slf4j
@Tag("slow")
@Transactional
public class TestScenarioComparison extends SpringBasedTest {

	@Autowired
	private TestInitializer testInitializer;

	@PostConstruct
	public void init() {
		testInitializer.initTests();
	}

	@Autowired
	private ScenarioComparisonManager manager;

	@Test
	public void test_scenarioCompBoardSummaryPerPool_totalStarts() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		val basePlan = PLAN_ID_LONG;
		val testPlan = 1L;
		runTestAndAssert("test_ScenarioCompBoardSummaryPerPool_totalStarts",
				()->manager.getBoardSummaryCompTotalsByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompDeadheadPerPool_totalStarts() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompDeadheadPerPool_totalStarts", ()->
			manager.getDeadheadCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTrainDelay_totalStarts() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTrainDelay_totalStarts", ()-> manager.getTrainDelayCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTrainStatsPerPool_totalStarts() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTrainStatsPerPool_totalStarts", ()-> manager.getTrainStatsCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTurnUtilPerPoolPerStatus_totalStarts() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTurnUtilPerPoolPerStatus_totalStarts", ()->manager.getTurnUtilizationCompTotalsByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompBoardSummaryPerPool_avgStartsPerWeek() {
		val basePlan = PLAN_ID_LONG;
		val testPlan = 1L;
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompBoardSummaryPerPool_avgStartsPerWeek", ()->manager.getBoardSummaryCompAvgByBaseScenarioIdTestScenarioId(basePlan, testPlan, baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompDeadheadPerPool_avgStartsPerWeek() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompDeadheadPerPool_avgStartsPerWeek", ()-> manager.getDeadheadCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTrainDelay_avgStartsPerWeek() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTrainDelay_avgStartsPerWeek", ()-> manager.getTrainDelayCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTrainStatsPerPool_avgStartsPerWeek() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTrainStatsPerPool_avgStartsPerWeek", ()-> manager.getTrainStatsCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}

	@Test
	public void test_ScenarioCompTurnUtilPerPoolPerStatus_avgStartsPerWeek() {
		val baseScenario = SCENARIO_ID_LONG;
		val testScenario = 1L;
		runTestAndAssert("test_ScenarioCompTurnUtilPerPoolPerStatus_avgStartsPerWeek", ()->manager.getTurnUtilizationCompAvgByBaseScenarioIdTestScenarioId(baseScenario, testScenario));
	}
}
