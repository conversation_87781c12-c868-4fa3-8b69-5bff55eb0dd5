package com.nscorp.ccp.biz.impl.plan;
import lombok.val;

import static com.nscorp.ccp.testing.util.SeedDataUtils.PLAN_ID_LONG;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.collect.ImmutableSet;
import com.nscorp.ccp.biz.plan.*;
import com.nscorp.ccp.common.plan.*;
import com.nscorp.ccp.testing.util.SpringBasedTest;
import com.nscorp.ccp.utils.commonTypes.Craft;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static com.google.common.collect.Iterables.find;
import static com.google.common.collect.Iterables.getFirst;
import static com.nscorp.ccp.biz.impl.plan.PlanTestUtils.createHubIfNonexistent;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import org.junit.jupiter.api.*;
import com.nscorp.ccp.testing.util.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

@Slf4j
@Tag("slow")
@Transactional
public class TestCreateUpdatePool extends SpringBasedTest {
	@Autowired
	private PlanManager planManager;

	@Autowired
	private PoolUpdater poolUpdater;

	@Autowired
	private PoolCreator poolCreator;

    @Autowired
    private TestInitializer testInitializer;

	@PostConstruct
	public void init() {
        testInitializer.initTests();
	}


	private Pool createInitialPool(final long planId) {

		// Turn CC01
		val cc01 = Turn.builder().
				turnId("CC01").
				craft(Craft.CO).
				build();

		// Rotation 1
		val rot1 = PoolRotationH.builder().
				craft(Craft.CO).
				tieupShort(1).
				tieupThru(1).
				build();

		// Rotation 2
		val rot2 = PoolRotationH.builder().
				craft(Craft.CO).
				tieupShort(1).
				tieupThru(1).
				build();

		// PSU 148H
		val psu_148h = PoolSetUp.builder().
				os("148H").
				leadTime(20).
				poolHomeAway(0).
				regularRestTime(10.0).
				undisturbedRest("Y").
				poolRotationHistories(ImmutableSet.of(rot1)).
				build();

		// PSU 251W
		val psu_251w = PoolSetUp.builder().
				os("251W").
				leadTime(20).
				poolHomeAway(1).
				regularRestTime(10.0).
				undisturbedRest("N").
				poolRotationHistories(ImmutableSet.of(rot2)).
				build();

		// Pool XX
		val initialPool = Pool.builder().
				hubId(createHubIfNonexistent(planManager, planId, "myhub")).
				planId(planId).
				distr("AL").
				subDistr("BI").
				poolName("XX").
				thMarkOffRate(0.25).
				poolSetUps(ImmutableSet.of(psu_148h, psu_251w)).
				turns(ImmutableSet.of(cc01)).
				build();
		return initialPool;
	}

	private PoolSetUp findPsuByOs(final Pool pool, final String os) {
		return find(pool.getPoolSetUps(), e -> e.getOs().equals(os), null);
	}

	private void checkInitialPool(long planIdLong, long poolId) {
		// Now read the pool from the DB and make sure it is correct.
		val completePlan = planManager.getCompletePlanByPlanId(planIdLong, PlanFetchOptions.builder().includePools(true).build());
		val initialPool = find(completePlan.getPools(), e -> e.getPoolId().equals(poolId), null);
		if ( log.isWarnEnabled() ) {
			warn(log, String.format("test_createPlan() created a record and got back this pool: %s", initialPool));
		}
		assertThat(initialPool).isNotNull();
		assertThat(initialPool.getThMarkOffRate()).isNotNull();
		assertThat(initialPool.getThMarkOffRate().doubleValue()).isEqualTo(0.25);
		assertThat(initialPool.getPoolSetUps()).hasSize(2);

		// Check PSU 1
		val psu_148h = find(initialPool.getPoolSetUps(), e->e.getOs().equals("148H"), null);
		assertThat(psu_148h).isNotNull();
		assertThat(psu_148h.getOs()).isEqualTo("148H");
		assertThat(psu_148h.getLeadTime()).isEqualTo(20);
		assertThat(psu_148h.getPoolHomeAway()).isEqualTo(0);
		assertThat(psu_148h.getPoolRotationHistories()).hasSize(1);

		// Check rotation #1
		val rot1 = getFirst(psu_148h.getPoolRotationHistories(), null);
		assertThat(rot1.getCraft()).isEqualTo(Craft.CO);
		assertThat(rot1.getTieupThru()).isEqualTo(1);
		assertThat(rot1.getTieupShort()).isEqualTo(1);

		// Check PSU 2
		val psu_251w = find(initialPool.getPoolSetUps(), e->e.getOs().equals("251W"), null);
		assertThat(psu_251w.getOs()).isEqualTo("251W");
		assertThat(psu_251w.getLeadTime()).isEqualTo(20);
		assertThat(psu_251w.getPoolHomeAway()).isEqualTo(1);
		assertThat(psu_251w.getPoolRotationHistories()).hasSize(1);
	}

	@SneakyThrows private Pool editPool(final Pool initialPool, final PoolSetUp psu_251w) {
		// Now perform the following edits:
		//      1) Add turn 2 ("CC02")
		//      2) Delete turn 1 ("CC01")
		//      3) Add PSU 3
		//      4) Delete PSU 1
		//      5) Change the leadTime field of PSU 2
		//      6) Change the pool's hub from myhub to myhub2.

		// New turn #2
		val cc02 = Turn.builder().
				turnId("CC02").
				craft(Craft.CO).
				build();

		// New rotation #3
		val testRot3 = PoolRotationH.builder().
				craft(Craft.CO).
				tieupShort(1).
				tieupThru(1).
				build();

		// New PSU #3
		val psu_547a = PoolSetUp.builder().
				os("547A").
				leadTime(15).
				poolHomeAway(2).
				regularRestTime(11.0).
				undisturbedRest("Y").
				poolRotationHistories(ImmutableSet.of(testRot3)).
				build();

		// Update the pool.  Note: Only PSU's 2 and 3 are retained.  PSU #1 is being dropped.
		val testPoolV2 = initialPool.toBuilder().
				hubId(createHubIfNonexistent(planManager, initialPool.getPlanId(), "myhub2")).
				turns(ImmutableSet.of(cc02)).
				poolSetUps(ImmutableSet.of(psu_251w.toBuilder().leadTime(99).build(), psu_547a)).
				build();

		return poolUpdater.update(testPoolV2, initialPool.getPoolId());
	}

	@Test
	@Transactional
	public void test_createPool_success() {
		val planId = PLAN_ID_LONG;
		val pool = poolCreator.create(createInitialPool(planId));
		assertThat(planManager.getPoolById(pool.getPoolId())).isNotNull();
	}

	@Test
	@Transactional
	public void test_createPool_locked() {
		val planId = PLAN_ID_LONG;
		planManager.changeLockStatus(planId, true);
		assertThatExceptionOfType(PlanLockedException.class).isThrownBy(()->poolCreator.create(createInitialPool(planId)));
	}

	@Test
	@Transactional
	public void test_updatePool() {
		val planId = PLAN_ID_LONG;
		planManager.changeLockStatus(planId, false);

		// Issue a create request.
		val initialPool = planManager.save(createInitialPool(planId));
		val psu_251w = findPsuByOs(initialPool, "251W");
		val newlyCreatedPoolId = initialPool.getPoolId();

		// Check the pool.
		checkInitialPool(planId, newlyCreatedPoolId);

		// Edit the pool.
		val editedPool = editPool(initialPool, psu_251w);

		// Expecting 1 pool.
		val updatedCompletePlan = planManager.getCompletePlanByPlanId(planId, PlanFetchOptions.builder().includePools(true).build());
		val updatedPool = find(updatedCompletePlan.getPools(), e -> e.getPoolName().equals("XX"), null);
		assertThat(updatedPool).isNotNull();
		assertThat(updatedPool.getHubId()).isEqualTo(createHubIfNonexistent(planManager, planId, "myhub2"));

		// Expecting 1 turn, which is CC02 (CC01 was deleted).
		val updatedTurns = updatedPool.getTurns();
		assertThat(updatedTurns).hasSize(1);
		val updatedTurnCC02 = getFirst(updatedTurns, null);
		assertThat(updatedTurnCC02.getTurnId()).isEqualTo("CC02");

		val updatedPsus = updatedPool.getPoolSetUps();
		assertThat(updatedPsus).hasSize(2);

		// Check PSU 547A
		val updatedPsu547A = find(updatedPsus, e -> e.getOs().equals("547A"));
		assertThat(updatedPsu547A.getOs()).isEqualTo("547A");
		assertThat(updatedPsu547A.getLeadTime()).isEqualTo(15);
		assertThat(updatedPsu547A.getPoolHomeAway()).isEqualTo(2);
		assertThat(updatedPsu547A.getRegularRestTime()).isEqualTo(11.0);

		// Check PSU 251W
		val updatedPsu251W = find(updatedPsus, e -> e.getOs().equals("251W"));
		assertThat(updatedPsu251W.getOs()).isEqualTo("251W");
		assertThat(updatedPsu251W.getLeadTime()).isEqualTo(99);

		// Delete the pool.
		planManager.deletePoolById(updatedPool.getPoolId());

		// Assert that the pool doesn't exist.
		val completePlanAfterDelete = planManager.getCompletePlanByPlanId(planId, PlanFetchOptions.builder().includePools(true).build());
		val nonexistentPool = find(completePlanAfterDelete.getPools(), e -> e.getPoolName().equals("XX"), null);
		assertThat(nonexistentPool).isNull();
	}
}
