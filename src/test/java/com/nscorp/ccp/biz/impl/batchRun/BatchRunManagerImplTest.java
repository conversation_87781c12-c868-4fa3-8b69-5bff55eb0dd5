package com.nscorp.ccp.biz.impl.batchRun;

import com.nscorp.ccp.common.batchRun.BatchRunType;
import com.nscorp.ccp.testing.util.SampleDataManager;
import lombok.val;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class BatchRunManagerImplTest {

	@Test
	void runBatchJob() {
		val result1 = BatchRunManagerImpl.runBatchJob(BatchRunType.TEST_JOB, ()->{}, SampleDataManager.getSampleInstant(), SampleDataManager::getSampleInstant);
		Assertions.assertThat(result1.isSuccessful()).isTrue();
		Assertions.assertThat(result1.getBatchRunType()).isEqualTo(BatchRunType.TEST_JOB);

		val result2 = BatchRunManagerImpl.runBatchJob(BatchRunType.TEST_JOB, ()->{throw new RuntimeException(); }, SampleDataManager.getSampleInstant(), SampleDataManager::getSampleInstant);
		Assertions.assertThat(result2.isSuccessful()).isFalse();
		Assertions.assertThat(result2.getBatchRunType()).isEqualTo(BatchRunType.TEST_JOB);
	}
}
