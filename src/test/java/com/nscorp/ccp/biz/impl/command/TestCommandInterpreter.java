package com.nscorp.ccp.biz.impl.command;
import lombok.val;

import com.nscorp.ccp.common.controlParam.ControlParams;
import com.nscorp.ccp.biz.command.CcpCommandInterpreter;
import com.nscorp.ccp.biz.controlParam.ControlParamManager;
import com.nscorp.ccp.testing.util.SpringBasedTest;
import com.nscorp.ccp.testing.util.TestInitializer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@Tag("slow")
@Transactional
public class TestCommandInterpreter extends SpringBasedTest {
	@Autowired
	private TestInitializer testInitializer;

	@Autowired
	private CcpCommandInterpreter commandInterpreter;

	@PostConstruct
	public void init() {
		testInitializer.initTests();
	}

	@Autowired
	private ControlParamManager controlParamManager;

	@Test
	public void test_setParam() {
		commandInterpreter.runCommand("set_control_param my_param my_value");
		assertThat(controlParamManager.getControlParamValueByKey("my_param")).isEqualTo("my_value");
		assertThat(commandInterpreter.runCommand("get_control_param my_param")).contains("my_value");
		assertThat(commandInterpreter.runCommand("list_control_params")).contains("my_param");
		commandInterpreter.runCommand("delete_control_param my_param");
		assertThat(controlParamManager.getControlParamValueByKey("my_param")).isNull();
		commandInterpreter.runCommand("scenario_execution_off");
		assertThat(controlParamManager.isTrue(ControlParams.SCENARIO_EXECUTION_BATCH_JOB_DISABLED)).isTrue();
		commandInterpreter.runCommand("scenario_execution_on");
		assertThat(controlParamManager.isTrue(ControlParams.SCENARIO_EXECUTION_BATCH_JOB_DISABLED)).isFalse();

		commandInterpreter.runCommand("job_importer_off");
		assertThat(controlParamManager.isTrue(ControlParams.IMPORT_CREWPRO_JOBS_DISABLED)).isTrue();
		commandInterpreter.runCommand("job_importer_on");
		assertThat(controlParamManager.isTrue(ControlParams.IMPORT_CREWPRO_JOBS_DISABLED)).isFalse();

		commandInterpreter.runCommand("run_sim_off");
		assertThat(controlParamManager.isTrue(ControlParams.RUN_SIMULATION_DISABLED)).isTrue();
		commandInterpreter.runCommand("run_sim_on");
		assertThat(controlParamManager.isTrue(ControlParams.RUN_SIMULATION_DISABLED)).isFalse();

		commandInterpreter.runCommand("study_cleaner_off");
		assertThat(controlParamManager.isTrue(ControlParams.STUDY_CLEANER_BATCH_JOB_DISABLED)).isTrue();
		commandInterpreter.runCommand("study_cleaner_on");
		assertThat(controlParamManager.isTrue(ControlParams.STUDY_CLEANER_BATCH_JOB_DISABLED)).isFalse();

		commandInterpreter.runCommand("study_creator_off");
		assertThat(controlParamManager.isTrue(ControlParams.STUDY_CREATOR_BATCH_JOB_DISABLED)).isTrue();
		commandInterpreter.runCommand("study_creator_on");
		assertThat(controlParamManager.isTrue(ControlParams.STUDY_CREATOR_BATCH_JOB_DISABLED)).isFalse();

		commandInterpreter.runCommand("scenario_cleaner_off");
		assertThat(controlParamManager.isTrue(ControlParams.SCENARIO_CLEANER_BATCH_JOB_DISABLED)).isTrue();
		commandInterpreter.runCommand("scenario_cleaner_on");
		assertThat(controlParamManager.isTrue(ControlParams.SCENARIO_CLEANER_BATCH_JOB_DISABLED)).isFalse();

		commandInterpreter.runCommand("plan_cleaner_off");
		assertThat(controlParamManager.isTrue(ControlParams.PLAN_CLEANER_BATCH_JOB_DISABLED)).isTrue();
		commandInterpreter.runCommand("plan_cleaner_on");
		assertThat(controlParamManager.isTrue(ControlParams.PLAN_CLEANER_BATCH_JOB_DISABLED)).isFalse();

		commandInterpreter.runCommand("restart_svc");
		assertThat(controlParamManager.isTrue(ControlParams.SVC_EXIT_REQUESTED)).isTrue();

		commandInterpreter.runCommand("restart_cron");
		assertThat(controlParamManager.isTrue(ControlParams.CRON_EXIT_REQUESTED)).isTrue();
	}

	@Test
	public void test_logLevel() {
		val systemCommands = "log_level svc WARN";
		commandInterpreter.runCommand(systemCommands);
	}
}
