package com.nscorp.ccp.biz.impl.crewpro;
import lombok.val;

import com.nscorp.ccp.biz.crewpro.CrewProGroupManager;
import com.nscorp.ccp.testing.util.*;
import com.nscorp.ccp.biz.scenario.ScenarioManager;
import com.nscorp.ccp.biz.study.StudyManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

@Slf4j
@Tag("slow")
@Transactional
public class TestCrewProJobImport extends SpringBasedTest {
	//@Autowired
	//private SeedDataLoader seedDataLoader;

    @Autowired
    private TestInitializer testInitializer;

	@PostConstruct
	public void init() {
        testInitializer.initTests();
	}

	@Autowired
	private CrewProGroupManager crewProGroupManager;

	@Autowired
	private StudyManager studyManager;

	@Autowired
	private ScenarioManager scenarioManager;

	@Test
	public void test_CrewProGroupManager_getAllGroups() {
		TestUtils.runTestAndWriteOutput("CrewProGroupManager.getAllGroups", () -> crewProGroupManager.getAllGroups());
	}

	@Test
	public void test_CrewPro_import() {
		// Assert that the number of ccp.trains rows is the same as the number of ccp.scen_trains rows for the
		//      imported CrewPro study and scenario.
		val scenTrains = scenarioManager.getScenTrainsByScenarioId(0L);
		val trains = studyManager.getTrainsByStudyId(0L);
		assertThat(scenTrains).hasSameSizeAs(trains);
	}
}
