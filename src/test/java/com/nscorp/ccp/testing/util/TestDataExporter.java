package com.nscorp.ccp.testing.util;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.nscorp.ccp.utils.db.DataExporter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Commit;

import java.io.File;
import java.io.StringWriter;
import org.junit.jupiter.api.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

@Slf4j
@Tag("slow")
@Transactional
public class TestDataExporter extends SpringBasedTest {

    @Autowired
    private TestInitializer testInitializer;

	@PostConstruct
	public void init() {
        testInitializer.initTests();
	}

	@Autowired
	private DataExporter dataExporter;

	@Test
	public void test_DataExporter() {
		StringWriter stringWriter = new StringWriter();
		String query = "select * from ccp.study";
		dataExporter.exportData("scpmDataSource", query, stringWriter, new File("src/test/resources/blobs"), "blobs", null);
	}
}
