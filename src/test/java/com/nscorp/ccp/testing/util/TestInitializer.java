package com.nscorp.ccp.testing.util;

import com.nscorp.ccp.biz.seedData.SeedDataLoader;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
@RequiredArgsConstructor
@Slf4j
public class TestInitializer {
    private final SeedDataLoader loader;
    private final ConfigurableEnvironment environment;

     private boolean initialized = false;

    @SneakyThrows @Synchronized public void initTests() {
        if ( ! initialized ) {
            LogOutputInitializer.init();

            val cutoffTs = Instant.parse("2021-01-01T00:00:00.00Z");
            loader.load(cutoffTs);

            initialized = true;
        }
    }
}

