{"responseInfo": {"warnings": [{"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp HB/CR/XF/3 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=EN\n  crewDestinOs=PC175\n  crewOrgnOs=00248\n  distr=HB\n  miles=<null>\n  poolHomeAway=3\n  poolName=XF\n  poolProfileDesc=<null>\n  profileId=HBCR03DH\n  rin=<null>\n  subDistr=CR\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp HB/CR/XF/3 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=CO\n  crewDestinOs=PC175\n  crewOrgnOs=00248\n  distr=HB\n  miles=<null>\n  poolHomeAway=3\n  poolName=XF\n  poolProfileDesc=<null>\n  profileId=HBCR03DH\n  rin=<null>\n  subDistr=CR\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=12.55\n  deptShape=1.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Fri Jun 11 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Fri Jun 11 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.26\n  transitShiftConstant=3.9\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C610\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-10\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Triangle\n  deptLambda1=<null>\n  deptLower=0.22\n  deptMean1=<null>\n  deptMedium=1.75\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=0.26\n  deptUpper=37.62\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Thu Jun 10 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Thu Jun 10 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.07\n  transitShiftConstant=2.5\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C609\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-09\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.34\n  deptScale=<null>\n  deptShape=3.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Tue Jun 08 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Tue Jun 08 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.3\n  transitShiftConstant=2.16\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C607\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-07\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC23\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=1.62\n  deptShape=0.53\n  deptShiftConstant=0.68\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=PC23\n  fromTs=Mon Jun 07 17:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=525\n  toLocSeqNr=3\n  toOs=PC175\n  toTs=Tue Jun 08 01:45:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.01\n  transitShiftConstant=2.13\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=171C307\n  trnCrewDistr=C3\n  trnOrgnDt=2021-06-07\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.48\n  deptScale=<null>\n  deptShape=5.9\n  deptShiftConstant=4.05\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Sun Jun 06 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Sun Jun 06 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.8\n  transitShiftConstant=2.05\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C605\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-05\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=12.55\n  deptShape=1.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Fri Jun 04 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Fri Jun 04 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.26\n  transitShiftConstant=3.9\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C603\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-03\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Triangle\n  deptLambda1=<null>\n  deptLower=0.22\n  deptMean1=<null>\n  deptMedium=1.75\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=0.26\n  deptUpper=37.62\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Thu Jun 03 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Thu Jun 03 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.07\n  transitShiftConstant=2.5\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C602\n  trnCrewDistr=C6\n  trnOrgnDt=2021-06-02\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.34\n  deptScale=<null>\n  deptShape=3.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Tue Jun 01 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Tue Jun 01 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.3\n  transitShiftConstant=2.16\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C631\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-31\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC23\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=1.62\n  deptShape=0.53\n  deptShiftConstant=0.68\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=PC23\n  fromTs=Mon May 31 17:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=525\n  toLocSeqNr=3\n  toOs=PC175\n  toTs=Tue Jun 01 01:45:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.01\n  transitShiftConstant=2.13\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=171C331\n  trnCrewDistr=C3\n  trnOrgnDt=2021-05-31\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.48\n  deptScale=<null>\n  deptShape=5.9\n  deptShiftConstant=4.05\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Sun May 30 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Sun May 30 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.8\n  transitShiftConstant=2.05\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C629\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-29\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=12.55\n  deptShape=1.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Fri May 28 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Fri May 28 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.26\n  transitShiftConstant=3.9\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C627\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-27\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Triangle\n  deptLambda1=<null>\n  deptLower=0.22\n  deptMean1=<null>\n  deptMedium=1.75\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=0.26\n  deptUpper=37.62\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Thu May 27 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Thu May 27 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.07\n  transitShiftConstant=2.5\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C626\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-26\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.34\n  deptScale=<null>\n  deptShape=3.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Tue May 25 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Tue May 25 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.3\n  transitShiftConstant=2.16\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C624\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-24\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC23\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=1.62\n  deptShape=0.53\n  deptShiftConstant=0.68\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=PC23\n  fromTs=Mon May 24 17:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=525\n  toLocSeqNr=3\n  toOs=PC175\n  toTs=Tue May 25 01:45:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.01\n  transitShiftConstant=2.13\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=171C324\n  trnCrewDistr=C3\n  trnOrgnDt=2021-05-24\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.48\n  deptScale=<null>\n  deptShape=5.9\n  deptShiftConstant=4.05\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Sun May 23 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Sun May 23 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.8\n  transitShiftConstant=2.05\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C622\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-22\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=12.55\n  deptShape=1.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Fri May 21 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Fri May 21 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.26\n  transitShiftConstant=3.9\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C620\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-20\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Triangle\n  deptLambda1=<null>\n  deptLower=0.22\n  deptMean1=<null>\n  deptMedium=1.75\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=0.26\n  deptUpper=37.62\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Thu May 20 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Thu May 20 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=2.07\n  transitShiftConstant=2.5\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C619\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-19\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.34\n  deptScale=<null>\n  deptShape=3.45\n  deptShiftConstant=0.0\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Tue May 18 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Tue May 18 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.3\n  transitShiftConstant=2.16\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C617\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-17\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC23\n  departureDistribution=Weibull\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=1.62\n  deptShape=0.53\n  deptShiftConstant=0.68\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=PC23\n  fromTs=Mon May 17 17:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=525\n  toLocSeqNr=3\n  toOs=PC175\n  toTs=Tue May 18 01:45:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.01\n  transitShiftConstant=2.13\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=171C317\n  trnCrewDistr=C3\n  trnOrgnDt=2021-05-17\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}, {"code": "ILLEGAL_TRAIN_DISTRIBUTION", "warningMessage": "This studyTrnInput uses transit distribution WEIBULL but is missing one of the required fields shape, scale.  The studyTrnInput record will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=PC175\n  departureDistribution=Gamma\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=0.48\n  deptScale=<null>\n  deptShape=5.9\n  deptShiftConstant=4.05\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=13\n  fromOs=PC175\n  fromTs=Sun May 16 14:05:00 EDT 2021\n  initTerminalTime=90\n  profileId=HBCR05\n  segTransitTime=380\n  toLocSeqNr=15\n  toOs=PC23\n  toTs=Sun May 16 20:25:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=1.8\n  transitShiftConstant=2.05\n  transitTimeDistribution=Weibull\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=170C615\n  trnCrewDistr=C6\n  trnOrgnDt=2021-05-15\n  trnSection=0\n  trnType=MER\n]", "type": "VALIDATION"}], "requestTs": "2021-05-23T18:12:56+0000", "requestId": "hub_7326_1.00_2021-05-29_2021-06-04", "errorMessage": null, "simulationEndTs": "2021-05-23T18:45:13+0000", "servantId": -1, "simulationStartTs": "2021-05-23T18:42:44+0000", "status": "SUCCESS"}, "boardSummaries": [{"nbrOfStarts": 3, "boardType": "Extra", "boardSize": 2, "subDistr": "CR", "craft": "CO", "distr": "HB", "boardName": "CO"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "CR", "craft": "EN", "distr": "HB", "boardName": "CO"}, {"nbrOfStarts": 11, "boardType": "Pool", "boardSize": 3, "subDistr": "CR", "craft": "CO", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 12, "boardType": "Pool", "boardSize": 3, "subDistr": "CR", "craft": "EN", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "CR", "craft": "CO", "distr": "HB", "boardName": "EN"}, {"nbrOfStarts": 3, "boardType": "Extra", "boardSize": 2, "subDistr": "CR", "craft": "EN", "distr": "HB", "boardName": "EN"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "CR", "craft": "CO", "distr": "HB", "boardName": "XE"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "CR", "craft": "EN", "distr": "HB", "boardName": "XE"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "CR", "craft": "CO", "distr": "HB", "boardName": "XF"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "CR", "craft": "EN", "distr": "HB", "boardName": "XF"}], "boardTrainStarts": [{"nbrOfStarts": 1, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "startType": "DEADHEAD", "distr": "HB", "boardName": "CO"}, {"nbrOfStarts": 2, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "startType": "NORMAL", "distr": "HB", "boardName": "CO"}, {"nbrOfStarts": 3, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "startType": "DEADHEAD", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 8, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "startType": "NORMAL", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 3, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "startType": "DEADHEAD", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 9, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "startType": "NORMAL", "distr": "HB", "boardName": "CR"}, {"nbrOfStarts": 1, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "startType": "DEADHEAD", "distr": "HB", "boardName": "EN"}, {"nbrOfStarts": 2, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "startType": "NORMAL", "distr": "HB", "boardName": "EN"}], "simulationTrainOutputs": [{"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-02T16:44:00+0000", "fromTs": "2021-06-02T18:14:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 591.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-02T16:44:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.81, "crewProfileId": "HBCR05", "toTs": "2021-06-03T00:35:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C601", "trnOrgnDt": "2021-06-01", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-05T16:47:00+0000", "fromTs": "2021-06-05T18:17:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 594.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-05T16:47:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 590.91, "crewProfileId": "HBCR05", "toTs": "2021-06-06T00:41:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C604", "trnOrgnDt": "2021-06-04", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-07T16:41:00+0000", "fromTs": "2021-06-07T18:11:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 589.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-07T16:41:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 590.84, "crewProfileId": "HBCR05", "toTs": "2021-06-08T00:30:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C606", "trnOrgnDt": "2021-06-06", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-09T16:44:00+0000", "fromTs": "2021-06-09T18:14:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 590.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-09T16:44:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 592.39, "crewProfileId": "HBCR05", "toTs": "2021-06-10T00:34:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C608", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-12T16:56:00+0000", "fromTs": "2021-06-12T18:26:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 591.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-12T16:56:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.06, "crewProfileId": "HBCR05", "toTs": "2021-06-13T00:47:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C611", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-17T16:42:00+0000", "fromTs": "2021-05-17T18:12:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 595.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-17T16:42:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.01, "crewProfileId": "HBCR05", "toTs": "2021-05-18T00:37:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C616", "trnOrgnDt": "2021-05-16", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-19T16:44:00+0000", "fromTs": "2021-05-19T18:14:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 590.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-19T16:44:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 592.12, "crewProfileId": "HBCR05", "toTs": "2021-05-20T00:34:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C618", "trnOrgnDt": "2021-05-18", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-22T16:53:00+0000", "fromTs": "2021-05-22T18:23:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 592.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-22T16:53:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.11, "crewProfileId": "HBCR05", "toTs": "2021-05-23T00:45:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C621", "trnOrgnDt": "2021-05-21", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-24T16:46:00+0000", "fromTs": "2021-05-24T18:16:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 590.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-24T16:46:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.49, "crewProfileId": "HBCR05", "toTs": "2021-05-25T00:36:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C623", "trnOrgnDt": "2021-05-23", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-26T16:37:00+0000", "fromTs": "2021-05-26T18:07:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 589.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-26T16:37:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.81, "crewProfileId": "HBCR05", "toTs": "2021-05-27T00:26:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C625", "trnOrgnDt": "2021-05-25", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-29T19:50:00+0000", "fromTs": "2021-05-29T18:00:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 200.00833333333333, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 591.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-29T19:50:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 590.71, "crewProfileId": "HBCR05", "toTs": "2021-05-30T00:21:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C628", "trnOrgnDt": "2021-05-28", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-31T16:44:00+0000", "fromTs": "2021-05-31T18:14:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 592.0083333333333, "fromLocSeqNr": 13, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-31T16:44:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 591.36, "crewProfileId": "HBCR05", "toTs": "2021-06-01T00:36:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "170C630", "trnOrgnDt": "2021-05-30", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-01T19:36:00+0000", "fromTs": "2021-06-01T21:06:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-01T19:36:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-06-02T05:51:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C301", "trnOrgnDt": "2021-06-01", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-02T19:29:00+0000", "fromTs": "2021-06-02T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-02T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-06-03T05:42:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C302", "trnOrgnDt": "2021-06-02", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-03T19:30:00+0000", "fromTs": "2021-06-03T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 736.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-03T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-06-04T05:46:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C303", "trnOrgnDt": "2021-06-03", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-04T19:32:00+0000", "fromTs": "2021-06-04T21:02:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 736.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-04T19:32:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.74, "crewProfileId": "HBCR05", "toTs": "2021-06-05T05:48:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C304", "trnOrgnDt": "2021-06-04", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-05T19:30:00+0000", "fromTs": "2021-06-05T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-05T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 733.49, "crewProfileId": "HBCR05", "toTs": "2021-06-06T05:43:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C305", "trnOrgnDt": "2021-06-05", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-06T19:31:00+0000", "fromTs": "2021-06-06T21:01:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-06T19:31:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.41, "crewProfileId": "HBCR05", "toTs": "2021-06-07T05:46:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C306", "trnOrgnDt": "2021-06-06", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-08T19:29:00+0000", "fromTs": "2021-06-08T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-08T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.86, "crewProfileId": "HBCR05", "toTs": "2021-06-09T05:42:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C308", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-09T19:30:00+0000", "fromTs": "2021-06-09T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-09T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.39, "crewProfileId": "HBCR05", "toTs": "2021-06-10T05:43:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C309", "trnOrgnDt": "2021-06-09", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-10T19:29:00+0000", "fromTs": "2021-06-10T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-10T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.26, "crewProfileId": "HBCR05", "toTs": "2021-06-11T05:42:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C310", "trnOrgnDt": "2021-06-10", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-11T19:30:00+0000", "fromTs": "2021-06-11T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-11T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-06-12T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C311", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-15T19:28:00+0000", "fromTs": "2021-05-15T20:58:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 732.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-15T19:28:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.16, "crewProfileId": "HBCR05", "toTs": "2021-05-16T05:40:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C315", "trnOrgnDt": "2021-05-15", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-16T19:44:00+0000", "fromTs": "2021-05-16T21:14:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 734.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-16T19:44:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-05-17T05:58:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C316", "trnOrgnDt": "2021-05-16", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-18T19:32:00+0000", "fromTs": "2021-05-18T21:02:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-18T19:32:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.11, "crewProfileId": "HBCR05", "toTs": "2021-05-19T05:47:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C318", "trnOrgnDt": "2021-05-18", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-19T19:31:00+0000", "fromTs": "2021-05-19T21:01:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 734.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-19T19:31:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.01, "crewProfileId": "HBCR05", "toTs": "2021-05-20T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C319", "trnOrgnDt": "2021-05-19", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-20T19:29:00+0000", "fromTs": "2021-05-20T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 737.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-20T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.66, "crewProfileId": "HBCR05", "toTs": "2021-05-21T05:46:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C320", "trnOrgnDt": "2021-05-20", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-21T22:21:00+0000", "fromTs": "2021-05-21T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 172.00833333333333, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-21T22:21:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.54, "crewProfileId": "HBCR05", "toTs": "2021-05-22T05:44:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C321", "trnOrgnDt": "2021-05-21", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-22T19:29:00+0000", "fromTs": "2021-05-22T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 731.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-22T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 733.76, "crewProfileId": "HBCR05", "toTs": "2021-05-23T05:40:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C322", "trnOrgnDt": "2021-05-22", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-23T19:36:00+0000", "fromTs": "2021-05-23T21:06:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-23T19:36:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.01, "crewProfileId": "HBCR05", "toTs": "2021-05-24T05:51:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C323", "trnOrgnDt": "2021-05-23", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-25T19:29:00+0000", "fromTs": "2021-05-25T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 736.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-25T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.79, "crewProfileId": "HBCR05", "toTs": "2021-05-26T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C325", "trnOrgnDt": "2021-05-25", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-26T19:29:00+0000", "fromTs": "2021-05-26T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-26T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.01, "crewProfileId": "HBCR05", "toTs": "2021-05-27T05:44:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C326", "trnOrgnDt": "2021-05-26", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-27T19:30:00+0000", "fromTs": "2021-05-27T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-27T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.64, "crewProfileId": "HBCR05", "toTs": "2021-05-28T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C327", "trnOrgnDt": "2021-05-27", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-28T19:34:00+0000", "fromTs": "2021-05-28T21:04:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-28T19:34:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.66, "crewProfileId": "HBCR05", "toTs": "2021-05-29T05:49:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C328", "trnOrgnDt": "2021-05-28", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-29T19:29:00+0000", "fromTs": "2021-05-29T20:59:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 736.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-29T19:29:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 734.16, "crewProfileId": "HBCR05", "toTs": "2021-05-30T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C329", "trnOrgnDt": "2021-05-29", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC23", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-30T19:30:00+0000", "fromTs": "2021-05-30T21:00:00+0000", "toOs": "PC175", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC23", "enProfilePoolHomeAway": 1, "enOnDutyDuration": 735.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-30T19:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC23", "fromOs": "PC23", "enProfileDistr": "HB", "coOnDutyDuration": 735.81, "crewProfileId": "HBCR05", "toTs": "2021-05-31T05:45:00+0000", "coProfilePoolHomeAway": 1, "recrewDuration": 0.0, "trn": "171C330", "trnOrgnDt": "2021-05-30", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-09T03:27:00+0000", "fromTs": "2021-06-09T04:57:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 681.0083333333333, "fromLocSeqNr": 10, "trnType": "UPL", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-09T03:27:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 681.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-06-09T12:48:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "66EC608", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-19T03:27:00+0000", "fromTs": "2021-05-19T04:57:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 681.0083333333333, "fromLocSeqNr": 10, "trnType": "UPL", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-19T03:27:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 681.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-05-19T12:48:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "66EC618", "trnOrgnDt": "2021-05-18", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-11T01:08:00+0000", "fromTs": "2021-06-11T02:38:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 552.0083333333333, "fromLocSeqNr": 11, "trnType": "WB", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-11T01:08:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 552.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-06-11T08:20:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "91XC310", "trnOrgnDt": "2021-06-10", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-21T01:08:00+0000", "fromTs": "2021-05-21T02:38:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 552.0083333333333, "fromLocSeqNr": 11, "trnType": "WB", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-21T01:08:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 552.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-05-21T08:20:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "91XC320", "trnOrgnDt": "2021-05-20", "trnCrewDistr": "C3", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-06-12T21:30:00+0000", "fromTs": "2021-06-12T23:00:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 590.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-06-12T21:30:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 590.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-06-13T05:20:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "M70C611", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "1", "status": "ARRIVED"}, {"crewOs": "PC175", "enProfileSubDistr": "CR", "enOnDutyStartTime": "2021-05-22T22:37:00+0000", "fromTs": "2021-05-22T23:00:00+0000", "toOs": "PC23", "delayDueToCrewDuration": 67.00833333333334, "enProfilePoolOs": "PC175", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 590.0083333333333, "fromLocSeqNr": 1, "trnType": "MER", "coProfilePoolName": "CR", "coOnDutyStartTime": "2021-05-22T22:37:00+0000", "coProfileSubDistr": "CR", "coProfilePoolOs": "PC175", "fromOs": "PC175", "enProfileDistr": "HB", "coOnDutyDuration": 590.0083333333333, "crewProfileId": "HBCR05", "toTs": "2021-05-23T05:20:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "M70C621", "trnOrgnDt": "2021-05-21", "trnCrewDistr": "C6", "arvlFlag": 1, "coProfileDistr": "HB", "enProfilePoolName": "CR", "trnSection": "1", "status": "ARRIVED"}], "turnUtilizations": [{"turnId": "C002", "duration": 48.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "AUTO_RESTING"}, {"turnId": "C002", "duration": 41.07, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "ON_ASSIGNMENT"}, {"turnId": "C002", "duration": 120.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "RESTING"}, {"turnId": "C002", "duration": 72.07, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "WAITING"}, {"turnId": "D001", "duration": 48.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "AUTO_RESTING"}, {"turnId": "D001", "duration": 41.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "ON_ASSIGNMENT"}, {"turnId": "D001", "duration": 48.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "RESTING"}, {"turnId": "D001", "duration": 144.0, "boardType": "Extra", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CO", "status": "WAITING"}, {"turnId": "CR01", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "CR01", "duration": 2.917, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "CR01", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "CR01", "duration": 3.975, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "CR01", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "CR01", "duration": 22.02, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "CR01", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "CR01", "duration": 40.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "CR01", "duration": 3.355, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "CR01", "duration": 54.94, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "CR01", "duration": 66.24, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "CR04", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "CR04", "duration": 4.704, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "CR04", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "CR04", "duration": 4.8, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "CR04", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "CR04", "duration": 25.29, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "CR04", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "CR04", "duration": 34.75, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "CR04", "duration": 3.158, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "CR04", "duration": 48.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "CR04", "duration": 70.01, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "CR06", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "CR06", "duration": 4.14, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "CR06", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "CR06", "duration": 4.688, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "CR06", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "CR06", "duration": 23.85, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "CR06", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "CR06", "duration": 40.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "CR06", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "CR06", "duration": 50.77, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "CR06", "duration": 62.95, "boardType": "Pool", "subDistr": "CR", "craft": "CO", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "B001", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "B001", "duration": 50.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "AUTO_RESTING"}, {"turnId": "B001", "duration": 0.984, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "B001", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "B001", "duration": 4.5, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "B001", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "B001", "duration": 23.86, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "B001", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "B001", "duration": 37.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "B001", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "B001", "duration": 36.55, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "B001", "duration": 52.9, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "C001", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "C001", "duration": 50.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "AUTO_RESTING"}, {"turnId": "C001", "duration": 0.932, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "C001", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "C001", "duration": 4.5, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "C001", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "C001", "duration": 23.83, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "C001", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "C001", "duration": 30.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "C001", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "C001", "duration": 59.54, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "C001", "duration": 40.7, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "D001", "duration": 6.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "D001", "duration": 50.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "AUTO_RESTING"}, {"turnId": "D001", "duration": 0.92, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "D001", "duration": 8.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "D001", "duration": 4.5, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "D001", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "D001", "duration": 23.83, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "D001", "duration": 0.001, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "D001", "duration": 30.75, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "D001", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "D001", "duration": 57.79, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "VACANT"}, {"turnId": "D001", "duration": 30.57, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU02", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU02", "duration": 1.0998611111111112, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "MU02", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU02", "duration": 1.5, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU02", "duration": 0.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU02", "duration": 8.77, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU02", "duration": 0.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU02", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU02", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "MU02", "duration": 0.017, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU03", "duration": 2.514, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU03", "duration": 0.833, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "MU03", "duration": 3.2, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU03", "duration": 2.25, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU03", "duration": 0.629, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU03", "duration": 10.65, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU03", "duration": 0.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU03", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU03", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "MU03", "duration": 2.009, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU04", "duration": 3.2, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU04", "duration": 0.9, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "MU04", "duration": 3.2, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU04", "duration": 3.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU04", "duration": 0.8, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU04", "duration": 15.12, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU04", "duration": 0.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU04", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU04", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "MU04", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU05", "duration": 2.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU05", "duration": 0.85, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "MU05", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU05", "duration": 2.25, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU05", "duration": 0.8, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU05", "duration": 11.93, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU05", "duration": 0.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU05", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU05", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "MU05", "duration": 2.009, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU06", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU06", "duration": 0.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU06", "duration": 0.0001388888888888889, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU07", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU07", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU07", "duration": 1.5, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU07", "duration": 0.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU07", "duration": 8.759, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU07", "duration": 0.0001388888888888889, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU07", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU07", "duration": 2.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "RIDING_TAXI"}, {"turnId": "MU07", "duration": 0.016777777777777777, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU08", "duration": 3.2, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU08", "duration": 0.8998611111111111, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "DETAINED"}, {"turnId": "MU08", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "MU08", "duration": 3.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "MU08", "duration": 0.8, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU08", "duration": 15.15, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "OPERATING"}, {"turnId": "MU08", "duration": 0.0002777777777777778, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "MU08", "duration": 10.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "RESTING"}, {"turnId": "MU08", "duration": 4.0, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "WAITING"}, {"turnId": "MU09", "duration": 1.6, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "ASSIGNED"}, {"turnId": "MU09", "duration": 0.4, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "CR", "status": "LEFT_FOR_WORK"}, {"turnId": "MU09", "duration": 0.0001388888888888889, "boardType": "Pool", "subDistr": "CR", "craft": "EN", "ondutyFlag": "Y", "distr": "HB", "boardName": "CR", "status": "READY_TO_DEPART"}, {"turnId": "C001", "duration": 50.0, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "AUTO_RESTING"}, {"turnId": "C001", "duration": 39.58, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "C001", "duration": 48.0, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "RESTING"}, {"turnId": "C001", "duration": 102.56, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "WAITING"}, {"turnId": "D001", "duration": 34.53, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "D001", "duration": 48.0, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "RESTING"}, {"turnId": "D001", "duration": 142.0, "boardType": "Extra", "subDistr": "CR", "craft": "EN", "ondutyFlag": "N", "distr": "HB", "boardName": "EN", "status": "WAITING"}]}