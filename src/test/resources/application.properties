spring.main.allow-bean-definition-overriding=true

spring.scpm.datasource.driver-class-name=org.hsqldb.jdbc.JDBCDriver
spring.scpm.datasource.jdbcUrl=****************************************
spring.scpm.datasource.username=sa
spring.scpm.datasource.password=

TrnTypeDAOImpl.selectTrnTypeSql=select * from td.trn_type
BssTrnStrtDAOImpl.selectBssTrnStrtSql=select * from cpo.bss_trn_strt
HistDataDAOImpl.selectRecentExtlStatEarnSql=select * from cpo.recent_extl_stat_earn 
ApcnDataDAOImpl.selectApcnFullTableSql=select * from cpo.apcn order by src_sys_last_uptd_date desc
LocalYardJobDAOImpl.getLocalTrainsSql=select * from lyj.local_train where trn_orgn_dt between :startDate and :endDate
LocalYardJobDAOImpl.getYardJobsSql=select * from lyj.yard_job where trn_orgn_dt between :startDate and :endDate
TsrStationControlDAOImpl.getTsrCrewPair=select crew_stn as crewStation, tsr_stn as tsrStation from dcccall.tsr_crew_pair
TransitTimeDAOImpl.getHistoricalTransitTimes1=select * from td.historical_transit_times;
TransitTimeDAOImpl.getHistoricalTransitTimes2=select * from td.historical_transit_times;
TransitTimeDAOImpl.getHistoricalTransitTimes3=select * from td.historical_transit_times;
TransitTimeDAOImpl.getHistoricalTransitTimes4=select * from td.historical_transit_times;
TransitTimeDAOImpl.getHistoricalTransitTimes5=select * from td.historical_transit_times;
CostCtrDAOImpl.getCostCenterByTrnSql=select * from td.cost_ctr;
TargetHeadcountByHireGrpDAOImpl.insertSql=INSERT INTO CCP.TARGET_HEADCOUNT_BY_HIRE_GROUP (SCENARIO_ID, HIRE_GRP_DESC, ROAD_SIMULATION_OUTPUT_PEOPLE_STARTS, ROAD_SIMULATION_OUTPUT_WEEKLY_PEOPLE_STARTS, ROAD_SIMULATION_OUTPUT_HEADCOUNT, ROAD_SIMULATION_OUTPUT_PRODUCTIVITY, YARD_LOCAL_SIMULATION_OUTPUT_PEOPLE_STARTS, YARD_LOCAL_SIMULATION_OUTPUT_WEEKLY_PEOPLE_STARTS, YARD_LOCAL_SIMULATION_OUTPUT_HEADCOUNT, SIMULATION_OUTPUT_HEADCOUNT, SIMULATION_OUTPUT_HEADCOUNT_PERCENTILE_85, SPW_MEAN, SPW_STDEV, HISTORICAL_PRODUCTIVITY_HEADCOUNT_PERCENTILE_50, HISTORICAL_PRODUCTIVITY_HEADCOUNT_PERCENTILE_85) SELECT * FROM CCP.SEED_TARGET_HEADCOUNT_BY_HIRE_GROUP where SCENARIO_ID=:scenarioId;
randomSeeds=1,2,3,4

logging.level.com.nscorp=WARN
logging.level.org.springframework=ERROR

# Enable to see sql statements.
# logging.level.org.springframework.jdbc.core=WARN
# logging.level.org.springframework.WARN
# logging.level.root=WARN

TestUtils.assertionsEnabled=true

flatTransactions=true

PlanDAOImpl.numSaveLoadThreads=1
