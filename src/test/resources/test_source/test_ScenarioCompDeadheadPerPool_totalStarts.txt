[DeadheadComp(distr=HB, subDistr=CW, boardName=C2, boardType=Extra, craft=CO, baseCount=1.0, testCount=2.0, countDiff=1.0), DeadheadComp(distr=HB, subDistr=CW, boardName=CM, boardType=Pool, craft=CO, baseCount=2.0, testCount=1.0, countDiff=-1.0), DeadheadComp(distr=HB, subDistr=CW, boardName=CM, boardType=Pool, craft=EN, baseCount=2.0, testCount=1.0, countDiff=-1.0), DeadheadComp(distr=HB, subDistr=CW, boardName=E3, boardType=Extra, craft=EN, baseCount=1.0, testCount=2.0, countDiff=1.0), DeadheadComp(distr=HB, subDistr=LE, boardName=VB, boardType=Pool, craft=CO, baseCount=7.0, testCount=2.0, countDiff=-5.0), DeadheadComp(distr=HB, subDistr=LE, boardName=VB, boardType=Pool, craft=EN, baseCount=5.0, testCount=7.0, countDiff=2.0), DeadheadComp(distr=PO, subDistr=SC, boardName=B8, boardType=Extra, craft=CO, baseCount=10.0, testCount=18.0, countDiff=8.0), DeadheadComp(distr=PO, subDistr=SC, boardName=CD, boardType=Pool, craft=CO, baseCount=31.0, testCount=17.0, countDiff=-14.0), DeadheadComp(distr=PO, subDistr=SC, boardName=CD, boardType=Pool, craft=EN, baseCount=29.0, testCount=23.0, countDiff=-6.0), DeadheadComp(distr=PO, subDistr=SC, boardName=E8, boardType=Extra, craft=EN, baseCount=8.0, testCount=12.0, countDiff=4.0), DeadheadComp(distr=PO, subDistr=SC, boardName=PW, boardType=Pool, craft=CO, baseCount=13.0, testCount=17.0, countDiff=4.0), DeadheadComp(distr=PO, subDistr=SC, boardName=PW, boardType=Pool, craft=EN, baseCount=15.0, testCount=15.0, countDiff=0.0), DeadheadComp(distr=HB, subDistr=CR, boardName=CO, boardType=Extra, craft=CO, baseCount=1.0, testCount=0.0, countDiff=-1.0), DeadheadComp(distr=HB, subDistr=CR, boardName=CR, boardType=Pool, craft=CO, baseCount=3.0, testCount=0.0, countDiff=-3.0), DeadheadComp(distr=HB, subDistr=CR, boardName=CR, boardType=Pool, craft=EN, baseCount=3.0, testCount=0.0, countDiff=-3.0), DeadheadComp(distr=HB, subDistr=CR, boardName=EN, boardType=Extra, craft=EN, baseCount=1.0, testCount=0.0, countDiff=-1.0), DeadheadComp(distr=PI, subDistr=GR, boardName=C9, boardType=Extra, craft=CO, baseCount=0.0, testCount=1.0, countDiff=1.0), DeadheadComp(distr=PI, subDistr=GR, boardName=E2, boardType=Extra, craft=EN, baseCount=0.0, testCount=1.0, countDiff=1.0)]