{"analysesByKey": {"pk[pid=HBCR05, coos=PC175]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "numExbPoolTurns": 5, "numPoolTurns": 7, "optimizableExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "poolKey": "pk[d=HB,s=CR,p=CR]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=HBCR05, coos=PC175]", "profileKeyWithCraft": "pkwc[pid=HBCR05, coos=PC175, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "protectingExbTurnKeys": "[xb-tk[d=HB,s=CR,exb=CO,tid=A001], xb-tk[d=HB,s=CR,exb=CO,tid=B001], xb-tk[d=HB,s=CR,exb=CO,tid=C001], xb-tk[d=HB,s=CR,exb=CO,tid=C002], xb-tk[d=HB,s=CR,exb=CO,tid=D001]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=HB,s=CR,p=CR,c=EN,t=A001], tk[d=HB,s=CR,p=CR,c=EN,t=B001], tk[d=HB,s=CR,p=CR,c=EN,t=C001], tk[d=HB,s=CR,p=CR,c=CO,t=CR01], tk[d=HB,s=CR,p=CR,c=CO,t=CR04], tk[d=HB,s=CR,p=CR,c=CO,t=CR06], tk[d=HB,s=CR,p=CR,c=EN,t=D001]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "numExbPoolTurns": 4, "numPoolTurns": 7, "optimizableExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "poolKey": "pk[d=HB,s=CR,p=CR]", "poolOptimizable": true, "poolOptimizeSize": true, "poolTurnRangeHi": 3, "poolTurnRangeLow": 3, "profileKey": "pk[pid=HBCR05, coos=PC175]", "profileKeyWithCraft": "pkwc[pid=HBCR05, coos=PC175, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "protectingExbTurnKeys": "[xb-tk[d=HB,s=CR,exb=EN,tid=A001], xb-tk[d=HB,s=CR,exb=EN,tid=B001], xb-tk[d=HB,s=CR,exb=EN,tid=C001], xb-tk[d=HB,s=CR,exb=EN,tid=D001]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=HB,s=CR,p=CR,c=EN,t=A001], tk[d=HB,s=CR,p=CR,c=EN,t=B001], tk[d=HB,s=CR,p=CR,c=EN,t=C001], tk[d=HB,s=CR,p=CR,c=CO,t=CR01], tk[d=HB,s=CR,p=CR,c=CO,t=CR04], tk[d=HB,s=CR,p=CR,c=CO,t=CR06], tk[d=HB,s=CR,p=CR,c=EN,t=D001]]", "useable": true}, "profileKey": "pk[pid=HBCR05, coos=PC175]", "useable": true}, "pk[pid=HBCR05, coos=PC23]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "numExbPoolTurns": 5, "numPoolTurns": 7, "optimizableExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "poolKey": "pk[d=HB,s=CR,p=CR]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=HBCR05, coos=PC23]", "profileKeyWithCraft": "pkwc[pid=HBCR05, coos=PC23, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=HB,sd=CR,p=CO]]", "protectingExbTurnKeys": "[xb-tk[d=HB,s=CR,exb=CO,tid=A001], xb-tk[d=HB,s=CR,exb=CO,tid=B001], xb-tk[d=HB,s=CR,exb=CO,tid=C001], xb-tk[d=HB,s=CR,exb=CO,tid=C002], xb-tk[d=HB,s=CR,exb=CO,tid=D001]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=HB,s=CR,p=CR,c=EN,t=A001], tk[d=HB,s=CR,p=CR,c=EN,t=B001], tk[d=HB,s=CR,p=CR,c=EN,t=C001], tk[d=HB,s=CR,p=CR,c=CO,t=CR01], tk[d=HB,s=CR,p=CR,c=CO,t=CR04], tk[d=HB,s=CR,p=CR,c=CO,t=CR06], tk[d=HB,s=CR,p=CR,c=EN,t=D001]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "numExbPoolTurns": 4, "numPoolTurns": 7, "optimizableExbPoolSetUps": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "poolKey": "pk[d=HB,s=CR,p=CR]", "poolOptimizable": true, "poolOptimizeSize": true, "poolTurnRangeHi": 3, "poolTurnRangeLow": 3, "profileKey": "pk[pid=HBCR05, coos=PC23]", "profileKeyWithCraft": "pkwc[pid=HBCR05, coos=PC23, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=HB,sd=CR,p=EN]]", "protectingExbTurnKeys": "[xb-tk[d=HB,s=CR,exb=EN,tid=A001], xb-tk[d=HB,s=CR,exb=EN,tid=B001], xb-tk[d=HB,s=CR,exb=EN,tid=C001], xb-tk[d=HB,s=CR,exb=EN,tid=D001]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=HB,s=CR,p=CR,c=EN,t=A001], tk[d=HB,s=CR,p=CR,c=EN,t=B001], tk[d=HB,s=CR,p=CR,c=EN,t=C001], tk[d=HB,s=CR,p=CR,c=CO,t=CR01], tk[d=HB,s=CR,p=CR,c=CO,t=CR04], tk[d=HB,s=CR,p=CR,c=CO,t=CR06], tk[d=HB,s=CR,p=CR,c=EN,t=D001]]", "useable": true}, "profileKey": "pk[pid=HBCR05, coos=PC23]", "useable": true}, "pk[pid=POSC01, coos=00248]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "numExbPoolTurns": 8, "numPoolTurns": 42, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "poolKey": "pk[d=PO,s=SC,p=CD]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=POSC01, coos=00248]", "profileKeyWithCraft": "pkwc[pid=POSC01, coos=00248, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=B8,tid=A001], xb-tk[d=PO,s=SC,exb=B8,tid=A002], xb-tk[d=PO,s=SC,exb=B8,tid=A003], xb-tk[d=PO,s=SC,exb=B8,tid=B001], xb-tk[d=PO,s=SC,exb=B8,tid=B002], xb-tk[d=PO,s=SC,exb=B8,tid=C001], xb-tk[d=PO,s=SC,exb=B8,tid=C003], xb-tk[d=PO,s=SC,exb=B8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=CD,c=CO,t=CD01], tk[d=PO,s=SC,p=CD,c=EN,t=CD01], tk[d=PO,s=SC,p=CD,c=CO,t=CD02], tk[d=PO,s=SC,p=CD,c=EN,t=CD02], tk[d=PO,s=SC,p=CD,c=CO,t=CD03], tk[d=PO,s=SC,p=CD,c=EN,t=CD03], tk[d=PO,s=SC,p=CD,c=CO,t=CD04], tk[d=PO,s=SC,p=CD,c=EN,t=CD04], tk[d=PO,s=SC,p=CD,c=CO,t=CD05], tk[d=PO,s=SC,p=CD,c=EN,t=CD05], tk[d=PO,s=SC,p=CD,c=CO,t=CD06], tk[d=PO,s=SC,p=CD,c=EN,t=CD06], tk[d=PO,s=SC,p=CD,c=CO,t=CD07], tk[d=PO,s=SC,p=CD,c=EN,t=CD07], tk[d=PO,s=SC,p=CD,c=CO,t=CD08], tk[d=PO,s=SC,p=CD,c=EN,t=CD08], tk[d=PO,s=SC,p=CD,c=CO,t=CD09], tk[d=PO,s=SC,p=CD,c=EN,t=CD09], tk[d=PO,s=SC,p=CD,c=CO,t=CD10], tk[d=PO,s=SC,p=CD,c=EN,t=CD10], tk[d=PO,s=SC,p=CD,c=CO,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD12], tk[d=PO,s=SC,p=CD,c=EN,t=CD13], tk[d=PO,s=SC,p=CD,c=EN,t=CD14], tk[d=PO,s=SC,p=CD,c=EN,t=CD15], tk[d=PO,s=SC,p=CD,c=EN,t=CD16], tk[d=PO,s=SC,p=CD,c=EN,t=CD17], tk[d=PO,s=SC,p=CD,c=EN,t=CD18], tk[d=PO,s=SC,p=CD,c=EN,t=CD19], tk[d=PO,s=SC,p=CD,c=EN,t=CD20], tk[d=PO,s=SC,p=CD,c=EN,t=CD21], tk[d=PO,s=SC,p=CD,c=EN,t=CD22], tk[d=PO,s=SC,p=CD,c=CO,t=SP01], tk[d=PO,s=SC,p=CD,c=CO,t=SP02], tk[d=PO,s=SC,p=CD,c=CO,t=SP03], tk[d=PO,s=SC,p=CD,c=CO,t=SP04], tk[d=PO,s=SC,p=CD,c=CO,t=SP06], tk[d=PO,s=SC,p=CD,c=CO,t=SP07], tk[d=PO,s=SC,p=CD,c=CO,t=SP08], tk[d=PO,s=SC,p=CD,c=CO,t=SP10], tk[d=PO,s=SC,p=CD,c=CO,t=SP11]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "numExbPoolTurns": 9, "numPoolTurns": 42, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "poolKey": "pk[d=PO,s=SC,p=CD]", "poolOptimizable": false, "poolOptimizeSize": false, "poolTurnRangeHi": 22, "poolTurnRangeLow": 22, "profileKey": "pk[pid=POSC01, coos=00248]", "profileKeyWithCraft": "pkwc[pid=POSC01, coos=00248, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=E8,tid=A001], xb-tk[d=PO,s=SC,exb=E8,tid=A002], xb-tk[d=PO,s=SC,exb=E8,tid=A003], xb-tk[d=PO,s=SC,exb=E8,tid=B001], xb-tk[d=PO,s=SC,exb=E8,tid=B002], xb-tk[d=PO,s=SC,exb=E8,tid=C001], xb-tk[d=PO,s=SC,exb=E8,tid=C002], xb-tk[d=PO,s=SC,exb=E8,tid=D001], xb-tk[d=PO,s=SC,exb=E8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=CD,c=CO,t=CD01], tk[d=PO,s=SC,p=CD,c=EN,t=CD01], tk[d=PO,s=SC,p=CD,c=CO,t=CD02], tk[d=PO,s=SC,p=CD,c=EN,t=CD02], tk[d=PO,s=SC,p=CD,c=CO,t=CD03], tk[d=PO,s=SC,p=CD,c=EN,t=CD03], tk[d=PO,s=SC,p=CD,c=CO,t=CD04], tk[d=PO,s=SC,p=CD,c=EN,t=CD04], tk[d=PO,s=SC,p=CD,c=CO,t=CD05], tk[d=PO,s=SC,p=CD,c=EN,t=CD05], tk[d=PO,s=SC,p=CD,c=CO,t=CD06], tk[d=PO,s=SC,p=CD,c=EN,t=CD06], tk[d=PO,s=SC,p=CD,c=CO,t=CD07], tk[d=PO,s=SC,p=CD,c=EN,t=CD07], tk[d=PO,s=SC,p=CD,c=CO,t=CD08], tk[d=PO,s=SC,p=CD,c=EN,t=CD08], tk[d=PO,s=SC,p=CD,c=CO,t=CD09], tk[d=PO,s=SC,p=CD,c=EN,t=CD09], tk[d=PO,s=SC,p=CD,c=CO,t=CD10], tk[d=PO,s=SC,p=CD,c=EN,t=CD10], tk[d=PO,s=SC,p=CD,c=CO,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD12], tk[d=PO,s=SC,p=CD,c=EN,t=CD13], tk[d=PO,s=SC,p=CD,c=EN,t=CD14], tk[d=PO,s=SC,p=CD,c=EN,t=CD15], tk[d=PO,s=SC,p=CD,c=EN,t=CD16], tk[d=PO,s=SC,p=CD,c=EN,t=CD17], tk[d=PO,s=SC,p=CD,c=EN,t=CD18], tk[d=PO,s=SC,p=CD,c=EN,t=CD19], tk[d=PO,s=SC,p=CD,c=EN,t=CD20], tk[d=PO,s=SC,p=CD,c=EN,t=CD21], tk[d=PO,s=SC,p=CD,c=EN,t=CD22], tk[d=PO,s=SC,p=CD,c=CO,t=SP01], tk[d=PO,s=SC,p=CD,c=CO,t=SP02], tk[d=PO,s=SC,p=CD,c=CO,t=SP03], tk[d=PO,s=SC,p=CD,c=CO,t=SP04], tk[d=PO,s=SC,p=CD,c=CO,t=SP06], tk[d=PO,s=SC,p=CD,c=CO,t=SP07], tk[d=PO,s=SC,p=CD,c=CO,t=SP08], tk[d=PO,s=SC,p=CD,c=CO,t=SP10], tk[d=PO,s=SC,p=CD,c=CO,t=SP11]]", "useable": true}, "profileKey": "pk[pid=POSC01, coos=00248]", "useable": true}, "pk[pid=POSC01, coos=10563]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "numExbPoolTurns": 8, "numPoolTurns": 42, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "poolKey": "pk[d=PO,s=SC,p=CD]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=POSC01, coos=10563]", "profileKeyWithCraft": "pkwc[pid=POSC01, coos=10563, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=B8,tid=A001], xb-tk[d=PO,s=SC,exb=B8,tid=A002], xb-tk[d=PO,s=SC,exb=B8,tid=A003], xb-tk[d=PO,s=SC,exb=B8,tid=B001], xb-tk[d=PO,s=SC,exb=B8,tid=B002], xb-tk[d=PO,s=SC,exb=B8,tid=C001], xb-tk[d=PO,s=SC,exb=B8,tid=C003], xb-tk[d=PO,s=SC,exb=B8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=CD,c=CO,t=CD01], tk[d=PO,s=SC,p=CD,c=EN,t=CD01], tk[d=PO,s=SC,p=CD,c=CO,t=CD02], tk[d=PO,s=SC,p=CD,c=EN,t=CD02], tk[d=PO,s=SC,p=CD,c=CO,t=CD03], tk[d=PO,s=SC,p=CD,c=EN,t=CD03], tk[d=PO,s=SC,p=CD,c=CO,t=CD04], tk[d=PO,s=SC,p=CD,c=EN,t=CD04], tk[d=PO,s=SC,p=CD,c=CO,t=CD05], tk[d=PO,s=SC,p=CD,c=EN,t=CD05], tk[d=PO,s=SC,p=CD,c=CO,t=CD06], tk[d=PO,s=SC,p=CD,c=EN,t=CD06], tk[d=PO,s=SC,p=CD,c=CO,t=CD07], tk[d=PO,s=SC,p=CD,c=EN,t=CD07], tk[d=PO,s=SC,p=CD,c=CO,t=CD08], tk[d=PO,s=SC,p=CD,c=EN,t=CD08], tk[d=PO,s=SC,p=CD,c=CO,t=CD09], tk[d=PO,s=SC,p=CD,c=EN,t=CD09], tk[d=PO,s=SC,p=CD,c=CO,t=CD10], tk[d=PO,s=SC,p=CD,c=EN,t=CD10], tk[d=PO,s=SC,p=CD,c=CO,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD12], tk[d=PO,s=SC,p=CD,c=EN,t=CD13], tk[d=PO,s=SC,p=CD,c=EN,t=CD14], tk[d=PO,s=SC,p=CD,c=EN,t=CD15], tk[d=PO,s=SC,p=CD,c=EN,t=CD16], tk[d=PO,s=SC,p=CD,c=EN,t=CD17], tk[d=PO,s=SC,p=CD,c=EN,t=CD18], tk[d=PO,s=SC,p=CD,c=EN,t=CD19], tk[d=PO,s=SC,p=CD,c=EN,t=CD20], tk[d=PO,s=SC,p=CD,c=EN,t=CD21], tk[d=PO,s=SC,p=CD,c=EN,t=CD22], tk[d=PO,s=SC,p=CD,c=CO,t=SP01], tk[d=PO,s=SC,p=CD,c=CO,t=SP02], tk[d=PO,s=SC,p=CD,c=CO,t=SP03], tk[d=PO,s=SC,p=CD,c=CO,t=SP04], tk[d=PO,s=SC,p=CD,c=CO,t=SP06], tk[d=PO,s=SC,p=CD,c=CO,t=SP07], tk[d=PO,s=SC,p=CD,c=CO,t=SP08], tk[d=PO,s=SC,p=CD,c=CO,t=SP10], tk[d=PO,s=SC,p=CD,c=CO,t=SP11]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "numExbPoolTurns": 9, "numPoolTurns": 42, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "poolKey": "pk[d=PO,s=SC,p=CD]", "poolOptimizable": false, "poolOptimizeSize": false, "poolTurnRangeHi": 22, "poolTurnRangeLow": 22, "profileKey": "pk[pid=POSC01, coos=10563]", "profileKeyWithCraft": "pkwc[pid=POSC01, coos=10563, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=E8,tid=A001], xb-tk[d=PO,s=SC,exb=E8,tid=A002], xb-tk[d=PO,s=SC,exb=E8,tid=A003], xb-tk[d=PO,s=SC,exb=E8,tid=B001], xb-tk[d=PO,s=SC,exb=E8,tid=B002], xb-tk[d=PO,s=SC,exb=E8,tid=C001], xb-tk[d=PO,s=SC,exb=E8,tid=C002], xb-tk[d=PO,s=SC,exb=E8,tid=D001], xb-tk[d=PO,s=SC,exb=E8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=CD,c=CO,t=CD01], tk[d=PO,s=SC,p=CD,c=EN,t=CD01], tk[d=PO,s=SC,p=CD,c=CO,t=CD02], tk[d=PO,s=SC,p=CD,c=EN,t=CD02], tk[d=PO,s=SC,p=CD,c=CO,t=CD03], tk[d=PO,s=SC,p=CD,c=EN,t=CD03], tk[d=PO,s=SC,p=CD,c=CO,t=CD04], tk[d=PO,s=SC,p=CD,c=EN,t=CD04], tk[d=PO,s=SC,p=CD,c=CO,t=CD05], tk[d=PO,s=SC,p=CD,c=EN,t=CD05], tk[d=PO,s=SC,p=CD,c=CO,t=CD06], tk[d=PO,s=SC,p=CD,c=EN,t=CD06], tk[d=PO,s=SC,p=CD,c=CO,t=CD07], tk[d=PO,s=SC,p=CD,c=EN,t=CD07], tk[d=PO,s=SC,p=CD,c=CO,t=CD08], tk[d=PO,s=SC,p=CD,c=EN,t=CD08], tk[d=PO,s=SC,p=CD,c=CO,t=CD09], tk[d=PO,s=SC,p=CD,c=EN,t=CD09], tk[d=PO,s=SC,p=CD,c=CO,t=CD10], tk[d=PO,s=SC,p=CD,c=EN,t=CD10], tk[d=PO,s=SC,p=CD,c=CO,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD11], tk[d=PO,s=SC,p=CD,c=EN,t=CD12], tk[d=PO,s=SC,p=CD,c=EN,t=CD13], tk[d=PO,s=SC,p=CD,c=EN,t=CD14], tk[d=PO,s=SC,p=CD,c=EN,t=CD15], tk[d=PO,s=SC,p=CD,c=EN,t=CD16], tk[d=PO,s=SC,p=CD,c=EN,t=CD17], tk[d=PO,s=SC,p=CD,c=EN,t=CD18], tk[d=PO,s=SC,p=CD,c=EN,t=CD19], tk[d=PO,s=SC,p=CD,c=EN,t=CD20], tk[d=PO,s=SC,p=CD,c=EN,t=CD21], tk[d=PO,s=SC,p=CD,c=EN,t=CD22], tk[d=PO,s=SC,p=CD,c=CO,t=SP01], tk[d=PO,s=SC,p=CD,c=CO,t=SP02], tk[d=PO,s=SC,p=CD,c=CO,t=SP03], tk[d=PO,s=SC,p=CD,c=CO,t=SP04], tk[d=PO,s=SC,p=CD,c=CO,t=SP06], tk[d=PO,s=SC,p=CD,c=CO,t=SP07], tk[d=PO,s=SC,p=CD,c=CO,t=SP08], tk[d=PO,s=SC,p=CD,c=CO,t=SP10], tk[d=PO,s=SC,p=CD,c=CO,t=SP11]]", "useable": true}, "profileKey": "pk[pid=POSC01, coos=10563]", "useable": true}, "pk[pid=POSC10, coos=10654]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "numExbPoolTurns": 8, "numPoolTurns": 10, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "poolKey": "pk[d=PO,s=SC,p=PW]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=POSC10, coos=10654]", "profileKeyWithCraft": "pkwc[pid=POSC10, coos=10654, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=B8,tid=A001], xb-tk[d=PO,s=SC,exb=B8,tid=A002], xb-tk[d=PO,s=SC,exb=B8,tid=A003], xb-tk[d=PO,s=SC,exb=B8,tid=B001], xb-tk[d=PO,s=SC,exb=B8,tid=B002], xb-tk[d=PO,s=SC,exb=B8,tid=C001], xb-tk[d=PO,s=SC,exb=B8,tid=C003], xb-tk[d=PO,s=SC,exb=B8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=PW,c=EN,t=A001], tk[d=PO,s=SC,p=PW,c=EN,t=B001], tk[d=PO,s=SC,p=PW,c=EN,t=B002], tk[d=PO,s=SC,p=PW,c=EN,t=C001], tk[d=PO,s=SC,p=PW,c=EN,t=D001], tk[d=PO,s=SC,p=PW,c=EN,t=D002], tk[d=PO,s=SC,p=PW,c=CO,t=PW01], tk[d=PO,s=SC,p=PW,c=CO,t=PW02], tk[d=PO,s=SC,p=PW,c=CO,t=PW03], tk[d=PO,s=SC,p=PW,c=CO,t=PW04]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "numExbPoolTurns": 9, "numPoolTurns": 10, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "poolKey": "pk[d=PO,s=SC,p=PW]", "poolOptimizable": false, "poolOptimizeSize": false, "poolTurnRangeHi": 6, "poolTurnRangeLow": 6, "profileKey": "pk[pid=POSC10, coos=10654]", "profileKeyWithCraft": "pkwc[pid=POSC10, coos=10654, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=E8,tid=A001], xb-tk[d=PO,s=SC,exb=E8,tid=A002], xb-tk[d=PO,s=SC,exb=E8,tid=A003], xb-tk[d=PO,s=SC,exb=E8,tid=B001], xb-tk[d=PO,s=SC,exb=E8,tid=B002], xb-tk[d=PO,s=SC,exb=E8,tid=C001], xb-tk[d=PO,s=SC,exb=E8,tid=C002], xb-tk[d=PO,s=SC,exb=E8,tid=D001], xb-tk[d=PO,s=SC,exb=E8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=PW,c=EN,t=A001], tk[d=PO,s=SC,p=PW,c=EN,t=B001], tk[d=PO,s=SC,p=PW,c=EN,t=B002], tk[d=PO,s=SC,p=PW,c=EN,t=C001], tk[d=PO,s=SC,p=PW,c=EN,t=D001], tk[d=PO,s=SC,p=PW,c=EN,t=D002], tk[d=PO,s=SC,p=PW,c=CO,t=PW01], tk[d=PO,s=SC,p=PW,c=CO,t=PW02], tk[d=PO,s=SC,p=PW,c=CO,t=PW03], tk[d=PO,s=SC,p=PW,c=CO,t=PW04]]", "useable": true}, "profileKey": "pk[pid=POSC10, coos=10654]", "useable": true}, "pk[pid=POSC10, coos=10563]": {"coAnalysis": {"craft": "CO", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "numExbPoolTurns": 8, "numPoolTurns": 10, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "poolKey": "pk[d=PO,s=SC,p=PW]", "poolOptimizable": false, "poolOptimizeSize": true, "poolTurnRangeHi": null, "poolTurnRangeLow": null, "profileKey": "pk[pid=POSC10, coos=10563]", "profileKeyWithCraft": "pkwc[pid=POSC10, coos=10563, craft=CO]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=B8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=B8,tid=A001], xb-tk[d=PO,s=SC,exb=B8,tid=A002], xb-tk[d=PO,s=SC,exb=B8,tid=A003], xb-tk[d=PO,s=SC,exb=B8,tid=B001], xb-tk[d=PO,s=SC,exb=B8,tid=B002], xb-tk[d=PO,s=SC,exb=B8,tid=C001], xb-tk[d=PO,s=SC,exb=B8,tid=C003], xb-tk[d=PO,s=SC,exb=B8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=PW,c=EN,t=A001], tk[d=PO,s=SC,p=PW,c=EN,t=B001], tk[d=PO,s=SC,p=PW,c=EN,t=B002], tk[d=PO,s=SC,p=PW,c=EN,t=C001], tk[d=PO,s=SC,p=PW,c=EN,t=D001], tk[d=PO,s=SC,p=PW,c=EN,t=D002], tk[d=PO,s=SC,p=PW,c=CO,t=PW01], tk[d=PO,s=SC,p=PW,c=CO,t=PW02], tk[d=PO,s=SC,p=PW,c=CO,t=PW03], tk[d=PO,s=SC,p=PW,c=CO,t=PW04]]", "useable": true}, "enAnalysis": {"craft": "EN", "nonEmptyExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "numExbPoolTurns": 9, "numPoolTurns": 10, "optimizableExbPoolSetUps": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "poolKey": "pk[d=PO,s=SC,p=PW]", "poolOptimizable": false, "poolOptimizeSize": false, "poolTurnRangeHi": 6, "poolTurnRangeLow": 6, "profileKey": "pk[pid=POSC10, coos=10563]", "profileKeyWithCraft": "pkwc[pid=POSC10, coos=10563, craft=EN]", "protectingExbPoolSetUpKeys": "[xb-psu-k[d=PO,sd=SC,p=E8]]", "protectingExbTurnKeys": "[xb-tk[d=PO,s=SC,exb=E8,tid=A001], xb-tk[d=PO,s=SC,exb=E8,tid=A002], xb-tk[d=PO,s=SC,exb=E8,tid=A003], xb-tk[d=PO,s=SC,exb=E8,tid=B001], xb-tk[d=PO,s=SC,exb=E8,tid=B002], xb-tk[d=PO,s=SC,exb=E8,tid=C001], xb-tk[d=PO,s=SC,exb=E8,tid=C002], xb-tk[d=PO,s=SC,exb=E8,tid=D001], xb-tk[d=PO,s=SC,exb=E8,tid=D002]]", "reasonCode": "NONZERO_TURNS_IN_POOL", "turnKeys": "[tk[d=PO,s=SC,p=PW,c=EN,t=A001], tk[d=PO,s=SC,p=PW,c=EN,t=B001], tk[d=PO,s=SC,p=PW,c=EN,t=B002], tk[d=PO,s=SC,p=PW,c=EN,t=C001], tk[d=PO,s=SC,p=PW,c=EN,t=D001], tk[d=PO,s=SC,p=PW,c=EN,t=D002], tk[d=PO,s=SC,p=PW,c=CO,t=PW01], tk[d=PO,s=SC,p=PW,c=CO,t=PW02], tk[d=PO,s=SC,p=PW,c=CO,t=PW03], tk[d=PO,s=SC,p=PW,c=CO,t=PW04]]", "useable": true}, "profileKey": "pk[pid=POSC10, coos=10563]", "useable": true}}}