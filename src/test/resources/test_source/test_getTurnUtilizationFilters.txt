[TurnUtilizationFilter(distr=HB, subDistr=CR, boardName=CO, boardType=Extra, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=CR, boardName=CR, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=CR, boardName=CR, boardType=Pool, craft=EN), TurnUtilizationFilter(distr=HB, subDistr=CR, boardName=EN, boardType=Extra, craft=EN), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=C2, boardType=Extra, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=CM, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=CM, boardType=Pool, craft=EN), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=E3, boardType=Extra, craft=EN), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=XE, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=CW, boardName=XE, boardType=Pool, craft=EN), TurnUtilizationFilter(distr=HB, subDistr=LE, boardName=VB, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=HB, subDistr=LE, boardName=VB, boardType=Pool, craft=EN), TurnUtilizationFilter(distr=LA, subDistr=FW, boardName=EN, boardType=Extra, craft=EN), TurnUtilizationFilter(distr=LA, subDistr=FW, boardName=FN, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=B8, boardType=Extra, craft=CO), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=CD, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=CD, boardType=Pool, craft=EN), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=E8, boardType=Extra, craft=EN), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=PW, boardType=Pool, craft=CO), TurnUtilizationFilter(distr=PO, subDistr=SC, boardName=PW, boardType=Pool, craft=EN)]