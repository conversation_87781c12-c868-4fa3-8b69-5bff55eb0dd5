ExpWorkRestProf(turns=[ExpWorkRestProfTurn(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-A, turnId=A001, craft=null), ExpWorkRestProfTurn(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-B, turnId=B001, craft=null), ExpWorkRestProfTurn(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-C, turnId=C001, craft=null), ExpWorkRestProfTurn(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-C, turnId=C002, craft=null), ExpWorkRestProfTurn(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-D, turnId=D001, craft=null)], rotations=[ExpWorkRestProfRotation(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-A, ordSeq=1, rotnId=3484, workIntoRestDayHrs=0.0, workIntoRestDayMkupHrs=80.0, futrMkupPerdHrs=80.0, nbrOffDays=2, nbrOnDays=6, useRemainder=false), ExpWorkRestProfRotation(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-B, ordSeq=1, rotnId=3485, workIntoRestDayHrs=0.0, workIntoRestDayMkupHrs=80.0, futrMkupPerdHrs=80.0, nbrOffDays=2, nbrOnDays=6, useRemainder=false), ExpWorkRestProfRotation(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-C, ordSeq=1, rotnId=3488, workIntoRestDayHrs=0.0, workIntoRestDayMkupHrs=80.0, futrMkupPerdHrs=80.0, nbrOffDays=2, nbrOnDays=6, useRemainder=false), ExpWorkRestProfRotation(distr=HB, subDistr=CR, poolName=CO, profName=HBCR CO-D, ordSeq=1, rotnId=3487, workIntoRestDayHrs=0.0, workIntoRestDayMkupHrs=80.0, futrMkupPerdHrs=80.0, nbrOffDays=2, nbrOnDays=6, useRemainder=false)])