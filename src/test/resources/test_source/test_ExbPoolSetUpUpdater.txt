{"planCardedPools": [], "planCrewProfiles": [], "planExbPoolSetUps": [{"suMarkOffRate": 9.15, "tuMarkOffRate": 9.15, "moMarkOffRate": 9.15, "homeOs": "10563", "weMarkOffRate": 9.15, "breakEvenPoint": 3, "subDistr": "SC", "craft": "CO", "description": null, "markOffRate": 9.15, "saMarkOffRate": 9.15, "exbType": 1, "exbPlacementCode": 1, "thMarkOffRate": 9.15, "frMarkOffRate": 9.15, "distr": "PO", "poolName": "B8", "optimizeSize": 1}], "planExbTurnsInPool": [{"turnId": "A002", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "A003", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "B001", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "B002", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "C001", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "C003", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "D002", "subDistr": "SC", "distr": "PO", "poolName": "B8"}, {"turnId": "D003", "subDistr": "SC", "distr": "PO", "poolName": "B8"}], "planExtraboards": [{"poolCraft": "CO", "subDistr": "SC", "poolHomeAway": 0, "exbBoardName": "B8", "distr": "PO", "poolName": "XX"}], "planIdPools": [], "planPoolRotationHistories": [], "planPoolSetUps": [], "planPools": [], "planSelfSustainings": [], "planTieUpExceptions": [], "planTurnsInPool": [], "planWorkRestProfRotns": [], "planWorkRestProfTGrps": [], "planWorkRestProfs": [], "requestInfo": null, "scenarioConfig": {"rule0WorkDays": null, "rule0RestHours": null, "rule0ReturnTime": null, "rule1WorkDays": null, "rule1RestDays": null, "rule1ReturnTime": null, "suMarkOffRate": null, "poolSizingMaxCycles": null, "trainDelayCostHourly": 176.92, "tuMarkOffRate": null, "restHomeM": 600, "poolSizingMinCo": null, "moMarkOffRate": null, "useSystemCost": 1, "exbSizingMaxEn": null, "stopD": null, "systemMarkOffRate": 25.0, "saMarkOffRate": null, "rndDelayPctg": null, "poolSizingMaxCoWeight": null, "servantId": null, "dhCost": 0.0, "maxPctgAssgn": null, "useSystemHome": 0, "maxLimboPerMonth": 30.0, "exbSizingUsesOriginalPoolSizes": null, "exbSizingMaxEnWeight": null, "frMarkOffRate": null, "poolSizingMaxCo": null, "dhTravelM": 120, "poolSizingAverageTrainDelayThreshold": null, "mandatoryDetentionsCo": 12.0, "useSystemAway": 0, "useSystemMarkOff": 0, "exbSizingMaxSeconds": null, "exbSizingDefaultCoBep": null, "weMarkOffRate": null, "poolSizingDirectionDensityThreshold": null, "postArrivalDurationM": 120, "studyEndDate": "2021-06-11", "statsEndDate": null, "addLimboToRest": 0, "undisturbRestAway": 1, "takeVacation": 1, "exbSizingMaxCoWeight": null, "poolSizingExtraboardRateThreshold": null, "dhComboDelayM": 12, "poolSizingMaxSeconds": null, "dhLookAheadM": 1440, "exbSizingMinCo": null, "poolSizingEpsilon": null, "rndOnDutyPctg": null, "exbSizingRandomSeeds": null, "detentionHourlyCost": 28.0, "undisturbRestHome": 1, "exbSizingMaxCo": null, "poolSizingAcceptableTrainCancellationRatio": null, "poolSizingRandomSeeds": null, "poolSizingTrainCancellationRatioThreshold": null, "poolSizingDefaultCoBep": null, "poolSizingMinEn": null, "districts": null, "exbSizingEpsilon": null, "poolSizingAcceptableAverageTrainDelay": null, "poolSizingMaxThreads": null, "useDhCombo": 1, "preferredDetention": 12.0, "poolSizingDefaultEnBep": null, "maxServiceTimePerMonth": 276, "commutePctg": null, "poolSizingAlgorithm": null, "exbSizingMaxThreads": null, "poolSizingMaxEn": null, "restAwayM": 480, "minRemainingHos": 20, "runSegments": null, "thMarkOffRate": null, "logLevel": null, "poolSizingMaxEnWeight": null, "excessiveDetention": 8.0, "mandatoryDetentionsEn": 14.0, "locationUpdate": null, "studyStartDate": "2021-05-15", "statsStartDate": null, "exbSizingMaxCycles": null, "trnMaxDelayM": 2880, "orderM": 1440, "exbSizingMinEn": null, "holdTurnForLongRest": 1, "perEntityLoggingEnabled": null, "poolSizingAcceptableTrainDelayRatio": null, "countDhForLongRest": 0, "useMakeUpTurn": 1, "holdTurnForMonthlyQuota": 0, "maxOperatingM": 720, "startD": null, "dhWhileRestAway": 1, "detailedTrackingEnabled": null, "exbSizingDefaultEnBep": null, "exbSizingNumRuns": null, "poolSizingNumRuns": null, "exbSizingDirectionDensityThreshold": null, "poolSizingDelayedTrainsRatioThreshold": null, "rescueUpdateM": null, "deadheadFlipDisabled": null, "detentionBufferM": null}, "studyTrnInputs": []}