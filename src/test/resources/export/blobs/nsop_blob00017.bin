{"responseInfo": {"warnings": [{"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp LA/FW/FN/4 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=EN\n  crewDestinOs=05268\n  crewOrgnOs=05268\n  distr=LA\n  miles=<null>\n  poolHomeAway=4\n  poolName=FN\n  poolProfileDesc=<null>\n  profileId=LAFW33DH\n  rin=<null>\n  subDistr=FW\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp LA/FW/FN/4 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=CO\n  crewDestinOs=05268\n  crewOrgnOs=05268\n  distr=LA\n  miles=<null>\n  poolHomeAway=4\n  poolName=FN\n  poolProfileDesc=<null>\n  profileId=LAFW33DH\n  rin=<null>\n  subDistr=FW\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp LA/FW/FN/4 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=EN\n  crewDestinOs=05268\n  crewOrgnOs=05268\n  distr=LA\n  miles=<null>\n  poolHomeAway=4\n  poolName=FN\n  poolProfileDesc=<null>\n  profileId=LAFW32FN\n  rin=<null>\n  subDistr=FW\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL_SET_UP", "warningMessage": "The following planCrewProfile references nonexistent planPoolSetUp LA/FW/FN/4 via its distr/subDistr/poolName/poolHomeAway fields and will be ignored: planCrewProfile[\n  craft=CO\n  crewDestinOs=05268\n  crewOrgnOs=05268\n  distr=LA\n  miles=<null>\n  poolHomeAway=4\n  poolName=FN\n  poolProfileDesc=<null>\n  profileId=LAFW32FN\n  rin=<null>\n  subDistr=FW\n  tieupException1=<null>\n  tieupException2=<null>\n  tieupException3=<null>\n  tieupException4=<null>\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL", "warningMessage": "The following planTieUpException references nonexistent planPool LA/CH/FC via its distr/subDistr/fromPoolName fields and will be ignored: planTieUpException[\n  craft=EN\n  description=<null>\n  distr=LA\n  distrSearch1=<null>\n  distrSearch2=LA\n  distrSearch3=<null>\n  distrSearch4=<null>\n  fromDistr=LA\n  fromPoolName=FC\n  fromSubDistr=FW\n  poolNameSearch1=<null>\n  poolNameSearch2=FC\n  poolNameSearch3=<null>\n  poolNameSearch4=<null>\n  subDistr=CH\n  subDistrSearch1=<null>\n  subDistrSearch2=FW\n  subDistrSearch3=<null>\n  subDistrSearch4=<null>\n  toDistr=LA\n  toPoolHomeAway=0\n  toPoolName=FC\n  toSubDistr=FW\n  tueSeqNbr=5\n  tueSubSeqNbr=2\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_PLAN_POOL", "warningMessage": "The following planTieUpException references nonexistent planPool LA/CH/FC via its distr/subDistr/fromPoolName fields and will be ignored: planTieUpException[\n  craft=CO\n  description=<null>\n  distr=LA\n  distrSearch1=<null>\n  distrSearch2=LA\n  distrSearch3=<null>\n  distrSearch4=<null>\n  fromDistr=LA\n  fromPoolName=FC\n  fromSubDistr=FW\n  poolNameSearch1=<null>\n  poolNameSearch2=FC\n  poolNameSearch3=<null>\n  poolNameSearch4=<null>\n  subDistr=CH\n  subDistrSearch1=<null>\n  subDistrSearch2=FW\n  subDistrSearch3=<null>\n  subDistrSearch4=<null>\n  toDistr=LA\n  toPoolHomeAway=0\n  toPoolName=FC\n  toSubDistr=FW\n  tueSeqNbr=5\n  tueSubSeqNbr=2\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_CREW_PROFILE", "warningMessage": "This studyTrnInput has assignFlag=0 and refers to nonexistent planCrewProfile 00371/LAFW33/EN: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=02123\n  fromTs=Sun Jun 13 12:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=LAFW33\n  segTransitTime=114\n  toLocSeqNr=3\n  toOs=00371\n  toTs=Sun Jun 13 13:54:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=64KL013\n  trnCrewDistr=L0\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=UFL\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_CREW_PROFILE", "warningMessage": "This studyTrnInput has assignFlag=0 and refers to nonexistent planCrewProfile 00371/LAFW33/EN: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=02123\n  fromTs=Tue Jun 08 12:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=LAFW33\n  segTransitTime=114\n  toLocSeqNr=3\n  toOs=00371\n  toTs=Tue Jun 08 13:54:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=64KL008\n  trnCrewDistr=L0\n  trnOrgnDt=2021-06-08\n  trnSection=0\n  trnType=UFL\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_CREW_PROFILE", "warningMessage": "This studyTrnInput has assignFlag=0 and refers to nonexistent planCrewProfile 00371/LAFW33/EN: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=02123\n  fromTs=Mon May 31 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=LAFW33\n  segTransitTime=114\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon May 31 16:54:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=64RL031\n  trnCrewDistr=L0\n  trnOrgnDt=2021-05-31\n  trnSection=0\n  trnType=UFL\n]", "type": "VALIDATION"}, {"code": "REFERENCE_TO_UNDEFINED_CREW_PROFILE", "warningMessage": "This studyTrnInput has assignFlag=0 and refers to nonexistent planCrewProfile 00371/LAFW33/EN: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=<null>\n  assignEnXbSD=<null>\n  assignEnXbXB=<null>\n  assignFlag=0\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=02123\n  fromTs=Sun May 23 12:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=LAFW33\n  segTransitTime=114\n  toLocSeqNr=3\n  toOs=00371\n  toTs=Sun May 23 13:54:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=64KL023\n  trnCrewDistr=L0\n  trnOrgnDt=2021-05-23\n  trnSection=0\n  trnType=UFL\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri Jun 18 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=704\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 19 02:44:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD18\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-18\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Thu Jun 17 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=531\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Fri Jun 18 07:51:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN32YD17\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-17\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Wed Jun 16 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=647\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Thu Jun 17 09:47:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD16\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-16\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Tue Jun 15 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=508\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Tue Jun 15 23:28:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD15\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-15\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Mon Jun 14 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=538\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 14 23:28:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD14\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-14\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Mon Jun 14 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=498\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 14 15:18:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD14\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-14\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 13 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=512\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 14 07:32:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN32YD13\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 13 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=542\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 14 08:02:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD13\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 13 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=515\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 13 23:35:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD13\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 13 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=503\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 13 22:53:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD13\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 13 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=542\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 13 16:02:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD13\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-13\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 12 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=547\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 13 08:07:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD12\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-12\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 12 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=542\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 13 00:02:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD12\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-12\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 12 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=545\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 12 23:35:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD12\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-12\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 12 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=533\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 12 15:53:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD12\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-12\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri Jun 11 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=697\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 12 10:37:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD11\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-11\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri Jun 11 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=559\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Fri Jun 11 23:49:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD11\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-11\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri Jun 11 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=499\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Fri Jun 11 15:19:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD11\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-11\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Thu Jun 10 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=497\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Fri Jun 11 07:17:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD10\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-10\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Thu Jun 10 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=506\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Thu Jun 10 23:26:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD10\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-10\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Wed Jun 09 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=502\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Thu Jun 10 07:22:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD09\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-09\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Tue Jun 08 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=530\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Wed Jun 09 07:50:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN32YD08\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-08\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Tue Jun 08 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=542\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Tue Jun 08 23:32:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD08\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-08\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Mon Jun 07 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=568\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 07 23:58:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD07\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-07\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 06 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=555\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon Jun 07 08:15:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD06\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-06\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun Jun 06 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=539\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 06 23:59:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD06\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-06\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 05 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=553\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 06 00:13:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD05\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-05\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 05 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=698\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun Jun 06 02:08:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD05\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-05\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat Jun 05 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=519\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 05 15:39:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD05\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-05\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri Jun 04 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=585\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat Jun 05 00:15:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD04\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-04\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Tue Jun 01 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=606\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Wed Jun 02 00:36:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD01\n  trnCrewDistr=YD\n  trnOrgnDt=2021-06-01\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Mon May 31 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=637\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Tue Jun 01 01:07:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD31\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-31\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun May 30 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=503\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun May 30 23:23:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD30\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-30\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sun May 30 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=654\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Mon May 31 01:24:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD30\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-30\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat May 29 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=594\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sun May 30 08:54:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD29\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-29\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Fri May 28 23:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=533\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat May 29 07:53:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN31YD28\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-28\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Thu May 27 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=523\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Thu May 27 23:43:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN21YD27\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-27\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Tue May 25 14:30:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=542\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Tue May 25 23:32:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD25\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-25\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Mon May 24 15:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=572\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Tue May 25 00:32:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN22YD24\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-24\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}, {"code": "CREW_PROFILE_NOT_USED", "warningMessage": "This studyTrnInput has assignFlag=1 but defines profileId/crewOrgnOs.  The profileId/crewOrgnOs reference null/00371 will be ignored: studyTrnInput[\n  assignCoXbDs=<null>\n  assignCoXbSD=<null>\n  assignCoXbXB=<null>\n  assignEnXbDs=LA\n  assignEnXbSD=FW\n  assignEnXbXB=EN\n  assignFlag=1\n  crewOrgnOs=00371\n  departureDistribution=<null>\n  deptLambda1=<null>\n  deptLower=<null>\n  deptMean1=<null>\n  deptMedium=<null>\n  deptRate=<null>\n  deptScale=<null>\n  deptShape=<null>\n  deptShiftConstant=<null>\n  deptUpper=<null>\n  deptVariance1=<null>\n  fromLocSeqNr=1\n  fromOs=00371\n  fromTs=Sat May 22 07:00:00 EDT 2021\n  initTerminalTime=90\n  profileId=<null>\n  segTransitTime=499\n  toLocSeqNr=2\n  toOs=00371\n  toTs=Sat May 22 15:19:00 EDT 2021\n  transitLambda1=<null>\n  transitLower=<null>\n  transitMean1=<null>\n  transitMedium=<null>\n  transitRate=<null>\n  transitScale=<null>\n  transitShape=<null>\n  transitShiftConstant=<null>\n  transitTimeDistribution=<null>\n  transitUpper=<null>\n  transitVariance1=<null>\n  trn=DN11YD22\n  trnCrewDistr=YD\n  trnOrgnDt=2021-05-22\n  trnSection=0\n  trnType=YDJB\n]", "type": "VALIDATION"}], "requestTs": "2021-05-30T18:10:26+0000", "requestId": "hub_5769_1.00_2021-06-05_2021-06-11", "errorMessage": null, "simulationEndTs": "2021-05-30T18:45:10+0000", "servantId": -1, "simulationStartTs": "2021-05-30T18:45:08+0000", "status": "SUCCESS"}, "boardSummaries": [{"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 50, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "C4"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "C4"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "EN"}, {"nbrOfStarts": 14, "boardType": "Extra", "boardSize": 50, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "EN"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 27, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "FC"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "FC"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 15, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "FE"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "FE"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 28, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "FF"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "FF"}, {"nbrOfStarts": 1, "boardType": "Pool", "boardSize": 36, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "FN"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "FN"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 2, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "WT"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "WT"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "CO", "distr": "LA", "boardName": "WX"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "FW", "craft": "EN", "distr": "LA", "boardName": "WX"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 50, "subDistr": "HD", "craft": "CO", "distr": "LA", "boardName": "B6"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "HD", "craft": "EN", "distr": "LA", "boardName": "B6"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 0, "subDistr": "HD", "craft": "CO", "distr": "LA", "boardName": "E6"}, {"nbrOfStarts": 0, "boardType": "Extra", "boardSize": 50, "subDistr": "HD", "craft": "EN", "distr": "LA", "boardName": "E6"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 16, "subDistr": "HD", "craft": "CO", "distr": "LA", "boardName": "PD"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "HD", "craft": "EN", "distr": "LA", "boardName": "PD"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "HD", "craft": "CO", "distr": "LA", "boardName": "WU"}, {"nbrOfStarts": 0, "boardType": "Pool", "boardSize": 0, "subDistr": "HD", "craft": "EN", "distr": "LA", "boardName": "WU"}], "boardTrainStarts": [{"nbrOfStarts": 14, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "startType": "NORMAL", "distr": "LA", "boardName": "EN"}, {"nbrOfStarts": 1, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "startType": "NORMAL", "distr": "LA", "boardName": "FN"}], "simulationTrainOutputs": [{"crewOs": "00371", "enProfileSubDistr": null, "enOnDutyStartTime": null, "fromTs": "2021-06-08T16:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": null, "enProfilePoolHomeAway": null, "enOnDutyDuration": null, "fromLocSeqNr": 1, "trnType": "UFL", "coProfilePoolName": "FN", "coOnDutyStartTime": "2021-06-08T14:30:00+0000", "coProfileSubDistr": "FW", "coProfilePoolOs": "00371", "fromOs": "02123", "enProfileDistr": null, "coOnDutyDuration": 324.0083333333333, "crewProfileId": "LAFW33", "toTs": "2021-06-08T17:54:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "64KL008", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "L0", "arvlFlag": 1, "coProfileDistr": "LA", "enProfilePoolName": null, "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": null, "enOnDutyStartTime": null, "fromTs": "2021-06-13T16:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": null, "enProfilePoolHomeAway": null, "enOnDutyDuration": null, "fromLocSeqNr": 1, "trnType": "UFL", "coProfilePoolName": "FN", "coOnDutyStartTime": "2021-06-13T14:30:00+0000", "coProfileSubDistr": "FW", "coProfilePoolOs": "00371", "fromOs": "02123", "enProfileDistr": null, "coOnDutyDuration": 324.0083333333333, "crewProfileId": "LAFW33", "toTs": "2021-06-13T17:54:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "64KL013", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "L0", "arvlFlag": 1, "coProfileDistr": "LA", "enProfilePoolName": null, "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": null, "enOnDutyStartTime": null, "fromTs": "2021-05-23T16:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": null, "enProfilePoolHomeAway": null, "enOnDutyDuration": null, "fromLocSeqNr": 1, "trnType": "UFL", "coProfilePoolName": "FN", "coOnDutyStartTime": "2021-05-23T14:30:00+0000", "coProfileSubDistr": "FW", "coProfilePoolOs": "00371", "fromOs": "02123", "enProfileDistr": null, "coOnDutyDuration": 324.0083333333333, "crewProfileId": "LAFW33", "toTs": "2021-05-23T17:54:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "64KL023", "trnOrgnDt": "2021-05-23", "trnCrewDistr": "L0", "arvlFlag": 1, "coProfileDistr": "LA", "enProfilePoolName": null, "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": null, "enOnDutyStartTime": null, "fromTs": "2021-05-31T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": null, "enProfilePoolHomeAway": null, "enOnDutyDuration": null, "fromLocSeqNr": 1, "trnType": "UFL", "coProfilePoolName": "FN", "coOnDutyStartTime": "2021-05-31T17:30:00+0000", "coProfileSubDistr": "FW", "coProfilePoolOs": "00371", "fromOs": "02123", "enProfileDistr": null, "coOnDutyDuration": 324.0083333333333, "crewProfileId": "LAFW33", "toTs": "2021-05-31T20:54:00+0000", "coProfilePoolHomeAway": 0, "recrewDuration": 0.0, "trn": "64RL031", "trnOrgnDt": "2021-05-31", "trnCrewDistr": "L0", "arvlFlag": 1, "coProfileDistr": "LA", "enProfilePoolName": null, "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-05T09:30:00+0000", "fromTs": "2021-06-05T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 729.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-05T19:39:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD05", "trnOrgnDt": "2021-06-05", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-11T09:30:00+0000", "fromTs": "2021-06-11T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 709.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-11T19:19:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD11", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-12T09:30:00+0000", "fromTs": "2021-06-12T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 743.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-12T19:53:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD12", "trnOrgnDt": "2021-06-12", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-13T09:30:00+0000", "fromTs": "2021-06-13T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 752.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-13T20:02:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD13", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-14T09:30:00+0000", "fromTs": "2021-06-14T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 708.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-14T19:18:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD14", "trnOrgnDt": "2021-06-14", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-22T09:30:00+0000", "fromTs": "2021-05-22T11:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 709.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-22T19:19:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN11YD22", "trnOrgnDt": "2021-05-22", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-05T17:30:00+0000", "fromTs": "2021-06-05T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 763.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-06T04:13:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD05", "trnOrgnDt": "2021-06-05", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-06T17:30:00+0000", "fromTs": "2021-06-06T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 749.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-07T03:59:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD06", "trnOrgnDt": "2021-06-06", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-10T17:30:00+0000", "fromTs": "2021-06-10T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 716.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-11T03:26:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD10", "trnOrgnDt": "2021-06-10", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-12T17:30:00+0000", "fromTs": "2021-06-12T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 752.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-13T04:02:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD12", "trnOrgnDt": "2021-06-12", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-13T17:30:00+0000", "fromTs": "2021-06-13T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 725.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-14T03:35:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD13", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-15T17:30:00+0000", "fromTs": "2021-06-15T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 718.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-16T03:28:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD15", "trnOrgnDt": "2021-06-15", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-18T17:30:00+0000", "fromTs": "2021-06-18T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 914.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-19T06:44:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD18", "trnOrgnDt": "2021-06-18", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-27T17:30:00+0000", "fromTs": "2021-05-27T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 733.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-28T03:43:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD27", "trnOrgnDt": "2021-05-27", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-30T17:30:00+0000", "fromTs": "2021-05-30T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 713.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-31T03:23:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN21YD30", "trnOrgnDt": "2021-05-30", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-01T17:00:00+0000", "fromTs": "2021-06-01T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 816.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-02T04:36:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD01", "trnOrgnDt": "2021-06-01", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-04T17:00:00+0000", "fromTs": "2021-06-04T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 795.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-05T04:15:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD04", "trnOrgnDt": "2021-06-04", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-05T17:00:00+0000", "fromTs": "2021-06-05T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 908.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-06T06:08:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD05", "trnOrgnDt": "2021-06-05", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-07T17:00:00+0000", "fromTs": "2021-06-07T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 778.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-08T03:58:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD07", "trnOrgnDt": "2021-06-07", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-08T17:00:00+0000", "fromTs": "2021-06-08T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 752.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-09T03:32:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD08", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-11T17:00:00+0000", "fromTs": "2021-06-11T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 769.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-12T03:49:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD11", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-12T17:00:00+0000", "fromTs": "2021-06-12T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 755.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-13T03:35:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD12", "trnOrgnDt": "2021-06-12", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-13T17:00:00+0000", "fromTs": "2021-06-13T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 713.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-14T02:53:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD13", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-14T17:00:00+0000", "fromTs": "2021-06-14T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 748.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-15T03:28:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD14", "trnOrgnDt": "2021-06-14", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-24T17:30:00+0000", "fromTs": "2021-05-24T19:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 782.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-25T04:32:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD24", "trnOrgnDt": "2021-05-24", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-25T17:00:00+0000", "fromTs": "2021-05-25T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 752.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-26T03:32:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD25", "trnOrgnDt": "2021-05-25", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-30T17:00:00+0000", "fromTs": "2021-05-30T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 864.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-31T05:24:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD30", "trnOrgnDt": "2021-05-30", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-31T17:00:00+0000", "fromTs": "2021-05-31T18:30:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 847.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-01T05:07:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN22YD31", "trnOrgnDt": "2021-05-31", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-07T01:30:00+0000", "fromTs": "2021-06-07T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 765.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-07T12:15:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD06", "trnOrgnDt": "2021-06-06", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-10T01:30:00+0000", "fromTs": "2021-06-10T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 712.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-10T11:22:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD09", "trnOrgnDt": "2021-06-09", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-11T01:30:00+0000", "fromTs": "2021-06-11T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 707.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-11T11:17:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD10", "trnOrgnDt": "2021-06-10", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-12T01:30:00+0000", "fromTs": "2021-06-12T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 907.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-12T14:37:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD11", "trnOrgnDt": "2021-06-11", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-13T01:30:00+0000", "fromTs": "2021-06-13T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 757.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-13T12:07:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD12", "trnOrgnDt": "2021-06-12", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-14T01:30:00+0000", "fromTs": "2021-06-14T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 752.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-14T12:02:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD13", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-17T01:30:00+0000", "fromTs": "2021-06-17T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 857.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-17T13:47:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD16", "trnOrgnDt": "2021-06-16", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-29T01:30:00+0000", "fromTs": "2021-05-29T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 743.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-29T11:53:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD28", "trnOrgnDt": "2021-05-28", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-05-30T01:30:00+0000", "fromTs": "2021-05-30T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 804.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-05-30T12:54:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN31YD29", "trnOrgnDt": "2021-05-29", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-09T01:30:00+0000", "fromTs": "2021-06-09T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 740.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-09T11:50:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN32YD08", "trnOrgnDt": "2021-06-08", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-14T01:30:00+0000", "fromTs": "2021-06-14T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 722.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-14T11:32:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN32YD13", "trnOrgnDt": "2021-06-13", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}, {"crewOs": "00371", "enProfileSubDistr": "FW", "enOnDutyStartTime": "2021-06-18T01:30:00+0000", "fromTs": "2021-06-18T03:00:00+0000", "toOs": "00371", "delayDueToCrewDuration": 0.0, "enProfilePoolOs": "00371", "enProfilePoolHomeAway": 0, "enOnDutyDuration": 741.0083333333333, "fromLocSeqNr": 1, "trnType": "YDJB", "coProfilePoolName": null, "coOnDutyStartTime": null, "coProfileSubDistr": null, "coProfilePoolOs": null, "fromOs": "00371", "enProfileDistr": "LA", "coOnDutyDuration": null, "crewProfileId": "", "toTs": "2021-06-18T11:51:00+0000", "coProfilePoolHomeAway": null, "recrewDuration": 0.0, "trn": "DN32YD17", "trnOrgnDt": "2021-06-17", "trnCrewDistr": "YD", "arvlFlag": 1, "coProfileDistr": null, "enProfilePoolName": "", "trnSection": "0", "status": "ARRIVED"}], "turnUtilizations": [{"turnId": "~100", "duration": 110.26, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~101", "duration": 42.84, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~101", "duration": 96.0, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~101", "duration": 39.45, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~102", "duration": 39.77, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~102", "duration": 63.39, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~102", "duration": 67.95, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~103", "duration": 40.08, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~103", "duration": 50.25, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~103", "duration": 68.77, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~104", "duration": 25.47, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~104", "duration": 48.0, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~104", "duration": 58.16, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~105", "duration": 30.81, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~105", "duration": 40.87, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~105", "duration": 57.19, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~106", "duration": 24.2, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~106", "duration": 24.89, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~106", "duration": 39.38, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~107", "duration": 21.76, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~107", "duration": 23.97, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~107", "duration": 12.03, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~108", "duration": 11.82, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~108", "duration": 3.72, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~108", "duration": 4.18, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~109", "duration": 20.73, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~109", "duration": 7.02, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~110", "duration": 11.783472222222223, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "ON_ASSIGNMENT"}, {"turnId": "~110", "duration": 3.716527777777778, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "RESTING"}, {"turnId": "~110", "duration": 3.716527777777778, "boardType": "Extra", "subDistr": "FW", "craft": "EN", "ondutyFlag": "N", "distr": "LA", "boardName": "EN", "status": "WAITING"}, {"turnId": "~071", "duration": 120.0, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "N", "distr": "LA", "boardName": "FN", "status": "VACANT"}, {"turnId": "~072", "duration": 1.6, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "N", "distr": "LA", "boardName": "FN", "status": "ASSIGNED"}, {"turnId": "~072", "duration": 2.0, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "Y", "distr": "LA", "boardName": "FN", "status": "DOING_POST_ARRIVAL_PROCEDURE"}, {"turnId": "~072", "duration": 1.5, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "Y", "distr": "LA", "boardName": "FN", "status": "DOING_PRE_DEPARTURE_PROCEDURE"}, {"turnId": "~072", "duration": 0.4, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "N", "distr": "LA", "boardName": "FN", "status": "LEFT_FOR_WORK"}, {"turnId": "~072", "duration": 1.9, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "Y", "distr": "LA", "boardName": "FN", "status": "OPERATING"}, {"turnId": "~072", "duration": 0.0001388888888888889, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "Y", "distr": "LA", "boardName": "FN", "status": "READY_TO_DEPART"}, {"turnId": "~072", "duration": 10.0, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "N", "distr": "LA", "boardName": "FN", "status": "RESTING"}, {"turnId": "~072", "duration": 24.0, "boardType": "Pool", "subDistr": "FW", "craft": "CO", "ondutyFlag": "N", "distr": "LA", "boardName": "FN", "status": "VACANT"}]}